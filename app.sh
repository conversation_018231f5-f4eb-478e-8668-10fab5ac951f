#!/bin/bash
# 定义日志文件路径
LOG_FILE="output.log"

# 清空旧日志文件
# > "$LOG_FILE"

echo "正在启动 OTA 设备管理系统（后台模式）…"
echo "日志输出在 $LOG_FILE"

# 设置环境变量
export FLASK_ENV=production
export DATABASE_URL="postgresql://kafanglinlin:7jbWNHYZZLMa@localhost:5432/KafangCharging"

# 切换到脚本所在目录
cd "$(dirname "$0")"

# 启动应用（生产环境配置：host=0.0.0.0, port=5000, debug=False）
nohup python3 -OO app.py --host 0.0.0.0 --port 5000 --no-debug > "$LOG_FILE" 2>&1 &

# 获取进程ID
PID=$!
echo "服务已启动，进程ID为 $PID"
echo "已启动，日志输出在 $LOG_FILE"
echo "服务地址: http://0.0.0.0:5000"
echo "调试模式: 关闭"