{"forwarder_config": {"enabled": true, "log_level": "INFO", "log_file": "logs/mqtt_forwarder.log", "config_check_interval": 3, "description": "MQTT消息转发服务配置 - 已更新"}, "platform_mapping": {"alicloud": {"product_key": "hs7eigK8Xvl", "platform_type": "ALIBABA_CLOUD"}, "emqx": {"product_key": "wxd48e69e833621cfd", "platform_type": "EMQX"}}, "target_devices": [], "forwarding_rules": [{"name": "alicloud_to_emqx_uplink", "description": "阿里云设备上行消息转发到EMQX", "source_platform": "alicloud", "target_platform": "emqx", "topic_pattern": "/hs7eigK8Xvl/{device_id}/user/update", "target_topic_pattern": "/wxd48e69e833621cfd/{device_id}/user/update", "enabled": true}, {"name": "emqx_to_alicloud_downlink", "description": "EMQX服务器下行消息转发到阿里云", "source_platform": "emqx", "target_platform": "alicloud", "topic_pattern": "/wxd48e69e833621cfd/{device_id}/user/get", "target_topic_pattern": "/hs7eigK8Xvl/{device_id}/user/get", "enabled": true}]}