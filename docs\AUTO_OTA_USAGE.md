# 自动OTA升级功能使用说明

## 概述

自动OTA升级功能是一个智能化的固件升级服务，当中控设备发送固件升级请求（B2_BMD_REQ_FIRMWARE_UPDAGRADE消息）时，系统会自动检测设备类型并选择对应的最新固件进行升级。

## 功能特点

### 🚀 核心功能
- **自动检测**: 当收到中控的固件升级请求时自动触发
- **智能匹配**: 根据设备类型自动选择对应的最新固件
- **版本比较**: 只有当设备当前版本低于最新固件版本时才执行升级
- **开关控制**: 支持启用/禁用自动升级功能

### 🛡️ 安全特性
- **超时控制**: 可配置升级任务的超时时间
- **重试机制**: 支持升级失败时的自动重试
- **强制更新**: 可选择是否强制执行固件更新
- **线程安全**: 使用独立线程处理升级请求，不阻塞消息处理

## 工作流程

```mermaid
graph TD
    A[中控发送固件升级请求] --> B[解析请求消息]
    B --> C{自动升级已启用?}
    C -->|否| D[忽略请求]
    C -->|是| E[获取设备固件信息]
    E --> F[更新设备类型到数据库]
    F --> G[查找设备类型对应的最新固件]
    G --> H{找到最新固件?}
    H -->|否| I[记录日志：无最新固件]
    H -->|是| J[比较固件版本]
    J --> K{需要升级?}
    K -->|否| L[记录日志：已是最新版本]
    K -->|是| M[创建并启动OTA升级任务]
    M --> N[升级完成]
```

## 配置说明

### 环境变量配置

在 `config.py` 中可以通过环境变量配置默认参数：

```bash
# 自动OTA升级配置
export AUTO_OTA_ENABLED=true              # 是否启用自动升级
export AUTO_OTA_FORCE_UPDATE=true         # 是否强制更新
export AUTO_OTA_TIMEOUT=300               # 升级超时时间（秒）
export AUTO_OTA_MAX_RETRIES=3             # 最大重试次数
```

### 运行时配置

可以通过Web界面或API动态修改配置：

```python
from services.auto_ota_service import configure_auto_ota_service

# 更新配置
configure_auto_ota_service(
    enabled=True,           # 启用自动升级
    force_update=True,      # 强制更新
    timeout=300,           # 超时时间（秒）
    max_retries=3          # 最大重试次数
)
```

## 使用方法

### 1. Web界面管理

访问 `/auto_ota` 页面进行可视化管理：

- **服务状态**: 查看服务运行状态和当前配置
- **配置设置**: 修改自动升级相关参数
- **功能说明**: 查看详细的功能介绍

### 2. API接口

#### 获取配置
```http
GET /api/auto_ota/config
```

#### 更新配置
```http
POST /api/auto_ota/config
Content-Type: application/json

{
    "enabled": true,
    "force_update": true,
    "timeout": 300,
    "max_retries": 3
}
```

#### 获取状态
```http
GET /api/auto_ota/status
```

### 3. 程序化调用

```python
from services.auto_ota_service import auto_ota_service

# 获取当前配置
config = auto_ota_service.get_config()
print(f"自动升级已启用: {config.enabled}")

# 修改配置
auto_ota_service.set_config(enabled=False)
```

## 前置条件

### 1. 固件管理
- 需要在固件管理页面上传对应设备类型的固件文件
- 需要在最新固件管理页面设置各设备类型的最新固件

### 2. 设备类型支持
目前支持的设备类型：
- **10**: V2 (旧版霍尔传感器版本，黑色PCB)
- **50**: V5 (新版BL0910 10通道版本)
- **51**: V51 (新版BL0939 2通道版本)

### 3. IoT客户端
- 确保IoT客户端服务已启动并正常运行
- 确保设备能够正常发送和接收MQTT消息

## 监控和日志

### 日志记录
系统会记录以下关键事件：
- 收到固件升级请求
- 设备类型检测和更新
- 固件版本比较结果
- OTA任务创建和执行状态
- 配置变更记录

### 状态监控
可以通过以下方式监控服务状态：
- Web界面的状态显示
- API接口查询
- 日志文件分析

## 故障排除

### 常见问题

1. **自动升级不触发**
   - 检查自动升级功能是否已启用
   - 确认IoT客户端是否正常运行
   - 查看日志确认是否收到升级请求消息

2. **找不到最新固件**
   - 检查是否为对应设备类型设置了最新固件
   - 确认固件文件是否存在且可访问

3. **版本比较失败**
   - 检查固件文件格式是否正确
   - 确认固件文件的版本信息是否正确写入

4. **OTA任务创建失败**
   - 检查设备是否在数据库中存在
   - 确认OTA服务是否正常运行
   - 查看详细错误日志

### 调试方法

1. **启用详细日志**
   ```python
   import logging
   logging.getLogger('services.auto_ota_service').setLevel(logging.DEBUG)
   ```

2. **手动测试**
   ```python
   # 运行测试脚本
   python test_auto_ota.py
   ```

3. **检查配置**
   ```python
   from services.auto_ota_service import get_auto_ota_config
   print(get_auto_ota_config())
   ```

## 注意事项

1. **性能影响**: 自动升级会消耗系统资源，建议在业务低峰期启用
2. **网络稳定性**: 确保设备网络连接稳定，避免升级过程中断
3. **固件兼容性**: 确保上传的固件与设备硬件版本兼容
4. **备份策略**: 建议在大规模自动升级前进行小范围测试

## 更新历史

- **v1.0.0**: 初始版本，支持基本的自动OTA升级功能
- 支持设备类型自动检测和固件匹配
- 支持配置管理和状态监控
- 支持Web界面和API接口
