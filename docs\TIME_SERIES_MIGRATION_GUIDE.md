# 时序数据库字段迁移操作文档

## 概述

本文档详细说明如何使用已开发的脚本为生产环境和调试环境数据库添加新的时序数据字段。迁移过程包括字段类型优化、索引重建和数据结构升级。

## 迁移脚本说明

### 可用的迁移脚本

1. **字段迁移脚本**：`sql_tools/migrate_time_series_fields.py`
   - 用途：修改现有字段类型（如VARCHAR到TEXT）
   - 适用场景：小规模字段调整

2. **优化部署脚本**：`sql_tools/deploy_optimized_time_series.py`
   - 用途：完整的时序数据库重构
   - 适用场景：大规模性能优化

3. **架构设计脚本**：`sql_tools/optimized_time_series_schema.py`
   - 用途：生成优化的数据库架构SQL
   - 适用场景：架构规划和文档生成

## 环境区别说明

### 开发环境 (Development)
- **表前缀**：`dev_`
- **表名示例**：`dev_time_series_data`, `dev_time_series_buffer`
- **环境变量**：`FLASK_ENV=development`
- **特点**：可以安全地删除和重建表

### 生产环境 (Production)
- **表前缀**：`prod_`
- **表名示例**：`prod_time_series_data`, `prod_time_series_buffer`
- **环境变量**：`FLASK_ENV=production`
- **特点**：需要谨慎操作，必须保证数据安全

## 迁移前准备工作

### 1. 数据备份建议

#### 开发环境备份
```bash
# 备份开发环境时序数据
pg_dump -h localhost -U username -d database_name \
  --table=dev_time_series_data \
  --table=dev_time_series_batch \
  --data-only \
  --file=dev_time_series_backup_$(date +%Y%m%d_%H%M%S).sql
```

#### 生产环境备份
```bash
# 完整备份生产环境数据库
pg_dump -h localhost -U username -d database_name \
  --file=prod_full_backup_$(date +%Y%m%d_%H%M%S).sql

# 仅备份时序数据表
pg_dump -h localhost -U username -d database_name \
  --table=prod_time_series_data \
  --table=prod_time_series_batch \
  --file=prod_time_series_backup_$(date +%Y%m%d_%H%M%S).sql
```

### 2. 系统状态检查

#### 检查当前表结构
```sql
-- 查看当前时序数据表结构
\d+ dev_time_series_data;
\d+ prod_time_series_data;

-- 检查表大小
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE tablename LIKE '%time_series%';

-- 检查数据量
SELECT 
    'dev_time_series_data' as table_name,
    COUNT(*) as record_count
FROM dev_time_series_data
UNION ALL
SELECT 
    'prod_time_series_data' as table_name,
    COUNT(*) as record_count
FROM prod_time_series_data;
```

#### 检查运行中的服务
```bash
# 检查是否有正在运行的调试脚本
ps aux | grep python | grep debug

# 检查数据库连接
netstat -an | grep :5432
```

## 迁移操作步骤

### 方案A：字段类型迁移（推荐用于生产环境）

#### 步骤1：停止相关服务
```bash
# 停止Web应用
sudo systemctl stop your_web_app

# 停止所有调试脚本
pkill -f "debug_script"
```

#### 步骤2：执行字段迁移
```bash
# 进入项目目录
cd /path/to/your/project

# 激活虚拟环境
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 执行字段迁移脚本
python sql_tools/migrate_time_series_fields.py
```

#### 步骤3：验证迁移结果
```sql
-- 检查字段类型是否已更改
SELECT column_name, data_type, character_maximum_length 
FROM information_schema.columns 
WHERE table_name IN ('dev_time_series_data', 'prod_time_series_data')
AND column_name = 'string_value';
```

#### 步骤4：重启服务
```bash
# 重启Web应用
sudo systemctl start your_web_app

# 检查服务状态
sudo systemctl status your_web_app
```

### 方案B：完整优化迁移（推荐用于开发环境）

#### 步骤1：选择环境并确认
```bash
# 运行优化部署脚本
python sql_tools/deploy_optimized_time_series.py

# 按提示选择环境
# development - 开发环境
# production - 生产环境

# 确认操作（输入 yes）
```

#### 步骤2：监控部署过程
脚本会自动执行以下步骤：
1. 删除旧的时序数据表
2. 创建优化的时序数据表
3. 创建分区表
4. 创建优化索引
5. 创建维护函数
6. 验证部署结果

#### 步骤3：验证部署结果
```sql
-- 检查新表是否创建成功
SELECT tablename FROM pg_tables 
WHERE tablename LIKE '%time_series%' 
ORDER BY tablename;

-- 检查分区是否创建
SELECT schemaname, tablename 
FROM pg_tables 
WHERE tablename LIKE '%time_series_data_%' 
ORDER BY tablename;

-- 检查索引是否创建
SELECT indexname, tablename 
FROM pg_indexes 
WHERE tablename LIKE '%time_series%' 
ORDER BY tablename, indexname;
```

## 迁移后验证方法

### 1. 功能验证

#### 测试数据写入
```python
# 运行测试脚本
python -c "
from services.time_series_service import time_series_service
import datetime

# 测试写入
success = time_series_service.write_sensor_data(
    device_id='test_migration',
    voltage=220.5,
    temperature=25.3,
    relay_state='测试迁移后的长字符串数据' * 10
)

print(f'写入测试结果: {success}')
"
```

#### 测试数据查询
```python
# 测试查询功能
python -c "
from services.time_series_service import time_series_service
from datetime import datetime, timedelta

# 测试查询
end_time = datetime.now()
start_time = end_time - timedelta(hours=1)

data = time_series_service.query_voltage_data('test_migration', start_time, end_time)
print(f'查询测试结果: {len(data)} 条记录')
"
```

### 2. 性能验证

#### 运行性能测试
```bash
# 运行性能测试脚本
python test_optimized_performance.py
```

#### 检查数据库性能
```sql
-- 检查查询执行计划
EXPLAIN ANALYZE 
SELECT * FROM dev_time_series_data 
WHERE device_id = 'test_device' 
AND timestamp >= NOW() - INTERVAL '1 hour';

-- 检查索引使用情况
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes 
WHERE tablename LIKE '%time_series%'
ORDER BY idx_scan DESC;
```

## 常见问题和解决方案

### 问题1：字段迁移失败
**症状**：执行迁移脚本时报错"column does not exist"

**解决方案**：
```sql
-- 检查表是否存在
SELECT tablename FROM pg_tables WHERE tablename LIKE '%time_series%';

-- 如果表不存在，先创建基础表
CREATE TABLE IF NOT EXISTS dev_time_series_data (
    id BIGSERIAL PRIMARY KEY,
    device_id VARCHAR(50),
    timestamp TIMESTAMP,
    data_type VARCHAR(50),
    string_value VARCHAR(255)  -- 旧字段类型
);
```

### 问题2：权限不足
**症状**：执行SQL时报错"permission denied"

**解决方案**：
```sql
-- 检查当前用户权限
SELECT current_user, session_user;

-- 授予必要权限（需要超级用户执行）
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO your_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO your_user;
```

### 问题3：分区创建失败
**症状**：分区表创建时报错

**解决方案**：
```sql
-- 检查PostgreSQL版本（分区需要10+）
SELECT version();

-- 手动创建分区（如果自动创建失败）
CREATE TABLE dev_time_series_data_2025_08 PARTITION OF dev_time_series_data
FOR VALUES FROM ('2025-08-01') TO ('2025-09-01');
```

### 问题4：数据丢失
**症状**：迁移后数据查询为空

**解决方案**：
```sql
-- 检查数据是否在备份中
\i /path/to/backup.sql

-- 检查分区数据分布
SELECT 
    schemaname,
    tablename,
    n_tup_ins,
    n_tup_upd,
    n_tup_del
FROM pg_stat_user_tables 
WHERE tablename LIKE '%time_series%';
```

## 回滚步骤

### 方案A回滚（字段类型迁移）

#### 步骤1：恢复字段类型
```sql
-- 将TEXT字段改回VARCHAR(255)
ALTER TABLE dev_time_series_data 
ALTER COLUMN string_value TYPE VARCHAR(255);

ALTER TABLE prod_time_series_data 
ALTER COLUMN string_value TYPE VARCHAR(255);
```

#### 步骤2：验证回滚
```sql
-- 检查字段类型
SELECT column_name, data_type, character_maximum_length 
FROM information_schema.columns 
WHERE table_name LIKE '%time_series_data'
AND column_name = 'string_value';
```

### 方案B回滚（完整优化迁移）

#### 步骤1：恢复备份数据
```bash
# 恢复完整备份
psql -h localhost -U username -d database_name < backup_file.sql
```

#### 步骤2：重建原始表结构
```sql
-- 删除优化后的表
DROP TABLE IF EXISTS dev_time_series_data CASCADE;
DROP TABLE IF EXISTS dev_time_series_buffer CASCADE;

-- 重建原始表结构
CREATE TABLE dev_time_series_data (
    id BIGSERIAL PRIMARY KEY,
    device_id VARCHAR(50) NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    data_type VARCHAR(50) NOT NULL,
    data_value JSON,
    numeric_value FLOAT,
    string_value VARCHAR(255),
    created_at TIMESTAMP DEFAULT NOW()
);

-- 重建索引
CREATE INDEX idx_dev_device_timestamp ON dev_time_series_data(device_id, timestamp);
CREATE INDEX idx_dev_device_type_timestamp ON dev_time_series_data(device_id, data_type, timestamp);
```

## 迁移完成检查清单

- [ ] 数据备份已完成
- [ ] 迁移脚本执行成功
- [ ] 新表结构验证通过
- [ ] 索引创建完成
- [ ] 数据写入测试通过
- [ ] 数据查询测试通过
- [ ] 性能测试通过
- [ ] 应用服务重启成功
- [ ] 监控指标正常
- [ ] 备份文件已保存

## 联系支持

如果在迁移过程中遇到问题，请：

1. 保存错误日志和截图
2. 记录执行的具体步骤
3. 检查系统资源使用情况
4. 联系技术支持团队

**注意**：生产环境迁移建议在业务低峰期进行，并提前通知相关用户。
