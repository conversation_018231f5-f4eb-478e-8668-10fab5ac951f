# Flask-Migrate 数据库迁移工具使用指南

## 1. 简介

Flask-Migrate 是 Flask 的一个扩展，基于 Alembic（SQLAlchemy 的数据库迁移工具）构建。它的主要作用是帮助我们管理数据库的版本变更，类似于代码版本控制，可以追踪、管理数据库结构的变化。

## 2. 主要功能和原理

### 2.1 版本控制
- 每次数据库结构变更都会生成一个新的迁移版本
- 迁移版本以 Python 文件形式存储在 `migrations/versions` 目录下
- 每个版本文件包含 `upgrade()` 和 `downgrade()` 两个函数，分别用于升级和回滚

### 2.2 自动检测变更
- 通过比较模型定义和数据库当前状态来检测变更
- 可以自动生成迁移脚本，包含必要的 SQL 语句

### 2.3 迁移历史记录
- 在数据库中维护一个 `alembic_version` 表
- 记录当前数据库使用的迁移版本
- 确保迁移操作的顺序性和一致性

## 3. 常用命令

```bash
# 初始化迁移环境
flask db init

# 创建新的迁移版本
flask db migrate -m "描述信息"

# 升级到最新版本
flask db upgrade

# 降级到上一个版本
flask db downgrade

# 查看迁移历史
flask db history

# 查看当前版本
flask db current
```

## 4. 实际使用示例

### 4.1 初始化项目
```python
from flask_migrate import Migrate

# 创建 Migrate 实例
migrate = Migrate(app, db)
```

### 4.2 修改模型
```python
class Firmware(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100))
    # 添加新字段
    crc32 = db.Column(db.String(8), nullable=False)
```

### 4.3 生成迁移脚本
```bash
flask db migrate -m "add crc32 field"
```

生成的迁移文件示例：
```python
def upgrade():
    op.add_column('firmware', sa.Column('crc32', sa.String(8), nullable=False))

def downgrade():
    op.drop_column('firmware', 'crc32')
```

### 4.4 应用迁移
```bash
flask db upgrade
```

## 5. 最佳实践

### 5.1 版本控制
- 将迁移文件纳入版本控制系统
- 确保团队成员之间迁移版本一致

### 5.2 迁移说明
- 为每个迁移版本添加清晰的说明
- 说明中包含主要变更内容

### 5.3 测试迁移
- 在应用到生产环境前先在测试环境验证
- 确保 `upgrade` 和 `downgrade` 都能正常工作

### 5.4 处理数据
```python
def upgrade():
    # 添加新列
    op.add_column('firmware', sa.Column('crc32', sa.String(8)))
    
    # 更新现有数据
    connection = op.get_bind()
    connection.execute(
        "UPDATE firmware SET crc32 = '00000000'"
    )
```

## 6. 注意事项

### 6.1 不可逆操作
- 某些操作（如删除数据）是不可逆的
- 在执行前要仔细确认

### 6.2 数据备份
- 在执行重要迁移前备份数据库
- 特别是在生产环境中

### 6.3 依赖关系
- 注意表之间的依赖关系
- 按正确的顺序执行迁移

### 6.4 性能影响
- 大型迁移可能影响数据库性能
- 考虑在低峰期执行迁移

## 7. 实际应用场景

### 7.1 添加新字段
```python
def upgrade():
    op.add_column('table_name', sa.Column('new_field', sa.String(50)))
```

### 7.2 修改字段属性
```python
def upgrade():
    op.alter_column('table_name', 'column_name',
                    type_=sa.String(100),
                    existing_type=sa.String(50))
```

### 7.3 创建索引
```python
def upgrade():
    op.create_index('idx_name', 'table_name', ['column_name'])
```

## 8. 总结

通过使用 Flask-Migrate，我们可以：
- 轻松管理数据库结构的变更
- 保持开发和生产环境的数据库结构一致
- 在需要时回滚到之前的版本
- 追踪数据库结构的演进历史

这对于团队协作和项目维护都非常重要。在本项目中，我们使用 Flask-Migrate 来管理所有的数据库结构变更，确保数据库变更可追踪、可控制。 