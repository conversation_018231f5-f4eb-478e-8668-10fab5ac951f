# 数据库迁移示例

本文档记录了项目中的具体数据库迁移示例，可以作为实际操作的参考。

## 示例1：添加固件CRC32字段

### 背景
在固件管理功能中，需要添加CRC32校验值字段，用于验证固件完整性。原始数据库中没有这个字段，需要通过迁移来添加。

### 步骤1：安装迁移工具
```bash
pip install Flask-Migrate
```

### 步骤2：在应用中初始化迁移
在 `app.py` 中添加：
```python
from flask_migrate import Migrate

# 创建 Migrate 实例
migrate = Migrate(app, db)
```

### 步骤3：修改数据模型
在 `Firmware` 模型中添加新字段：
```python
class Firmware(db.Model):
    """固件模型"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    version = db.Column(db.String(20), nullable=False)
    file_path = db.Column(db.String(255), nullable=False)
    size = db.Column(db.Integer, nullable=False)  # 文件大小（字节）
    crc32 = db.Column(db.String(8), nullable=False)  # CRC32校验值
    description = db.Column(db.Text)
    upload_time = db.Column(db.DateTime, default=datetime.now)
    download_count = db.Column(db.Integer, default=0)
```

### 步骤4：初始化迁移环境
```bash
flask db init
```
这个命令会创建 `migrations` 目录及其相关文件。

### 步骤5：创建迁移脚本
```bash
flask db migrate -m "add crc32 field to firmware table"
```
生成的迁移文件 `migrations/versions/94909021d440_add_crc32_field_to_firmware_table.py`：
```python
"""add crc32 field to firmware table

Revision ID: 94909021d440
Revises: 
Create Date: 2025-04-17 07:44:49.993173

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '94909021d440'
down_revision = None
branch_labels = None
depends_on = None

def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('firmware', schema=None) as batch_op:
        batch_op.add_column(sa.Column('crc32', sa.String(length=8), nullable=False, server_default='00000000'))
    # ### end Alembic commands ###

def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('firmware', schema=None) as batch_op:
        batch_op.drop_column('crc32')
    # ### end Alembic commands ###
```

### 步骤6：应用迁移
```bash
flask db upgrade
```

### 遇到的问题和解决方案

1. **问题**：添加非空字段时出错
```
sqlalchemy.exc.OperationalError: (sqlite3.OperationalError) Cannot add a NOT NULL column with default value NULL
```

2. **解决方案**：在迁移脚本中添加默认值
```python
batch_op.add_column(sa.Column('crc32', sa.String(length=8), nullable=False, server_default='00000000'))
```

### 验证迁移结果

1. 检查数据库表结构：
```sql
.schema firmware
```

2. 检查现有数据：
```sql
SELECT id, name, crc32 FROM firmware;
```

### 回滚方案（如果需要）
```bash
flask db downgrade
```

## 经验总结

1. **添加非空字段**
   - 必须提供默认值
   - 使用 `server_default` 参数设置默认值

2. **数据迁移**
   - 对于现有数据，需要设置合适的默认值
   - 考虑数据一致性和业务逻辑

3. **迁移测试**
   - 在开发环境先测试迁移
   - 确保 `upgrade` 和 `downgrade` 都能正常工作

4. **版本控制**
   - 将迁移文件纳入版本控制
   - 保持迁移历史的完整性 