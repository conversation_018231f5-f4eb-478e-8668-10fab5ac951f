# OTA数据库迁移指南

## 概述

本指南提供了完整的OTA数据库迁移步骤，用于解决并行OTA升级系统中的数据库结构不匹配问题。

## 问题描述

在实现并行OTA升级系统时，为`ota_task`表添加了以下新字段：
- `detailed_status` - 详细状态信息
- `stage_info` - 阶段信息
- `retry_count` - 重试次数
- `max_retries` - 最大重试次数
- `started_at` - 开始时间
- `completed_at` - 完成时间

但PostgreSQL数据库表结构未相应更新，导致应用无法正常运行。

## 🚀 快速解决步骤

### 1. 检查兼容性
```bash
python sql_tools/check_ota_compatibility.py debug
```

### 2. 执行测试环境迁移
在DBeaver中执行：`sql_tools/ota_task_migration.sql`（测试环境部分）

### 3. 验证迁移结果
```bash
python tests/test_database_migration.py
```

### 4. 生产环境迁移
确认测试环境正常后，执行生产环境迁移

## 详细步骤

### 步骤1: 兼容性检查

首先检查当前数据库的兼容性状态：

```bash
# 检查测试环境
python sql_tools/check_ota_compatibility.py debug

# 检查生产环境
python sql_tools/check_ota_compatibility.py production

# 检查所有环境
python sql_tools/check_ota_compatibility.py
```

### 步骤2: 测试环境迁移

#### 方法1: 手动执行SQL脚本（推荐）

1. 打开DBeaver连接到测试数据库
2. 执行SQL脚本：`sql_tools/ota_task_migration.sql`
3. 确保只执行测试环境部分（kfchargingdbgc_schema）

关键SQL语句：
```sql
-- 设置搜索路径到测试环境
SET search_path TO kfchargingdbgc_schema, public;

-- 添加新字段
ALTER TABLE kfchargingdbgc_schema.ota_task 
ADD COLUMN IF NOT EXISTS detailed_status VARCHAR(50) DEFAULT '等待中';

ALTER TABLE kfchargingdbgc_schema.ota_task 
ADD COLUMN IF NOT EXISTS stage_info TEXT DEFAULT '';

ALTER TABLE kfchargingdbgc_schema.ota_task 
ADD COLUMN IF NOT EXISTS retry_count INTEGER DEFAULT 0;

ALTER TABLE kfchargingdbgc_schema.ota_task 
ADD COLUMN IF NOT EXISTS max_retries INTEGER DEFAULT 3;

ALTER TABLE kfchargingdbgc_schema.ota_task 
ADD COLUMN IF NOT EXISTS started_at TIMESTAMP NULL;

ALTER TABLE kfchargingdbgc_schema.ota_task 
ADD COLUMN IF NOT EXISTS completed_at TIMESTAMP NULL;
```

#### 方法2: 自动Python脚本

```bash
# 只迁移测试环境
python sql_tools/ota_task_auto_migration.py debug
```

### 步骤3: 验证测试环境

运行测试脚本验证迁移结果：

```bash
# 数据库迁移测试
python tests/test_database_migration.py

# 并行OTA功能测试
python tests/test_parallel_ota.py
```

### 步骤4: 生产环境迁移

**⚠️ 重要：只有在测试环境验证成功后才执行此步骤**

#### 方法1: 手动执行SQL脚本（推荐）

1. 备份生产数据库
2. 在DBeaver中取消注释生产环境部分
3. 执行生产环境迁移SQL

#### 方法2: 自动Python脚本

```bash
# 迁移生产环境
python sql_tools/ota_task_auto_migration.py production
```

## 验证清单

### 数据库结构验证

- [ ] 所有新字段已添加
- [ ] 字段类型和默认值正确
- [ ] 索引已创建
- [ ] 现有数据完整性保持

### 功能验证

- [ ] OTA任务页面正常访问
- [ ] 新建OTA任务功能正常
- [ ] 任务状态更新正常
- [ ] 并行OTA服务启动正常
- [ ] WebSocket实时更新正常

## 兼容性处理

新代码已经实现了向后兼容性：

1. **模型层兼容性**：使用 `getattr()` 安全访问新字段
2. **服务层兼容性**：使用 `hasattr()` 检查字段存在性
3. **业务层兼容性**：动态检查数据库支持的字段

## 常见问题

### Q1: 迁移后应用启动失败
**A**: 检查数据库连接配置，确保应用连接到正确的数据库和schema。

### Q2: 新字段显示为NULL
**A**: 检查迁移脚本是否正确执行，确认字段默认值设置正确。

### Q3: 并行OTA功能不工作
**A**: 检查并行OTA服务是否正确初始化，查看应用日志获取详细错误信息。

## 回滚方案

如果迁移出现问题，可以执行以下回滚操作：

```sql
-- 删除新添加的字段（谨慎操作）
ALTER TABLE kfchargingdbgc_schema.ota_task DROP COLUMN IF EXISTS detailed_status;
ALTER TABLE kfchargingdbgc_schema.ota_task DROP COLUMN IF EXISTS stage_info;
ALTER TABLE kfchargingdbgc_schema.ota_task DROP COLUMN IF EXISTS retry_count;
ALTER TABLE kfchargingdbgc_schema.ota_task DROP COLUMN IF EXISTS max_retries;
ALTER TABLE kfchargingdbgc_schema.ota_task DROP COLUMN IF EXISTS started_at;
ALTER TABLE kfchargingdbgc_schema.ota_task DROP COLUMN IF EXISTS completed_at;
```

## 总结

通过以上步骤，可以安全地将OTA数据库结构迁移到支持并行升级的新版本。关键是：

1. **先测试后生产** - 确保测试环境验证成功
2. **备份数据** - 在生产环境操作前备份
3. **逐步验证** - 每个步骤都要验证结果
4. **监控运行** - 迁移后密切监控系统运行状态
