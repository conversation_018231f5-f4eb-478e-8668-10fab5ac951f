# 并行OTA系统测试指南

本文档介绍如何测试并行OTA系统的各项功能。

## 测试脚本概述

### 1. test_parallel_ota.py - 基础功能测试
这个脚本测试系统的基础组件和配置：

- 数据库连接
- 并行OTA服务状态
- WebSocket连接
- API端点可用性
- 文件完整性检查

### 2. test_ota_functionality.py - 功能详细测试
这个脚本测试OTA任务的完整生命周期：

- OTA任务创建
- 任务状态更新
- WebSocket实时通知
- 并发任务处理
- 服务集成测试

## 运行测试

### 前提条件

1. 确保系统已正确安装和配置
2. 数据库连接正常
3. 所有依赖包已安装

### 运行基础功能测试

```bash
python test_parallel_ota.py
```

这个测试不需要启动Web服务器，主要检查系统组件的完整性。

### 运行功能详细测试

```bash
python test_ota_functionality.py
```

这个测试会创建测试数据，建议在测试环境中运行。

### 运行Web界面测试

1. 启动Web服务器：
```bash
python app.py
```

2. 在浏览器中访问 `http://localhost:5000`

3. 登录系统后，访问OTA任务页面

4. 点击"测试连接"按钮测试WebSocket连接

## 测试场景

### 1. WebSocket实时更新测试

1. 打开OTA任务页面
2. 点击"测试连接"按钮
3. 观察是否收到测试消息
4. 检查浏览器控制台的日志输出

### 2. 任务状态更新测试

1. 创建一个OTA任务
2. 观察任务状态的实时更新
3. 检查进度条是否平滑更新
4. 验证状态变化通知

### 3. 并发任务测试

1. 同时创建多个OTA任务
2. 观察任务是否能并行执行
3. 检查系统资源使用情况
4. 验证任务间不会相互干扰

### 4. 错误处理测试

1. 创建指向不存在设备的任务
2. 测试网络中断情况
3. 验证错误重试机制
4. 检查错误日志记录

## 测试数据

测试脚本会自动创建以下测试数据：

- **测试设备**: `TEST_DEVICE_001`
- **测试固件**: `测试固件 v2.0.0`
- **测试任务**: 根据测试需要动态创建

测试完成后，任务数据会被自动清理，但设备和固件数据会保留以供后续测试使用。

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务是否运行
   - 验证连接配置是否正确
   - 确认数据库权限设置

2. **WebSocket连接失败**
   - 检查防火墙设置
   - 验证端口是否被占用
   - 确认Socket.IO版本兼容性

3. **任务创建失败**
   - 检查设备是否在线
   - 验证固件文件是否存在
   - 确认用户权限设置

### 调试模式

在URL中添加 `?debug=1` 参数可以启用调试模式，显示更详细的日志信息：

```
http://localhost:5000/ota/tasks?debug=1
```

### 日志查看

系统日志位置：
- 应用日志: `logs/app.log`
- OTA服务日志: `logs/ota_service.log`
- WebSocket日志: `logs/websocket.log`

## 性能测试

### 并发任务测试

```bash
# 运行并发测试
python test_ota_functionality.py
```

### 压力测试

可以通过修改测试脚本中的并发数量来进行压力测试：

```python
# 在 test_concurrent_tasks 方法中修改线程数量
for i in range(10):  # 增加到10个并发任务
```

## 测试报告

测试完成后，脚本会输出详细的测试报告，包括：

- 测试用例执行结果
- 执行时间统计
- 成功率计算
- 失败原因分析

## 持续集成

这些测试脚本可以集成到CI/CD流水线中：

```yaml
# GitHub Actions 示例
- name: Run OTA Tests
  run: |
    python test_parallel_ota.py
    python test_ota_functionality.py
```

## 注意事项

1. **测试环境隔离**: 建议在独立的测试环境中运行功能测试
2. **数据备份**: 在生产环境测试前请备份重要数据
3. **资源监控**: 测试期间监控系统资源使用情况
4. **网络环境**: 确保测试环境网络稳定

## 扩展测试

可以根据具体需求扩展测试用例：

1. **设备兼容性测试**: 测试不同类型设备的OTA升级
2. **网络异常测试**: 模拟网络中断、延迟等情况
3. **大文件传输测试**: 测试大型固件文件的传输
4. **长时间运行测试**: 验证系统长期稳定性

## 联系支持

如果在测试过程中遇到问题，请：

1. 查看系统日志文件
2. 检查测试环境配置
3. 参考故障排除指南
4. 联系技术支持团队
