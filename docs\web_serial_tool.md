# Web串口工具开发文档

## 功能概述

Web串口工具是一个基于Web Serial API的在线串口调试工具，提供了直观的用户界面和丰富的功能，可以方便地进行串口通信调试。

## 主要特性

1. **串口连接管理**：
   - 支持多种波特率选择（9600-115200）
   - 自动检测可用串口
   - 实时显示连接状态

2. **数据收发功能**：
   - 支持ASCII和HEX格式数据发送
   - 支持自动添加换行符
   - 支持回车发送
   - 实时显示发送和接收的数据

3. **显示控制**：
   - 支持自动滚动
   - 可选时间戳显示
   - ASCII/HEX格式切换
   - 支持清空显示

4. **用户界面**：
   - 现代化的终端界面设计
   - 响应式布局，支持移动设备
   - 清晰的数据收发显示
   - 直观的操作控件

## 技术实现

### 1. 核心组件

#### SerialTool 类
- 负责串口通信的底层实现
- 基于Web Serial API
- 提供串口操作的核心功能：
  - 连接管理
  - 数据收发
  - 错误处理

#### SerialTerminal 类
- 提供用户界面和交互功能
- 管理UI状态和事件处理
- 实现数据格式转换和显示

### 2. 关键技术

1. **Web Serial API**：
   ```javascript
   // 请求串口访问
   const port = await navigator.serial.requestPort();
   // 打开串口连接
   await port.open({ baudRate: 115200 });
   ```

2. **数据处理**：
   ```javascript
   // 文本转HEX
   const bytes = new Uint8Array(hexString.length / 2);
   for (let i = 0; i < hexString.length; i += 2) {
       bytes[i / 2] = parseInt(hexString.substr(i, 2), 16);
   }
   ```

3. **异步通信**：
   ```javascript
   // 读取数据
   while (this.isReading) {
       const { value, done } = await this.reader.read();
       if (done) break;
       if (value) this.handleData(value);
   }
   ```

## 项目结构

```
static/
  ├── js/
  │   ├── tools/
  │   │   └── serial.js      # 串口通信核心类
  │   └── components/
  │       └── SerialTerminal.js  # 终端UI组件
  ├── css/
  │   └── tools/
  │       └── serial.css     # 终端样式
templates/
  └── tools/
      └── serial.html        # 页面模板
routes/
  └── tools.py              # 路由处理
```

## 使用说明

1. **连接串口**：
   - 点击"连接"按钮
   - 在弹出的对话框中选择目标串口
   - 选择合适的波特率

2. **发送数据**：
   - 在输入框中输入要发送的数据
   - 选择数据格式（ASCII/HEX）
   - 点击发送或按回车键

3. **接收数据**：
   - 自动显示接收到的数据
   - 可选择显示格式和时间戳
   - 支持自动滚动

4. **其他功能**：
   - 使用清空按钮清除显示内容
   - 通过复选框控制显示选项
   - 断开连接后自动保存配置

## 浏览器兼容性

- Chrome 89+
- Edge 89+
- Opera 76+

注：需要安全上下文（HTTPS或localhost）

## 未来计划

1. **功能增强**：
   - 添加数据记录和回放功能
   - 支持自定义数据包模板
   - 添加数据统计功能

2. **用户体验**：
   - 添加快捷键支持
   - 提供更多自定义选项
   - 优化移动端体验

3. **开发者功能**：
   - 提供API接口
   - 支持插件扩展
   - 添加调试工具

## 技术栈

- 前端：
  - HTML5
  - CSS3
  - JavaScript (ES6+)
  - Web Serial API
- 后端：
  - Flask
  - Blueprint
- UI框架：
  - Bootstrap 5
  - Font Awesome 