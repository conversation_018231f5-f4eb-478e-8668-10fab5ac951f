import time
from typing import Optional, Dict, Any
from dataclasses import dataclass
import crcmod
import struct
from construct import (
    Struct,
    Int8ub,
    Int16ub,
    Int32ub,
    Int64ub,
    Bytes,
    Array,
    Padding,
    Computed,
    len_,
    this,
    GreedyBytes,
    Adapter,
    Container,
    ListContainer,
    ConstructError,
    PaddedString,
)

from iot_client.bin_block.protocol_constants import Protocol, CmdType, MsgType, SetType, ReadType, MqttBrokerType, ReqType
from iot_client.bin_block.reg_addr import RegAddr


class TimestampAdapter(Adapter):
    """时间戳适配器：处理6字节时间戳的编解码"""

    def _encode(self, obj, context, path):
        # 将毫秒时间戳编码为6字节
        timestamp = int(obj) if obj else int(time.time() * 1000)
        return (timestamp).to_bytes(8, "big")[2:8]

    def _decode(self, obj, context, path):
        # 将6字节解码为毫秒时间戳
        return int.from_bytes(b"\x00\x00" + obj, "big")


class DeviceIdAdapter(Adapter):
    """设备ID适配器：处理5字节设备ID的编解码"""

    def _encode(self, obj, context, path):
        # 将设备ID编码为5字节（高1字节为0）
        return obj.to_bytes(5, "big")

    def _decode(self, obj, context, path):
        # 将5字节解码为设备ID
        return int.from_bytes(obj, "big")


_modbus_crc16 = crcmod.predefined.mkPredefinedCrcFun("modbus")


@dataclass
class BinBlockMQTTBrokerInfo_t:
    """二进制消息服务端信息"""

    new_devid: int
    broker_type: MqttBrokerType
    port: int
    keep_alive: int
    broker_host: str
    username: str
    password: str
    product_key: str
    device_secret: str


class BinBlock:
    """二进制消息编解码类

    用于处理二进制协议的编码和解码，支持CRC-16/MODBUS校验。
    所有多字节数据采用大端序（Big-Endian）处理。
    """

    # 消息头结构定义
    HEADER_STRUCT = Struct(
        "magic_header" / Int16ub,
        "protocol_version" / Int16ub,
        "devid" / DeviceIdAdapter(Bytes(5)),
        "blockLen" / Int16ub,
        "timestamp" / TimestampAdapter(Bytes(6)),
        "msgType" / Int8ub,
        "msgObj" / Int8ub,
        "sessionId" / Int16ub,
    )

    SET_RSP_STRUCT = Struct(
        "register_address" / Int16ub,
    )

    DEBUG_INFO_QUERY_RSP_STRUCT = Struct(
        "result" / Int8ub,
        "bl0910_error_count" / Int32ub,
        "bl0910_rms_regs" / Array(10, Int32ub),
        "relay_state" / Int16ub,
        "short_period_error_count" / Int16ub,
        "long_period_error_count" / Int16ub,
        "zero_cross_time" / Int32ub,
        "voltage" / Int16ub,
        "temperature" / Int16ub,
        "total_power" / Int32ub,
        "csq" / Int8ub,
        "ber" / Int8ub,
        "relay_pull_fault" / Int16ub,
        "relay_open_fault" / Int16ub,
    )

    SIMCARD_INFO_QUERY_RSP_STRUCT = Struct(
        "result" / Int8ub,
        "imei_len" / Int32ub,
        "imei" / PaddedString(this.imei_len, "utf8"),
        "iccid_len" / Int32ub,
        "iccid" / PaddedString(this.iccid_len, "utf8"),
    )

    FIRMWARE_INFO_QUERY_RSP_STRUCT = Struct(
        "result" / Int8ub,
        "fw1_size" / Int32ub,
        "fw1_crc32" / Int32ub,
        "fw1_update_time" / Int32ub,
        "fw1_capacity" / Int32ub,
        "fw2_size" / Int32ub,
        "fw2_crc32" / Int32ub,
        "fw2_update_time" / Int32ub,
        "fw2_capacity" / Int32ub,
        "fw1_version" / Int32ub,
        "fw2_version" / Int32ub,
        "bootloader_version" / Int32ub,
        "device_type" / Int16ub,
        "device_func" / Int16ub,
        "uid_0" / Int32ub,
        "uid_1" / Int32ub,
        "uid_2" / Int32ub,
        "compile_ts" / Int32ub,
    )

    def __init__(self, devid: int = 0, msgType: int = 0, msgObj: int = 0, data: bytearray = bytearray()):
        """初始化BinBlock实例"""
        # 消息头字段
        self.magic_header: int = Protocol.MAGIC_HEADER
        self.protocol_version: int = Protocol.PROTOCOL_VERSION_CURR
        self.devid: int = devid
        self.blockLen: int = 0
        self.timestamp: int = 0
        self.msgType: int = msgType
        self.msgObj: int = msgObj
        self.sessionId: int = 0
        self.data: bytearray = data
        self.crc: int = 0

    def is_request(self) -> bool:
        """判断是否为请求消息"""
        return (self.msgType & 0x01) == 0

    def is_response(self) -> bool:
        """判断是否为响应消息"""
        return (self.msgType & 0x01) == 1

    def encode(self) -> bytes:
        """编码二进制消息

        Returns:
            bytes: 编码后的二进制数据
        """
        # 准备头部数据
        header_data = Container(
            magic_header=self.magic_header,
            protocol_version=self.protocol_version,
            devid=self.devid,
            blockLen=len(self.data) + Protocol.FIXED_PART_LEN,
            timestamp=int(time.time() * 1000),
            msgType=self.msgType,
            msgObj=self.msgObj,
            sessionId=self.sessionId,
        )

        # 编码头部
        header_bytes = self.HEADER_STRUCT.build(header_data)

        # 组装完整消息（不包括CRC）
        message_without_crc = header_bytes + self.data

        # 计算CRC
        crc_value = _modbus_crc16(message_without_crc)

        # 添加CRC
        return message_without_crc + crc_value.to_bytes(2, "big")

    @staticmethod
    def decode_header(data: bytes) -> "BinBlock":
        """
        Decode the header from raw binary data.

        Parameters
        ----------
        data : bytes
            The raw binary data to decode.

        Returns
        -------
        BinBlock
            The decoded binary block.

        Raises
        ------
        ValueError
            If the header format is invalid or data is too short.
        struct.error
            If unpacking fails due to malformed data.
        """
        if len(data) < Protocol.HEADER_LEN:
            raise ValueError("数据长度不足")

        bin_block = BinBlock()

        try:
            # 检查魔术头
            bin_block.magic_header = struct.unpack(">H", data[0:2])[0]
            if bin_block.magic_header != Protocol.MAGIC_HEADER:
                # 魔术头不匹配错误
                raise ValueError("魔术头不匹配")

            # 协议版本号 (2字节)
            bin_block.protocol_version = struct.unpack(">H", data[2:4])[0]

            # 设备ID (5字节) - 高1字节 + 低4字节
            bin_block.devid = struct.unpack(">Q", b"\x00\x00\x00" + data[4:9])[0]

            # 块长度 (2字节)
            bin_block.blockLen = struct.unpack(">H", data[9:11])[0]

            # 时间戳 (6字节)
            timestamp_bytes = b"\x00\x00" + data[11:17]
            bin_block.timestamp = struct.unpack(">Q", timestamp_bytes)[0]

            # 消息类型 (1字节)
            bin_block.msgType = data[17]

            # 消息对象 (1字节)
            bin_block.msgObj = data[18]

            # 会话ID (2字节)
            bin_block.sessionId = struct.unpack(">H", data[19:21])[0]

            # 数据部分
            data_start = Protocol.HEADER_LEN
            data_end = bin_block.blockLen - Protocol.CRC_LEN
            bin_block.data = data[data_start:data_end]

            # CRC校验 (2字节)
            bin_block.crc = struct.unpack(">H", data[bin_block.blockLen - 2 : bin_block.blockLen])[0]
            crc_cal = _modbus_crc16(data[: bin_block.blockLen - 2])
            if bin_block.crc != crc_cal:
                return ValueError(f"CRC校验失败 {bin_block.crc} != {crc_cal}")

        except Exception as e:
            raise ValueError(f"数据解析失败: {e}")

        return bin_block

    def decode_data(self, req: "BinBlock" = None) -> Optional[dict | None]:
        """解码二进制消息

        Args:
            req (BinBlock): 请求时候的BinBlock，辅助某些消息的解析。

        Returns:
            Optional[dict]: 解码后的字典数据，解码失败返回None
        """
        parsed_data = {}
        match self.msgType:
            case MsgType.REQ_RSP:
                parsed_data = self.__parse_req_rsp()
            case MsgType.REQ:
                parsed_data = self.__parse_req()
            case MsgType.CMD_RSP:
                parsed_data = self.__parse_cmd_rsp(req)
            case MsgType.SET_RSP:
                parsed_data = self.__parse_set_rsp()
            case MsgType.READ_RSP:
                parsed_data = self.__parse_read_rsp()

        return {
            "magic_header": self.magic_header,
            "protocol_version": self.protocol_version,
            "devid": self.devid,
            "blockLen": self.blockLen,
            "timestamp": self.timestamp,
            "msgType": self.msgType,
            "msgObj": self.msgObj,
            "sessionId": self.sessionId,
            "data": self.data,
            "parsed_data": parsed_data,
            "crc": self.crc,
        }

    @staticmethod
    def decode(data: bytes) -> "BinBlock":
        bin_block = BinBlock.decode_header(data)
        return bin_block.decode_data()

    def __parse_req_rsp(self) -> dict:
        """将消息数据转换为字典格式

        Returns:
            dict: 包含消息所有字段的字典
        """
        return {}

    def __parse_req(self) -> dict:
        """将消息数据转换为字典格式

        Returns:
            dict: 包含消息所有字段的字典
        """
        match self.msgObj:
            case ReqType.B2_BMD_REQ_FIRMWARE_UPDAGRADE:
                return self._parse_firmware_upgrade()
            case _:
                return {}

    def __parse_cmd_rsp(self, req: "BinBlock") -> dict:
        """将消息数据转换为字典格式

        Args:
            req (BinBlock): 请求时候的BinBlock，辅助某些消息的解析。

        Returns:
            dict: 包含消息所有字段的字典
        """
        match self.msgObj:
            case CmdType.OTA_START:
                return self._parse_ota_start()
            case CmdType.OTA_ABORT:
                return self._parse_ota_abort()
            case CmdType.OTA_RESULT_QUERY:
                return self._parse_ota_result_query()
            case CmdType.DEVICE_FORCE_REBOOT:
                return self._parse_device_force_reboot()
            case CmdType.OTA_DATA_TRANSFER:
                return self._parse_ota_data_transfer()
            case CmdType.OTA_DIFF_DATA_CHECK:
                return self._parse_ota_diff_data_check()
            case CmdType.DEBUG_INFO_QUERY:
                return self._parse_debug_info_query(req)
            case CmdType.DEBUG_FIRMWARE_INFO_QUERY:
                return self._parse_firmware_info_query(req)
            case CmdType.MQTT_BROKER_INFO_UPDATE:
                return self._parse_mqtt_broker_info_update()
            case _:
                return {}

    def __parse_set_rsp(self) -> dict:
        """解析SET响应"""
        try:
            result = self.SET_RSP_STRUCT.parse(bytes(self.data))
            return {
                "regNumWrote": self.msgObj,
                "register_address": result.register_address,
            }
        except Exception:
            return {}

    def __parse_read_rsp(self) -> dict:
        """解析READ命令数据"""
        """解析READ响应"""
        try:
            read_rsp_struct = Struct(
                "register_address" / Int16ub,
                "register_values" / Array(self.msgObj, Int16ub),
            )
            result = read_rsp_struct.parse(bytes(self.data))
            return {
                "regNumRead": self.msgObj,
                "register_address": result.register_address,
                "register_value": list(result.register_values),
            }
        except Exception as e:
            print(e)
            return {}

    def _parse_ota_start(self) -> dict:
        """解析OTA_START命令数据"""
        # 结果 (1字节)
        result = struct.unpack(">B", self.data[0:1])[0]
        return {"result": result}

    def _parse_ota_abort(self) -> dict:
        """解析OTA_ABORT命令数据"""
        # 结果 (1字节)
        result = struct.unpack(">B", self.data[0:1])[0]
        return {"result": result}

    def _parse_ota_result_query(self) -> dict:
        """解析OTA_RESULT_QUERY命令数据"""
        # 结果 (1字节)
        result = struct.unpack(">B", self.data[0:1])[0]
        return {"result": result}

    def _parse_device_force_reboot(self) -> dict:
        """解析DEVICE_FORCE_REBOOT命令数据"""
        # 结果 (1字节)
        result = struct.unpack(">B", self.data[0:1])[0]
        return {"result": result}

    def _parse_ota_data_transfer(self) -> dict:
        """解析OTA_DATA_TRANSFER命令数据"""
        # 全部大端序
        # 待接收的分片序号 (2字节)
        slice_seq = struct.unpack(">H", self.data[0:2])[0]
        # 结果 (1字节)
        result = struct.unpack(">B", self.data[2:3])[0]
        # 响应的分片序号 (2字节) 响应是那个分片命令的序号。
        response_slice_seq = struct.unpack(">H", self.data[3:5])[0]
        return {
            "result": result,
            "response_slice_seq": response_slice_seq,
            "slice_seq": slice_seq,
        }

    def _parse_ota_diff_data_check(self) -> dict:
        """解析OTA_DIFF_DATA_CHECK命令数据"""
        # 全部大端序
        # 待校验差分分片起始序号X (2字节)
        start_seq = struct.unpack(">H", self.data[0:2])[0]
        # 已校验差分分片数量N (2字节)
        checked_count = struct.unpack(">H", self.data[2:4])[0]
        # 结果 (1字节)
        result = struct.unpack(">B", self.data[4:5])[0]
        # 差分校验结果 (ceil(已校验差分分片数量N/8)字节)
        diff_check_result_len = 32
        """
        表示差分分片是否相同的字节数组，例如第0字节的最低位bit0为对应分片X的CRC32校验值是否相同，为0表示不同，为1表示相同。
        """
        diff_check_result = []
        diff_check_result_data = self.data[7 : 7 + diff_check_result_len]
        for i in range(diff_check_result_len):
            for j in range(8):
                diff_check_result.extend(diff_check_result_data[i] & (1 << j) != 0)
        return {
            "result": result,
            "start_seq": start_seq,
            "checked_count": checked_count,
            "diff_check_result": diff_check_result,
        }

    def _parse_debug_info_query(self, req: "BinBlock") -> dict:
        """解析调试信息查询响应
        Args:
            req (BinBlock): 请求时候的BinBlock，辅助消息的解析。

        根据硬件信息查询响应的数据格式解析响应数据，包括：
        - BL0910错误计数 (4字节)
        - 10个测量通道的BL0910 RMS寄存器值 (每个4字节)
        - 继电器状态 (2字节)
        - 短周期错误计数 (2字节)
        - 长周期错误计数 (2字节)
        - 零交叉时间 (4字节)
        - 电压有效值 (2字节)
        - 温度 (2字节)
        - 总有功功率 (4字节)
        - 信号质量 (1字节)
        - 误码率 (1字节)

        Returns:
            dict: 解析后的数据字典
        """
        try:
            # 先解析基本结果
            result = self.data[0] if len(self.data) > 0 else 1
            query_type = int(req.data[0])
            query_type_dict = {0: "device", 1: "current_rms", 2: "sim_card"}
            response = {"result": result, "info": {}}
            if result != 0:
                return response

            if len(self.data) < 3:
                return response

            # 成功且有足够数据
            if query_type == 0:
                # 使用完整结构解析
                parsed = self.DEBUG_INFO_QUERY_RSP_STRUCT.parse(bytes(self.data))

                debug_info = {
                    "bl0910_error_count": parsed.bl0910_error_count,
                    "bl0910_rms_regs": list(parsed.bl0910_rms_regs),
                    "relay_state": parsed.relay_state,
                    "short_period_error_count": parsed.short_period_error_count,
                    "long_period_error_count": parsed.long_period_error_count,
                    "zero_cross_time": parsed.zero_cross_time,
                    "voltage": parsed.voltage,
                    "temperature": parsed.temperature,
                    "total_power": parsed.total_power,
                    "csq": parsed.csq,
                    "ber": parsed.ber,
                    "relay_pull_fault": parsed.relay_pull_fault,
                    "relay_open_fault": parsed.relay_open_fault,
                    "query_type": query_type_dict.get(query_type, "unknown"),
                }

                # 构建继电器状态位字典
                relay_bits = {}
                for i in range(16):
                    relay_bits[f"relay_{i}"] = bool(parsed.relay_state & (1 << i))
                debug_info["relay_bits"] = relay_bits

                response["info"] = debug_info
            elif query_type == 2:
                # 使用完整结构解析
                parsed = self.SIMCARD_INFO_QUERY_RSP_STRUCT.parse(bytes(self.data))
                sim_card_info = parsed
                sim_card_info["query_type"] = query_type_dict.get(query_type, "unknown")
                response["info"] = sim_card_info

            return response
        except Exception:
            return response

    def _parse_firmware_info_query(self, req: "BinBlock") -> dict:
        """解析固件信息查询响应
        Args:
            req (BinBlock): 请求时候的BinBlock，辅助消息的解析。

        根据固件信息查询响应的数据格式解析响应数据，包括：
        - 结果状态 (1字节)
        - FW1 Size (4字节)
        - FW1 CRC32 (4字节)
        - FW1 Update Time (4字节)
        - FW1 Capacity (4字节)
        - FW2 Size (4字节)
        - FW2 CRC32 (4字节)
        - FW2 Update Time (4字节)
        - FW2 Capacity (4字节)
        - FW1 Version (4字节)
        - FW2 Version (4字节)
        - Bootloader Version (4字节)
        - Device Type|Device Func (4字节)
        - UID[3] (3个4字节)
        - Compiler Ts (4字节)

        Returns:
            dict: 解析后的数据字典
        """
        try:
            # 先解析基本结果
            result = self.data[0] if len(self.data) > 0 else 1
            response = {"result": result, "info": {}}
            if result != 0:
                return response

            if len(self.data) < 3:
                return response

            # 成功且有足够数据，使用完整结构解析
            parsed = self.FIRMWARE_INFO_QUERY_RSP_STRUCT.parse(bytes(self.data))

            firmware_info = {
                "fw1_size": parsed.fw1_size,
                "fw1_crc32": parsed.fw1_crc32,
                "fw1_update_time": parsed.fw1_update_time,
                "fw1_capacity": parsed.fw1_capacity,
                "fw2_size": parsed.fw2_size,
                "fw2_crc32": parsed.fw2_crc32,
                "fw2_update_time": parsed.fw2_update_time,
                "fw2_capacity": parsed.fw2_capacity,
                "fw1_version": parsed.fw1_version,
                "fw2_version": parsed.fw2_version,
                "bootloader_version": parsed.bootloader_version,
                "device_type": parsed.device_type,
                "device_func": parsed.device_func,
                "uid": [parsed.uid_0, parsed.uid_1, parsed.uid_2],
                "compile_ts": parsed.compile_ts,
            }

            response["info"] = firmware_info
            return response
        except Exception as e:
            print(f"解析固件信息查询响应失败: {e}")
            return response

    def _parse_firmware_upgrade(self) -> dict:
        """解析固件信息查询响应
        Args:
            req (BinBlock): 请求时候的BinBlock，辅助消息的解析。

        根据固件信息查询响应的数据格式解析响应数据，包括：
        - 结果状态 (1字节)
        - FW1 Size (4字节)
        - FW1 CRC32 (4字节)
        - FW1 Update Time (4字节)
        - FW1 Capacity (4字节)
        - FW2 Size (4字节)
        - FW2 CRC32 (4字节)
        - FW2 Update Time (4字节)
        - FW2 Capacity (4字节)
        - FW1 Version (4字节)
        - FW2 Version (4字节)
        - Bootloader Version (4字节)
        - Device Type|Device Func (4字节)
        - UID[3] (3个4字节)
        - Compiler Ts (4字节)

        Returns:
            dict: 解析后的数据字典
        """
        try:
            # 先解析基本结果
            result = self.data[0] if len(self.data) > 0 else 1
            response = {"result": result, "info": {}}
            if result != 0:
                return response

            if len(self.data) < 3:
                return response

            # 成功且有足够数据，使用完整结构解析
            parsed = self.FIRMWARE_INFO_QUERY_RSP_STRUCT.parse(bytes(self.data))

            firmware_info = {
                "fw1_size": parsed.fw1_size,
                "fw1_crc32": parsed.fw1_crc32,
                "fw1_update_time": parsed.fw1_update_time,
                "fw1_capacity": parsed.fw1_capacity,
                "fw2_size": parsed.fw2_size,
                "fw2_crc32": parsed.fw2_crc32,
                "fw2_update_time": parsed.fw2_update_time,
                "fw2_capacity": parsed.fw2_capacity,
                "fw1_version": parsed.fw1_version,
                "fw2_version": parsed.fw2_version,
                "bootloader_version": parsed.bootloader_version,
                "device_type": parsed.device_type,
                "device_func": parsed.device_func,
                "uid": [parsed.uid_0, parsed.uid_1, parsed.uid_2],
                "compile_ts": parsed.compile_ts,
            }

            response["info"] = firmware_info
            return response
        except Exception as e:
            print(f"解析固件信息查询响应失败: {e}")
            return response

    def _parse_mqtt_broker_info_update(self) -> dict:
        update_result = self.data[0]
        response = {"result": update_result}
        return response

    @staticmethod
    def encode_ota_start(
        devid: int,
        firmware_size: int,
        slice_size: int,
        firmware_version: int,
        firmware_crc32: int,
        force_update: bool = True,
    ) -> "BinBlock":
        """编码OTA_START命令数据

        Args:
            devid: 设备ID (5字节)
            firmware_size: 固件大小 (4字节)
            slice_size: 分片大小 (2字节)
            firmware_version: 固件版本 (4字节)
            force_update: 是否强制更新 (1字节)
            firmware_crc32: 固件CRC32校验值 (4字节)

        Returns:
            BinBlock: 编码后的BinBlock实例
        """
        result = bytearray()
        # 固件大小 (4字节)
        result.extend(struct.pack(">I", firmware_size))
        # 分片大小 (2字节)
        result.extend(struct.pack(">H", slice_size))
        # 固件版本 (4字节)
        result.extend(struct.pack(">I", firmware_version))
        # 是否强制更新 (1字节)
        result.append(1 if force_update else 0)
        # 固件CRC32校验值 (4字节)
        result.extend(struct.pack(">I", firmware_crc32))

        bin_block = BinBlock(devid, MsgType.CMD, CmdType.OTA_START, result)
        return bin_block

    @staticmethod
    def encode_heart_rsp(devid: int) -> "BinBlock":
        """编码OTA_ABORT命令数据"""
        result = bytearray()
        bin_block = BinBlock(devid, MsgType.EVT_RSP, ReqType.HEART, result)
        return bin_block

    @staticmethod
    def encode_ota_abort(devid: int) -> "BinBlock":
        """编码OTA_ABORT命令数据"""
        result = bytearray()

        # 15字节的保留字段
        result.extend(b"\x00" * 15)

        bin_block = BinBlock(devid, MsgType.CMD, CmdType.OTA_ABORT, result)
        return bin_block

    @staticmethod
    def encode_ota_result_query(devid: int) -> "BinBlock":
        """编码OTA_RESULT_QUERY命令数据"""
        result = bytearray()

        # 15字节的保留字段
        result.extend(b"\x00" * 15)

        bin_block = BinBlock(devid, MsgType.CMD, CmdType.OTA_RESULT_QUERY, result)
        return bin_block

    @staticmethod
    def encode_device_force_reboot(devid: int) -> "BinBlock":
        """编码DEVICE_FORCE_REBOOT命令数据"""
        result = bytearray()

        # 15字节的保留字段
        result.extend(b"\x00" * 15)

        bin_block = BinBlock(devid, MsgType.CMD, CmdType.DEVICE_FORCE_REBOOT, result)
        return bin_block

    @staticmethod
    def encode_ota_data_transfer(devid: int, slice_seq: int, slice_size: int, data: bytes = None) -> "BinBlock":
        """编码OTA_DATA_TRANSFER命令数据"""
        result = bytearray()
        # 分片序号 (2字节)
        result.extend(struct.pack(">H", slice_seq))
        # 分片大小（字节） (2字节)
        result.extend(struct.pack(">H", slice_size))
        # N字节数据
        result.extend(data)
        bin_block = BinBlock(devid, MsgType.CMD, CmdType.OTA_DATA_TRANSFER, result)
        return bin_block

    @staticmethod
    def encode_ota_diff_data_check(
        devid: int, start_seq: int, checked_count: int, diff_check_result: list[int]
    ) -> "BinBlock":
        """编码OTA_DIFF_DATA_CHECK命令数据"""
        result = bytearray()
        # 待校验差分分片起始序号X (2字节)
        result.extend(struct.pack(">H", start_seq))
        # 待校验差分分片数量N (2字节)
        result.extend(struct.pack(">H", checked_count))
        for i in range(checked_count):
            # 分片X的CRC32校验值 (4字节)
            result.extend(struct.pack(">I", diff_check_result[i]))
        bin_block = BinBlock(devid, MsgType.CMD, CmdType.OTA_DIFF_DATA_CHECK, result)
        return bin_block

    @staticmethod
    def encode_set(devid: int, register_address: int, register_value: list[int]) -> "BinBlock":
        """编码SET命令数据"""
        result = bytearray()
        # 期望写入的寄存器数量N
        want_write_reg_num = len(register_value)
        # 寄存器地址，大端序 (2字节)
        result.extend(struct.pack(">H", register_address))
        # 寄存器值，大端序 (2字节)
        for i in range(len(register_value)):
            result.extend(struct.pack(">H", register_value[i]))
        bin_block = BinBlock(devid, MsgType.SET, want_write_reg_num, result)
        return bin_block

    @staticmethod
    def encode_read(devid: int, register_address: int, register_num: int) -> "BinBlock":
        """编码READ命令数据"""
        result = bytearray()
        # 期望读取的寄存器数量N
        want_read_reg_num = register_num
        # 寄存器地址，大端序 (2字节)
        result.extend(struct.pack(">H", register_address))
        bin_block = BinBlock(devid, MsgType.READ, want_read_reg_num, result)
        return bin_block

    @staticmethod
    def encode_debug_info_query(devid: int, query_type: int = 0) -> "BinBlock":
        """编码DEBUG_INFO_QUERY命令数据"""
        result = bytearray()
        # 查询类型 (1字节)
        result.append(query_type)
        bin_block = BinBlock(devid, MsgType.CMD, CmdType.DEBUG_INFO_QUERY, result)
        return bin_block

    @staticmethod
    def encode_firmware_info_query(devid: int, query_type: int = 0) -> "BinBlock":
        """编码DEBUG_FIRMWARE_INFO_QUERY命令数据"""
        result = bytearray()
        # 查询类型 (1字节)
        result.append(query_type)
        bin_block = BinBlock(devid, MsgType.CMD, CmdType.DEBUG_FIRMWARE_INFO_QUERY, result)
        return bin_block

    @staticmethod
    def encode_mqtt_broker_info_update(devid: int, broker_info: BinBlockMQTTBrokerInfo_t, restart: bool) -> "BinBlock":
        """编码MQTT_BROKER_INFO_UPDATE命令数据"""
        """
        新设备ID	新的设备ID，若为0代表不跟新设备ID。
        新MQTT服务器类型	0：阿里云，1：EMQX
        是否直接重启	0：不直接重启，1：直接重启
        MQTT服务器端口	新的服务器端口，若为0代表不更新端口。
        keepAliveInterval	0代表不更新
        MQTT服务器地址长度	MQTT服务器地址长度，为0代表不更新MQTT服务器地址。
        MQTT服务器地址字符串	缓存总长度为上述长度指定的字节+1，末尾字节为0，以便于C语言风格的字符串编程。
        MQTT用户名长度	为0代表不更新。
        MQTT服用户名字符串	缓存总长度为上述长度指定的字节+1，末尾字节为0，以便于C语言风格的字符串编程。
        MQTT密码长度	为0代表不更新。
        MQTT密码字符串	缓存总长度为上述长度指定的字节+1，末尾字节为0，以便于C语言风格的字符串编程。
        新产品ID长度	为0代表不更新。
        新产品ID字符串	缓存总长度为上述长度指定的字节+1，末尾字节为0，以便于C语言风格的字符串编程。
        新设备密钥长度	为0代表不更新。但是不允许为0这里。
        新设备密钥字符串	缓存总长度为上述长度指定的字节+1，末尾字节为0，以便于C语言风格的字符串编程。
        """
        MQTT_BROKER_INFO_UPDATE_CMD_DATA_STRUCT = Struct(
            "new_devid" / DeviceIdAdapter(Bytes(5)),
            "broker_type" / Int8ub,
            "restart" / Int8ub,
            "port" / Int16ub,
            "keep_alive" / Int32ub,
            "broker_host_len" / Int16ub,
            "broker_host" / PaddedString(this.broker_host_len, "utf-8"),
            Padding(1),  # 填充'\0'
            "username_len" / Int16ub,
            "username" / PaddedString(this.username_len, "utf-8"),
            Padding(1),  # 填充'\0'
            "password_len" / Int16ub,
            "password" / PaddedString(this.password_len, "utf-8"),
            Padding(1),  # 填充'\0'
            "product_key_len" / Int16ub,
            "product_key" / PaddedString(this.product_key_len, "utf-8"),
            Padding(1),  # 填充'\0'
            "device_secret_len" / Int16ub,
            "device_secret" / PaddedString(this.device_secret_len, "utf-8"),
            Padding(1),  # 填充'\0'
        )

        result_container = Container(
            new_devid=broker_info.new_devid,
            broker_type=broker_info.broker_type.value,
            restart=1 if restart else 0,  # 显式转换布尔值
            port=broker_info.port,
            keep_alive=broker_info.keep_alive,
            broker_host_len=len(broker_info.broker_host.encode("utf-8")),
            broker_host=broker_info.broker_host,
            username_len=len(broker_info.username.encode("utf-8")),
            username=broker_info.username,
            password_len=len(broker_info.password.encode("utf-8")),
            password=broker_info.password,
            product_key_len=len(broker_info.product_key.encode("utf-8")),
            product_key=broker_info.product_key,
            device_secret_len=len(broker_info.device_secret.encode("utf-8")),
            device_secret=broker_info.device_secret,
        )
        result = MQTT_BROKER_INFO_UPDATE_CMD_DATA_STRUCT.build(result_container)
        bin_block = BinBlock(devid, MsgType.CMD, CmdType.MQTT_BROKER_INFO_UPDATE, result)
        return bin_block
