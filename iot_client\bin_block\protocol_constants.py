#!/usr/bin/env python
"""
协议常量定义
包含消息类型、请求类型、命令类型、事件类型等常量定义
"""
from enum import Enum

# 协议常量定义
class Protocol:
    MAGIC_HEADER = 0x55AA
    PROTOCOL_VERSION_MIN = 0x0003
    PROTOCOL_VERSION_CURR = 0x0003
    PROTOCOL_VERSION_MAX = 0x0004
    HEADER_LEN = 21
    FIXED_PART_LEN = HEADER_LEN + 2
    CRC_LEN = 2
    MAX_DATA_LEN = 406
    MAX_MSG_LEN = 427  # MAX_DATA_LEN + FIXED_PART_LEN


# 消息类型定义
class MsgType:
    REQ = 0x00
    REQ_RSP = 0x01
    CMD = 0x02
    CMD_RSP = 0x03
    EVT = 0x04
    EVT_RSP = 0x05
    SET = 0x06
    SET_RSP = 0x07
    READ = 0x08
    READ_RSP = 0x09


# 请求类型定义
class ReqType:
    PLUG_RELAY_FAULT_REPORT = 0x01
    HEART = 0x02
    START_WIRELESS_CHARGE = 0x03
    WIRELESS_PLUG_ID_MAP = 0x04
    B2_BMD_REQ_FIRMWARE_UPDAGRADE = 0x81


# 命令类型定义
class CmdType:
    HEARTBEAT = 0x01
    START_CHARGE = 0x02
    RESTART_CHARGE_SESSION = 0x03
    BILLING_TIME_SLICE = 0x04
    STOP_CHARGE = 0x05
    HEARTBEAT_AND_BILLING = 0x06
    OTA_START = 0x07
    OTA_ABORT = 0x08
    OTA_RESULT_QUERY = 0x09
    DEVICE_FORCE_REBOOT = 0x0A
    OTA_DATA_TRANSFER = 0x0B
    OTA_DIFF_DATA_CHECK = 0x0C
    DEBUG_INFO_QUERY = 0x0D
    MQTT_BROKER_INFO_UPDATE = 0x0E
    DEBUG_FIRMWARE_INFO_QUERY = 0x10
    START_WIRELESS_CHARGE = 0x81
    USER_STOP_WIRELESS_CHARGE = 0x82


# 事件类型定义
class EvtType:
    USER_TIMEOUT_NO_PLUG_IN = 0x01
    USER_PULL_OUT_PLUG = 0x02
    USER_CHARGE_OK = 0x03
    PLUG_POWER_OVER_LIMIT = 0x04
    PLUG_POWER_OVER_SAFE_LIMIT = 0x05
    MAIN_POWER_OVER_TOTAL_LIMIT = 0x06
    MAIN_BOARD_TEMP_OVER_LIMIT = 0x07
    MAIN_POWER_OFF_SUSPECTED = 0x08
    RELAY_OPEN_CIRCUIT = 0x09
    WIRELESS_CHARGE_FINISH = 0x81
    USER_RECYCLE_WIRELESS_CHARGER_STOP_CHARGE = 0x82


# 无线充电状态码定义
class WctStatus:
    IDLE = 0x00
    READY = 0x41
    FAULT = 0x61
    CHARGING = 0x62


# 无线充电功能码定义
class WctFunc:
    QUERY_STATUS = 0x00
    STOP_OUTPUT = 0x80
    ENABLE_OUTPUT = 0xA0


# 设置类型定义
class SetType:
    SET = 0x00


# 读取类型定义
class ReadType:
    READ = 0x00


# OTA命令结果定义
class OtaCmdResult:
    # OTA启动命令结果
    LAUNCH_CMD_RES_READY = 0
    LAUNCH_CMD_RES_HAS_READY = 1
    LAUNCH_CMD_RES_FIRMWARE_TOO_LARGE = 2
    LAUNCH_CMD_RES_SLICE_TOO_LARGE = 3
    LAUNCH_CMD_RES_VERSION_LOW = 4
    LAUNCH_CMD_RES_SLICE_TOO_SMALL = 5

    # OTA中止命令结果
    ABORT_CMD_RES_SUCCESS = 0
    ABORT_CMD_RES_HAS_ABORTED = 1

    # OTA查询命令结果
    QUERY_CMD_RES_NOT_READY = 0
    QUERY_CMD_RES_READY = 1
    QUERY_CMD_RES_SUCCESS = 2
    QUERY_CMD_RES_CRC_ERROR = 3

    # 强制重启命令结果
    DEVICE_FORCE_REBOOT_CMD_RES_SUCCESS = 0

    # OTA数据传输命令结果
    DATA_TRANSFER_CMD_RES_SUCCESS = 0
    DATA_TRANSFER_CMD_RES_SLICE_RECEIVED = 1
    DATA_TRANSFER_CMD_RES_SLICE_SEQ_OUT_OF_RANGE = 2
    DATA_TRANSFER_CMD_RES_SLICE_SIZE_ERROR = 3
    DATA_TRANSFER_CMD_RES_NOT_READY = 4
    DATA_TRANSFER_CMD_RES_SLICE_SEQ_GT_WAIT = 5

    # 差分OTA校验命令结果
    DIFF_DATA_CHECK_CMD_RES_SUCCESS = 0
    DIFF_DATA_CHECK_CMD_RES_NOT_READY = 1
    DIFF_DATA_CHECK_CMD_RES_SLICE_SEQ_OUT_OF_RANGE = 2


# 工具函数
def get_msg_type_string(type_value):
    """获取消息类型字符串"""
    types = {
        MsgType.REQ: "请求",
        MsgType.REQ_RSP: "请求回复",
        MsgType.CMD: "命令",
        MsgType.CMD_RSP: "命令回复",
        MsgType.EVT: "事件",
        MsgType.EVT_RSP: "事件回复",
        MsgType.SET: "设置",
        MsgType.SET_RSP: "设置回复",
        MsgType.READ: "读取",
        MsgType.READ_RSP: "读取回复",
    }
    return types.get(type_value, "未知消息类型")


def get_req_type_string(type_value):
    """获取请求类型字符串"""
    types = {
        ReqType.PLUG_RELAY_FAULT_REPORT: "插座继电器故障上报",
        ReqType.HEART: "心跳请求",
        ReqType.START_WIRELESS_CHARGE: "开始无线充电",
        ReqType.WIRELESS_PLUG_ID_MAP: "无线充电发射模块充电口编号映射获取",
    }
    return types.get(type_value, "未知请求类型")


def get_cmd_type_string(type_value):
    """获取命令类型字符串"""
    types = {
        CmdType.HEARTBEAT: "设备心跳",
        CmdType.START_CHARGE: "启动充电",
        CmdType.RESTART_CHARGE_SESSION: "充电会话重启",
        CmdType.BILLING_TIME_SLICE: "计费时间片",
        CmdType.STOP_CHARGE: "停止充电",
        CmdType.HEARTBEAT_AND_BILLING: "设备心跳及计费时间片",
        CmdType.OTA_START: "OTA启动",
        CmdType.OTA_ABORT: "OTA中止",
        CmdType.OTA_RESULT_QUERY: "OTA结果查询",
        CmdType.DEVICE_FORCE_REBOOT: "设备强制重启",
        CmdType.OTA_DATA_TRANSFER: "OTA数据传输",
        CmdType.OTA_DIFF_DATA_CHECK: "OTA差分数据校验",
        CmdType.DEBUG_INFO_QUERY: "调试信息查询",
        CmdType.DEBUG_FIRMWARE_INFO_QUERY: "固件信息查询",
        CmdType.START_WIRELESS_CHARGE: "启动无线充电",
        CmdType.USER_STOP_WIRELESS_CHARGE: "停止无线充电",
    }
    return types.get(type_value, "未知命令类型")


def get_evt_type_string(type_value):
    """获取事件类型字符串"""
    types = {
        EvtType.USER_TIMEOUT_NO_PLUG_IN: "用户超时未接入充电器",
        EvtType.USER_PULL_OUT_PLUG: "用户主动拔出充电器",
        EvtType.USER_CHARGE_OK: "电动车充满",
        EvtType.PLUG_POWER_OVER_LIMIT: "插座功率长期过高",
        EvtType.PLUG_POWER_OVER_SAFE_LIMIT: "插座功率超过安全限制",
        EvtType.MAIN_POWER_OVER_TOTAL_LIMIT: "系统总功率超过限制被切断",
        EvtType.MAIN_BOARD_TEMP_OVER_LIMIT: "主板温度超过限制被断开",
        EvtType.MAIN_POWER_OFF_SUSPECTED: "中控疑似被断电",
        EvtType.RELAY_OPEN_CIRCUIT: "充电过程中继电器异常开路",
        EvtType.WIRELESS_CHARGE_FINISH: "无线充电完成",
        EvtType.USER_RECYCLE_WIRELESS_CHARGER_STOP_CHARGE: "用户回收无线充电器停止充电",
    }
    return types.get(type_value, "未知事件类型")


def get_set_type_string(type_value):
    """获取设置类型字符串"""
    types = {
        SetType.SET: "设置",
    }
    return types.get(type_value, "未知设置类型")


def get_read_type_string(type_value):
    """获取读取类型字符串"""
    types = {
        ReadType.READ: "读取",
    }
    return types.get(type_value, "未知读取类型")


def get_wct_status_string(status):
    """获取无线充电状态字符串"""
    statuses = {
        WctStatus.IDLE: "空闲，无接收设备接入",
        WctStatus.READY: "就绪",
        WctStatus.FAULT: "错误",
        WctStatus.CHARGING: "充电中",
    }
    return statuses.get(status, "未知状态")


def get_wct_func_string(func):
    """获取无线充电功能码字符串"""
    funcs = {
        WctFunc.QUERY_STATUS: "状态查询",
        WctFunc.STOP_OUTPUT: "停止输出",
        WctFunc.ENABLE_OUTPUT: "使能输出",
    }
    return funcs.get(func, "状态查询")


def get_ota_cmd_result_string(cmd_type, result):
    """获取OTA命令结果字符串"""
    if cmd_type == CmdType.OTA_START:
        results = {
            OtaCmdResult.LAUNCH_CMD_RES_READY: "就绪",
            OtaCmdResult.LAUNCH_CMD_RES_HAS_READY: "已就绪",
            OtaCmdResult.LAUNCH_CMD_RES_FIRMWARE_TOO_LARGE: "固件过大",
            OtaCmdResult.LAUNCH_CMD_RES_SLICE_TOO_LARGE: "分片过大",
            OtaCmdResult.LAUNCH_CMD_RES_VERSION_LOW: "版本过低",
            OtaCmdResult.LAUNCH_CMD_RES_SLICE_TOO_SMALL: "分片过小",
        }
        return results.get(result, "未知结果")
    elif cmd_type == CmdType.OTA_ABORT:
        results = {
            OtaCmdResult.ABORT_CMD_RES_SUCCESS: "中止成功",
            OtaCmdResult.ABORT_CMD_RES_HAS_ABORTED: "已中止",
        }
        return results.get(result, "未知结果")
    elif cmd_type == CmdType.OTA_RESULT_QUERY:
        results = {
            OtaCmdResult.QUERY_CMD_RES_NOT_READY: "未就绪",
            OtaCmdResult.QUERY_CMD_RES_READY: "就绪",
            OtaCmdResult.QUERY_CMD_RES_SUCCESS: "成功",
            OtaCmdResult.QUERY_CMD_RES_CRC_ERROR: "CRC错误",
        }
        return results.get(result, "未知结果")
    elif cmd_type == CmdType.DEVICE_FORCE_REBOOT:
        results = {
            OtaCmdResult.DEVICE_FORCE_REBOOT_CMD_RES_SUCCESS: "重启成功",
        }
        return results.get(result, "未知结果")
    elif cmd_type == CmdType.OTA_DATA_TRANSFER:
        results = {
            OtaCmdResult.DATA_TRANSFER_CMD_RES_SUCCESS: "传输成功",
            OtaCmdResult.DATA_TRANSFER_CMD_RES_SLICE_RECEIVED: "分片已接收",
            OtaCmdResult.DATA_TRANSFER_CMD_RES_SLICE_SEQ_OUT_OF_RANGE: "分片序号超出范围",
            OtaCmdResult.DATA_TRANSFER_CMD_RES_SLICE_SIZE_ERROR: "分片大小错误",
            OtaCmdResult.DATA_TRANSFER_CMD_RES_NOT_READY: "未就绪",
            OtaCmdResult.DATA_TRANSFER_CMD_RES_SLICE_SEQ_GT_WAIT: "分片序号大于等待接收分片",
        }
        return results.get(result, "未知结果")
    elif cmd_type == CmdType.OTA_DIFF_DATA_CHECK:
        results = {
            OtaCmdResult.DIFF_DATA_CHECK_CMD_RES_SUCCESS: "校验成功",
            OtaCmdResult.DIFF_DATA_CHECK_CMD_RES_NOT_READY: "未就绪",
            OtaCmdResult.DIFF_DATA_CHECK_CMD_RES_SLICE_SEQ_OUT_OF_RANGE: "分片序号超出范围",
        }
        return results.get(result, "未知结果")
    return "未知命令类型"



# 枚举定义MQTT服务器类型 0：阿里云，1：EMQX，定义为一个枚举类型
class MqttBrokerType(Enum):
    ALIBABA_CLOUD = 0
    EMQX = 1
