"""
寄存器地址定义模块

本模块定义了设备寄存器地址常量和相关工具函数，用于设备通信和寄存器操作。
主要包含以下功能：
1. 定义所有寄存器地址常量
2. 提供寄存器名称和地址的映射关系
3. 提供获取寄存器名称的工具函数

使用示例:
    from reg_addr import RegAddr
    
    # 获取寄存器地址
    temp_reg_addr = RegAddr.REG_TEMP1
    
    # 获取寄存器名称
    reg_name = RegAddr.get_reg_name(RegAddr.REG_CSQ)
"""

# 寄存器地址定义
class RegAddr:
    REG_T1 = 0
    REG_T2 = 1
    REG_T3 = 2
    REG_T4 = 3
    REG_T5 = 4
    REG_T6 = 5
    REG_T7 = 6
    REG_T8 = 7
    REG_T9 = 8
    REG_T10 = 9
    REG_P1 = 10
    REG_P2 = 11
    REG_P3 = 12
    REG_P4 = 13
    REG_P5 = 14
    REG_P6 = 15
    REG_P7 = 16
    REG_P8 = 17
    REG_T11 = 18
    REG_CTRL1 = 19
    REG_TEMP1 = 20
    REG_BOOT_CNT = 21
    REG_VERSION_H = 22
    REG_VERSION_L = 23
    REG_PERSENTAGE = 24
    REG_CSQ = 25

    # 扩展寄存器定义（基于下位机代码）
    REG_LOCATION_CODE = 26
    REG_LOCATION_LATITUDE_H = 27
    REG_LOCATION_LATITUDE_L = 28
    REG_LOCATION_LONGITUDE_H = 29
    REG_LOCATION_LONGITUDE_L = 30
    REG_ERROR_CNT1 = 31
    REG_ERROR_CNT2 = 32
    REG_UID_PROTECT_KEY1 = 33
    REG_HEART_AND_BILLING_PROTO_TYPE = 34
    REG_COMPILER_TS_H = 35
    REG_COMPILER_TS_L = 36
    REG_FACTORY_FAULT = 37
    REG_FACTORY_FAULT2 = 38
    REG_P2_PLUG0 = 39
    REG_P3_PLUG0 = 40
    REG_P5_PLUG0 = 41
    REG_P2_PLUG1 = 42
    REG_P3_PLUG1 = 43
    REG_P5_PLUG1 = 44
    REG_P2_PLUG2 = 45
    REG_P3_PLUG2 = 46
    REG_P5_PLUG2 = 47
    REG_P2_PLUG3 = 48
    REG_P3_PLUG3 = 49
    REG_P5_PLUG3 = 50
    REG_P2_PLUG4 = 51
    REG_P3_PLUG4 = 52
    REG_P5_PLUG4 = 53
    REG_P2_PLUG5 = 54
    REG_P3_PLUG5 = 55
    REG_P5_PLUG5 = 56
    REG_P2_PLUG6 = 57
    REG_P3_PLUG6 = 58
    REG_P5_PLUG6 = 59
    REG_P2_PLUG7 = 60
    REG_P3_PLUG7 = 61
    REG_P5_PLUG7 = 62
    REG_P2_PLUG8 = 63
    REG_P3_PLUG8 = 64
    REG_P5_PLUG8 = 65
    REG_P2_PLUG9 = 66
    REG_P3_PLUG9 = 67
    REG_P5_PLUG9 = 68
    REG_UID_PROTECT_KEY2 = 77
    REG_T12 = 78
    REG_WTC_CTRL = 79

    APP_MAIN_REG_NUM = 26
    APP_MAIN_SYSINFO_VARIABLE_REG_NUM = 100
    
    # 寄存器名称映射表
    REG_NAMES = {
        REG_T1: "REG_T1",
        REG_T2: "REG_T2",
        REG_T3: "REG_T3",
        REG_T4: "REG_T4",
        REG_T5: "REG_T5",
        REG_T6: "REG_T6",
        REG_T7: "REG_T7",
        REG_T8: "REG_T8",
        REG_T9: "REG_T9",
        REG_T10: "REG_T10",
        REG_P1: "REG_P1",
        REG_P2: "REG_P2",
        REG_P3: "REG_P3",
        REG_P4: "REG_P4",
        REG_P5: "REG_P5",
        REG_P6: "REG_P6",
        REG_P7: "REG_P7",
        REG_P8: "REG_P8",
        REG_T11: "REG_T11",
        REG_CTRL1: "REG_CTRL1",
        REG_TEMP1: "REG_TEMP1",
        REG_BOOT_CNT: "REG_BOOT_CNT",
        REG_VERSION_H: "REG_VERSION_H",
        REG_VERSION_L: "REG_VERSION_L",
        REG_PERSENTAGE: "REG_PERSENTAGE",
        REG_CSQ: "REG_CSQ",
        # 扩展寄存器
        REG_LOCATION_CODE: "REG_LOCATION_CODE",
        REG_LOCATION_LATITUDE_H: "REG_LOCATION_LATITUDE_H",
        REG_LOCATION_LATITUDE_L: "REG_LOCATION_LATITUDE_L",
        REG_LOCATION_LONGITUDE_H: "REG_LOCATION_LONGITUDE_H",
        REG_LOCATION_LONGITUDE_L: "REG_LOCATION_LONGITUDE_L",
        REG_ERROR_CNT1: "REG_ERROR_CNT1",
        REG_ERROR_CNT2: "REG_ERROR_CNT2",
        REG_UID_PROTECT_KEY1: "REG_UID_PROTECT_KEY1",
        REG_HEART_AND_BILLING_PROTO_TYPE: "REG_HEART_AND_BILLING_PROTO_TYPE",
        REG_COMPILER_TS_H: "REG_COMPILER_TS_H",
        REG_COMPILER_TS_L: "REG_COMPILER_TS_L",
        REG_FACTORY_FAULT: "REG_FACTORY_FAULT",
        REG_FACTORY_FAULT2: "REG_FACTORY_FAULT2",
        REG_P2_PLUG0: "REG_P2_PLUG0",
        REG_P3_PLUG0: "REG_P3_PLUG0",
        REG_P5_PLUG0: "REG_P5_PLUG0",
        REG_P2_PLUG1: "REG_P2_PLUG1",
        REG_P3_PLUG1: "REG_P3_PLUG1",
        REG_P5_PLUG1: "REG_P5_PLUG1",
        REG_P2_PLUG2: "REG_P2_PLUG2",
        REG_P3_PLUG2: "REG_P3_PLUG2",
        REG_P5_PLUG2: "REG_P5_PLUG2",
        REG_P2_PLUG3: "REG_P2_PLUG3",
        REG_P3_PLUG3: "REG_P3_PLUG3",
        REG_P5_PLUG3: "REG_P5_PLUG3",
        REG_P2_PLUG4: "REG_P2_PLUG4",
        REG_P3_PLUG4: "REG_P3_PLUG4",
        REG_P5_PLUG4: "REG_P5_PLUG4",
        REG_P2_PLUG5: "REG_P2_PLUG5",
        REG_P3_PLUG5: "REG_P3_PLUG5",
        REG_P5_PLUG5: "REG_P5_PLUG5",
        REG_P2_PLUG6: "REG_P2_PLUG6",
        REG_P3_PLUG6: "REG_P3_PLUG6",
        REG_P5_PLUG6: "REG_P5_PLUG6",
        REG_P2_PLUG7: "REG_P2_PLUG7",
        REG_P3_PLUG7: "REG_P3_PLUG7",
        REG_P5_PLUG7: "REG_P5_PLUG7",
        REG_P2_PLUG8: "REG_P2_PLUG8",
        REG_P3_PLUG8: "REG_P3_PLUG8",
        REG_P5_PLUG8: "REG_P5_PLUG8",
        REG_P2_PLUG9: "REG_P2_PLUG9",
        REG_P3_PLUG9: "REG_P3_PLUG9",
        REG_P5_PLUG9: "REG_P5_PLUG9",
        REG_UID_PROTECT_KEY2: "REG_UID_PROTECT_KEY2",
        REG_T12: "REG_T12",
        REG_WTC_CTRL: "REG_WTC_CTRL"
    }

    # 寄存器描述
    REG_DESC = {
        "REG_T1": "长时间未插入充电器检测时间(单位：s)",
        "REG_T2": "功率大于0连续时间，判定为已连接充电器(单位：s)",
        "REG_T3": "浮充时间，大于该时间判定为电量已满(单位：s)",
        "REG_T4": "功率超过限制判定时间(单位：s)",
        "REG_T5": "总功率超过限制触发时间(单位：ms)",
        "REG_T6": "温度超过阈值判定时间(单位：s)",
        "REG_T7": "初始单个口功率过大判定时间(单位：ms)",
        "REG_T8": "充电过程中继电器开路状态判断为中控断电的时间(单位：ms)",
        "REG_T9": "首次进入充电过程中功率突降为0时的浮充时间(单位：s)",
        "REG_T10": "无线充电浮充时间(单位：s)",
        "REG_P1": "浮充功率阈值(单位：W)",
        "REG_P2": "单口充电过程中的功率限制(单位：W)",
        "REG_P3": "单口充电过程中的安全功率限制(单位：W)",
        "REG_P4": "总功率限制(单位：W)",
        "REG_P5": "单口初始安全功率限制(单位：W)",
        "REG_P6": "启动充电后检测充电负载存在阈值(单位：W)",
        "REG_P7": "无线充电浮充功率阈值(单位：W)",
        "REG_P8": "判断是否接入用电设备的阈值，小于这个阈值判定为用电设备断开与插座的连接(单位：V5板子为BL0910的有功功率的寄存器值，V2板子为mW)",
        "REG_T11": "拔出充电器的判定时间(单位：秒)",
        "REG_CTRL1": "控制寄存器，bit0: 控制SIM卡拔出功能，bit1: 控制LED闪烁模式",
        "REG_TEMP1": "过温保护阈值(单位：℃)",
        "REG_BOOT_CNT": "启动计数",
        "REG_VERSION_H": "版本号高字节",
        "REG_VERSION_L": "版本号低字节",
        "REG_PERSENTAGE": "拔出插头判定百分比(单位：%)",
        "REG_CSQ": "信号强度(CSQ)和误码率(BER)",
        # 扩展寄存器描述
        "REG_LOCATION_CODE": "位置编码",
        "REG_LOCATION_LATITUDE_H": "纬度高位",
        "REG_LOCATION_LATITUDE_L": "纬度低位",
        "REG_LOCATION_LONGITUDE_H": "经度高位",
        "REG_LOCATION_LONGITUDE_L": "经度低位",
        "REG_ERROR_CNT1": "临时错误计数器1：高字节为服务器掉线次数，低字节为SIM卡被拔出的次数",
        "REG_ERROR_CNT2": "临时错误计数器2：高字节为电压过零中断周期小于工频周期的次数，低字节为电压过零中断周期大于工频周期的次数",
        "REG_UID_PROTECT_KEY1": "UID保护密钥1",
        "REG_HEART_AND_BILLING_PROTO_TYPE": "MQTT服务器的类型",
        "REG_COMPILER_TS_H": "本地编译时间戳高位(秒)",
        "REG_COMPILER_TS_L": "本地编译时间戳低位(秒)",
        "REG_FACTORY_FAULT": "工厂故障记录1",
        "REG_FACTORY_FAULT2": "工厂故障记录2",
        "REG_P2_PLUG0": "插座0的P2功率阈值(单位：W)",
        "REG_P3_PLUG0": "插座0的P3功率阈值(单位：W)",
        "REG_P5_PLUG0": "插座0的P5功率阈值(单位：W)",
        "REG_P2_PLUG1": "插座1的P2功率阈值(单位：W)",
        "REG_P3_PLUG1": "插座1的P3功率阈值(单位：W)",
        "REG_P5_PLUG1": "插座1的P5功率阈值(单位：W)",
        "REG_P2_PLUG2": "插座2的P2功率阈值(单位：W)",
        "REG_P3_PLUG2": "插座2的P3功率阈值(单位：W)",
        "REG_P5_PLUG2": "插座2的P5功率阈值(单位：W)",
        "REG_P2_PLUG3": "插座3的P2功率阈值(单位：W)",
        "REG_P3_PLUG3": "插座3的P3功率阈值(单位：W)",
        "REG_P5_PLUG3": "插座3的P5功率阈值(单位：W)",
        "REG_P2_PLUG4": "插座4的P2功率阈值(单位：W)",
        "REG_P3_PLUG4": "插座4的P3功率阈值(单位：W)",
        "REG_P5_PLUG4": "插座4的P5功率阈值(单位：W)",
        "REG_P2_PLUG5": "插座5的P2功率阈值(单位：W)",
        "REG_P3_PLUG5": "插座5的P3功率阈值(单位：W)",
        "REG_P5_PLUG5": "插座5的P5功率阈值(单位：W)",
        "REG_P2_PLUG6": "插座6的P2功率阈值(单位：W)",
        "REG_P3_PLUG6": "插座6的P3功率阈值(单位：W)",
        "REG_P5_PLUG6": "插座6的P5功率阈值(单位：W)",
        "REG_P2_PLUG7": "插座7的P2功率阈值(单位：W)",
        "REG_P3_PLUG7": "插座7的P3功率阈值(单位：W)",
        "REG_P5_PLUG7": "插座7的P5功率阈值(单位：W)",
        "REG_P2_PLUG8": "插座8的P2功率阈值(单位：W)",
        "REG_P3_PLUG8": "插座8的P3功率阈值(单位：W)",
        "REG_P5_PLUG8": "插座8的P5功率阈值(单位：W)",
        "REG_P2_PLUG9": "插座9的P2功率阈值(单位：W)",
        "REG_P3_PLUG9": "插座9的P3功率阈值(单位：W)",
        "REG_P5_PLUG9": "插座9的P5功率阈值(单位：W)",
        "REG_UID_PROTECT_KEY2": "UID保护密钥2",
        "REG_T12": "无线充电最大允许充电时间(单位：分钟)",
        "REG_WTC_CTRL": "无线充电控制寄存器"
    }
    
    @classmethod
    def get_reg_name(cls, reg_addr: int) -> str:
        """获取寄存器名称"""
        return cls.REG_NAMES.get(reg_addr, f"未知寄存器{reg_addr}")

    @classmethod
    def get_reg_desc(cls, reg_name: str) -> str:
        """获取寄存器描述"""
        return cls.REG_DESC.get(reg_name, f"未知寄存器{reg_name}")

    @classmethod
    def get_reg_addr_by_name(cls, reg_name: str) -> int:
        """根据寄存器名称获取地址"""
        # 创建反向映射
        name_to_addr = {name: addr for addr, name in cls.REG_NAMES.items()}
        return name_to_addr.get(reg_name, -1)
