#!/usr/bin/env python
"""
OTA客户端
用于向设备发送OTA升级命令和固件
"""

import os
import math
import logging
import zlib
from datetime import datetime
from typing import Optional, Callable, List, Tuple
from enum import Enum
from dataclasses import dataclass
from iot_client.bin_block.protocol_constants import *
from iot_client.bin_block.bin_block import BinBlock
from iot_client.iot_client import IoTClient
from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_exception_type, before_log, after_log
from iot_client.platform.platform_type import PlatformType


class OtaState(Enum):
    """OTA状态枚举"""

    INIT = "INIT"
    LOADING_FIRMWARE = "LOADING_FIRMWARE"
    STARTING = "STARTING"
    CHECKING_DIFF = "CHECKING_DIFF"
    SENDING_SLICES = "SENDING_SLICES"
    QUERYING_RESULT = "QUERYING_RESULT"
    REBOOTING = "REBOOTING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    ABORTED = "ABORTED"


@dataclass
class FirmwareInfo:
    """固件信息数据类"""

    path: str
    data: bytes
    size: int
    version: int
    crc32: int
    slice_count: int


@dataclass
class OtaConfig:
    """OTA配置数据类"""

    slice_size: int = 400
    timeout: int = 30
    max_retries: int = 3
    retry_delay: int = 1
    crc32_capacity: int = 96  # (400 - 6) // 4


# 配置日志
def setup_logging():
    """配置日志记录器"""
    # 创建日志目录（如果不存在）
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 生成日志文件名（包含时间戳）
    log_file = os.path.join(log_dir, f"ota_client_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

    # 配置根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)

    # 清除现有的处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)

    # 创建文件处理器
    file_handler = logging.FileHandler(log_file, encoding="utf-8", mode="w")
    file_handler.setLevel(logging.INFO)
    file_formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    file_handler.setFormatter(file_formatter)
    root_logger.addHandler(file_handler)

    # 创建并返回OTA客户端专用的日志记录器
    logger = logging.getLogger("OTA_Client")
    logger.info(f"日志文件已创建: {log_file}")
    return logger


# 初始化日志记录器
logger = setup_logging()


class OtaClient:
    """
    OTA客户端类
    用于向设备发送OTA升级命令和固件
    """

    def __init__(
        self,
        iot_client: IoTClient,
        target_product_key: str,
        target_device_name: str,
        logger: Optional[logging.Logger] = None,
        progress_callback: Optional[Callable[[int, int, str], None]] = None,
    ):
        """
        初始化OTA客户端

        Args:
            iot_client: IoT客户端实例
            target_product_key: 目标产品密钥
            target_device_name: 目标设备名称
            logger: 日志记录器，如果为None则使用默认日志记录器
            progress_callback: 进度回调函数，用于外部更新进度
                              参数: (当前进度, 总进度, 当前阶段描述)
        """
        self.iot_client = iot_client
        self.target_product_key = target_product_key
        self.target_device_name = target_device_name
        self.target_device_id = int(target_device_name)
        self.logger = logger or logging.getLogger("OTA_Client")
        self.progress_callback = progress_callback

        # 构建主题和平台类型
        self.topic = f"/{target_product_key}/{target_device_name}/user/ota"
        self.platform = PlatformType.EMQX if self.topic.startswith("/wx") else PlatformType.ALIBABA_CLOUD

        # 初始化配置和状态
        self.config = OtaConfig()
        self.state = OtaState.INIT
        self.firmware_info: Optional[FirmwareInfo] = None
        self.diff_check_result: List[bool] = []

        self.logger.info(
            f"OTA客户端初始化完成: 产品密钥={target_product_key}, 设备名称={target_device_name}, "
            f"设备ID={self.target_device_id}, 平台={self.platform.value}"
        )

    def _update_progress(self, current: int, total: int, message: str) -> None:
        """
        更新进度回调
        Args:
            current: 当前进度
            total: 总进度
            message: 当前阶段描述"""
        if self.progress_callback:
            self.progress_callback(current, total, message)

    def _update_state(self, new_state: OtaState) -> None:
        """更新OTA状态"""
        old_state = self.state
        self.state = new_state
        self.logger.info(f"OTA状态变更: {old_state.value} -> {new_state.value}")

    def _validate_connection(self) -> bool:
        """验证IoT客户端连接状态"""
        if not self.iot_client.is_connected():
            self.logger.error("未连接到IoT平台")
            return False
        return True

    def _calculate_firmware_version(self, firmware_data: bytes) -> int:
        """
        从固件数据中计算版本号

        Args:
            firmware_data: 固件数据

        Returns:
            固件版本号
        """
        if len(firmware_data) >= 32:
            # 使用小端序读取固件版本（偏移量28处，4字节）
            return int.from_bytes(firmware_data[28:32], byteorder="little")
        else:
            self.logger.warning("固件大小不足，无法获取版本号，使用默认值0")
            return 0

    def load_firmware(self, firmware_path: str) -> bool:
        """
        加载固件文件

        Args:
            firmware_path: 固件文件路径

        Returns:
            bool: 加载是否成功
        """
        self._update_state(OtaState.LOADING_FIRMWARE)

        try:
            # 验证文件存在
            if not os.path.exists(firmware_path):
                raise FileNotFoundError(f"固件文件不存在: {firmware_path}")

            # 读取固件数据
            with open(firmware_path, "rb") as f:
                firmware_data = f.read()

            if not firmware_data:
                raise ValueError("固件文件为空")

            # 计算固件信息
            firmware_size = len(firmware_data)
            firmware_version = self._calculate_firmware_version(firmware_data)
            firmware_crc32 = zlib.crc32(firmware_data) & 0xFFFFFFFF
            slice_count = (firmware_size + self.config.slice_size - 1) // self.config.slice_size

            # 创建固件信息对象
            self.firmware_info = FirmwareInfo(
                path=firmware_path,
                data=firmware_data,
                size=firmware_size,
                version=firmware_version,
                crc32=firmware_crc32,
                slice_count=slice_count,
            )

            self.logger.info(
                f"固件加载成功: 路径={firmware_path}, 大小={firmware_size}字节, "
                f"CRC32=0x{firmware_crc32:08X}, 版本=0x{firmware_version:08X}, 分片数={slice_count}"
            )

            return True

        except Exception as e:
            self.logger.error(f"加载固件失败: {e}")
            self._update_state(OtaState.FAILED)
            return False

    def _send_ota_start_command(self, force_update: bool) -> bool:
        """
        发送OTA启动命令

        Args:
            force_update: 是否强制更新

        Returns:
            命令是否发送成功
        """
        if not self.firmware_info:
            self.logger.error("固件信息未加载")
            return False

        # 先发送中止命令
        self._abort_ota()

        # 发送启动命令
        bin_block = BinBlock.encode_ota_start(
            self.target_device_id,
            self.firmware_info.size,
            self.config.slice_size,
            self.firmware_info.version,
            self.firmware_info.crc32,
            force_update,
        )

        msg = self.iot_client.request_syc(self.topic, bin_block, self.platform, timeout=30)

        if msg is None:
            self.logger.error("未收到OTA启动命令回复")
            return False

        # 解析回复结果
        result = msg["parsed_data"]["result"]
        if result in [OtaCmdResult.LAUNCH_CMD_RES_READY, OtaCmdResult.LAUNCH_CMD_RES_HAS_READY]:
            self.logger.info("OTA启动命令发送成功")
            return True
        else:
            result_str = get_ota_cmd_result_string(CmdType.OTA_START, result)
            self.logger.error(f"OTA启动命令发送失败: {result_str}")
            return False

    def _split_firmware_data(self) -> List[bytes]:
        """
        将固件数据按照分片大小进行切分

        Returns:
            分片数据列表
        """
        if not self.firmware_info:
            return []

        slice_datas = []
        for i in range(self.firmware_info.slice_count):
            start_idx = i * self.config.slice_size
            end_idx = min((i + 1) * self.config.slice_size, self.firmware_info.size)
            slice_datas.append(self.firmware_info.data[start_idx:end_idx])

        self.logger.info(f"固件数据切分完成: 共{self.firmware_info.slice_count}个分片")
        return slice_datas

    def _calculate_slice_crc32_list(self, slice_datas: List[bytes]) -> List[int]:
        """
        计算分片CRC32列表

        Args:
            slice_datas: 分片数据列表

        Returns:
            CRC32列表
        """
        return [zlib.crc32(slice_data) & 0xFFFFFFFF for slice_data in slice_datas]

    def _diff_ota_data_check(self, slice_datas: List[bytes]) -> bool:
        """
        差分OTA数据校验

        Args:
            slice_datas: 分片数据列表

        Returns:
            校验是否成功
        """
        if not slice_datas:
            self.logger.error("分片数据为空")
            return False

        # 检查分片数量限制
        if len(slice_datas) > 32 * 8:
            self.logger.error("分片数据校验失败，分片数量超过限制")
            return False

        # 计算分片CRC32
        slice_crc32_list = self._calculate_slice_crc32_list(slice_datas)

        # 计算校验批次
        crc32_capacity = self.config.crc32_capacity
        batch_count = math.ceil(len(slice_datas) / crc32_capacity)

        self.logger.info(f"开始差分OTA数据校验: 分片数={len(slice_datas)}, 批次数={batch_count}")

        # 分批校验
        for batch_idx in range(batch_count):
            start_seq = batch_idx * crc32_capacity
            batch_size = min(crc32_capacity, len(slice_datas) - start_seq)
            batch_crc32_list = slice_crc32_list[start_seq : start_seq + batch_size]

            # 发送校验命令
            success = self._send_diff_check_command(start_seq, batch_size, batch_crc32_list)
            if not success:
                self.logger.error(f"批次{batch_idx}校验失败")
                return False

            # 更新进度
            self._update_progress(batch_idx + 1, batch_count, f"差分数据校验 {batch_idx + 1}/{batch_count}")

        return True

    def _send_diff_check_command(self, start_seq: int, batch_size: int, crc32_list: List[int]) -> bool:
        """
        发送差分校验命令

        Args:
            start_seq: 起始序号
            batch_size: 批次大小
            crc32_list: CRC32列表

        Returns:
            命令是否发送成功
        """
        for attempt in range(self.config.max_retries):
            try:
                bin_block = BinBlock.encode_ota_diff_data_check(
                    self.target_device_id, start_seq, batch_size, crc32_list
                )
                msg = self.iot_client.request_syc(self.topic, bin_block, self.platform, timeout=10)

                if msg is None:
                    self.logger.warning(f"差分校验命令未收到回复，重试 {attempt + 1}/{self.config.max_retries}")
                    continue

                if msg["parsed_data"]["result"] == OtaCmdResult.DIFF_DATA_CHECK_CMD_RES_SUCCESS:
                    self.diff_check_result = msg["parsed_data"]["diff_check_result"]
                    self.logger.info(f"差分校验成功: 起始序号={start_seq}, 批次大小={batch_size}")
                    return True
                else:
                    self.logger.warning(f"差分校验失败: {msg['parsed_data']['result']}")

            except Exception as e:
                self.logger.warning(f"差分校验异常: {e}")

        return False

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_fixed(5),
        before=before_log(logger, logging.WARNING),
        after=after_log(logger, logging.INFO),
        retry=retry_if_exception_type((TypeError, ValueError, KeyError)),
    )
    def _send_single_slice(self, slice_index: int, slice_data: bytes) -> Tuple[bool, int]:
        """
        发送单个固件分片

        Args:
            slice_index: 分片索引
            slice_data: 分片数据

        Returns:
            (发送是否成功, 下一个分片序号)
        """
        bin_block = BinBlock.encode_ota_data_transfer(self.target_device_id, slice_index, len(slice_data), slice_data)

        msg = self.iot_client.request_syc(self.topic, bin_block, self.platform, timeout=30)

        if msg is None:
            self.logger.warning(f"分片{slice_index}发送未收到回复，将重试")
            raise ValueError("未收到回复")

        result = msg["parsed_data"]["result"]
        if result == OtaCmdResult.DATA_TRANSFER_CMD_RES_SUCCESS:
            next_slice_seq = msg["parsed_data"]["slice_seq"]
            self.logger.info(f"分片{slice_index}发送成功, 下一个分片序号: {next_slice_seq}")
            return True, next_slice_seq
        else:
            result_str = get_ota_cmd_result_string(CmdType.OTA_DATA_TRANSFER, result)
            self.logger.warning(f"分片{slice_index}发送失败: {result_str}，将重试")
            raise ValueError(f"发送失败: {result_str}")

    def _send_firmware_slices(self, slice_datas: List[bytes]) -> bool:
        """
        发送固件分片

        Args:
            slice_datas: 分片数据列表

        Returns:
            发送是否成功
        """
        if not slice_datas:
            self.logger.error("分片数据为空")
            return False

        success_count = 0
        current_slice_seq = 0
        total_slices = len(slice_datas)

        self.logger.info(f"开始发送固件分片: 总数={total_slices}")

        while current_slice_seq < total_slices:
            try:
                success, next_slice_seq = self._send_single_slice(current_slice_seq, slice_datas[current_slice_seq])

                if success:
                    success_count += 1
                    # 更新进度
                    self._update_progress(
                        current_slice_seq + 1, total_slices, f"发送固件分片 {current_slice_seq + 1}/{total_slices}"
                    )
                    current_slice_seq = next_slice_seq
                else:
                    self.logger.error(f"分片{current_slice_seq}发送失败")
                    return False

            except Exception as e:
                self.logger.error(f"分片{current_slice_seq}发送失败，已达到最大重试次数: {e}")
                return False

        self.logger.info(f"固件分片发送完成: 成功{success_count}/{total_slices}")
        return True

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_fixed(1),
        before=before_log(logger, logging.WARNING),
        after=after_log(logger, logging.INFO),
    )
    def _query_ota_result(self) -> bool:
        """
        查询OTA结果

        Returns:
            查询是否成功
        """
        bin_block = BinBlock.encode_ota_result_query(self.target_device_id)
        msg = self.iot_client.request_syc(self.topic, bin_block, self.platform, timeout=10)

        if msg is None:
            self.logger.warning("未收到OTA结果查询回复，将重试")
            raise ValueError("未收到回复")

        result = msg["parsed_data"]["result"]
        if result == OtaCmdResult.QUERY_CMD_RES_SUCCESS:
            self.logger.info("OTA结果查询成功")
            return True
        else:
            result_str = get_ota_cmd_result_string(CmdType.OTA_RESULT_QUERY, result)
            self.logger.warning(f"OTA结果查询失败: {result_str}，将重试")
            raise ValueError(f"查询失败: {result_str}")

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_fixed(1),
        before=before_log(logger, logging.WARNING),
        after=after_log(logger, logging.INFO),
    )
    def _abort_ota(self) -> bool:
        """
        中止OTA升级

        Returns:
            中止是否成功
        """
        bin_block = BinBlock.encode_ota_abort(self.target_device_id)
        msg = self.iot_client.request_syc(self.topic, bin_block, self.platform, timeout=30)

        if msg is None:
            self.logger.warning("未收到OTA中止回复，将重试")
            raise ValueError("未收到回复")

        self.logger.info("OTA中止成功")
        return True

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_fixed(1),
        before=before_log(logger, logging.WARNING),
        after=after_log(logger, logging.INFO),
    )
    def _force_reboot_device(self) -> bool:
        """
        强制重启设备

        Returns:
            重启命令是否发送成功
        """
        bin_block = BinBlock.encode_device_force_reboot(self.target_device_id)
        msg = self.iot_client.request_syc(self.topic, bin_block, self.platform, timeout=10)

        if msg is None:
            self.logger.warning("未收到强制重启设备回复，将重试")
            raise ValueError("未收到回复")

        self.logger.info("强制重启设备命令发送成功")
        return True

    def start_ota(self, firmware_path: str, force_update: bool = False) -> bool:
        """
        开始OTA升级

        Args:
            firmware_path: 固件文件路径
            force_update: 是否强制升级

        Returns:
            升级是否成功
        """
        self.logger.info(f"开始OTA升级: 固件={firmware_path}, 强制升级={force_update}")

        try:
            # 1. 验证连接
            if not self._validate_connection():
                self._update_state(OtaState.FAILED)
                return False

            # 2. 加载固件
            if not self.load_firmware(firmware_path):
                self._update_state(OtaState.FAILED)
                return False

            # 3. 发送启动命令
            self._update_state(OtaState.STARTING)
            if not self._send_ota_start_command(force_update):
                self._update_state(OtaState.FAILED)
                return False

            # 4. 切分固件数据
            slice_datas = self._split_firmware_data()
            if not slice_datas:
                self.logger.error("固件数据切分失败")
                self._update_state(OtaState.FAILED)
                return False

            # 5. 差分数据校验（可选）
            self._update_state(OtaState.CHECKING_DIFF)
            # if not self._diff_ota_data_check(slice_datas):
            #     self.logger.error("差分OTA数据校验失败")
            #     self._update_state(OtaState.FAILED)
            #     return False

            # 6. 发送固件分片
            self._update_state(OtaState.SENDING_SLICES)
            if not self._send_firmware_slices(slice_datas):
                self.logger.error("发送固件分片失败")
                self._update_state(OtaState.FAILED)
                return False

            # 7. 查询OTA结果
            self._update_state(OtaState.QUERYING_RESULT)
            if not self._query_ota_result():
                self.logger.error("查询OTA结果失败")
                self._update_state(OtaState.FAILED)
                return False

            # 8. 强制重启设备
            self._update_state(OtaState.REBOOTING)
            if not self._force_reboot_device():
                self.logger.error("强制重启设备失败")
                self._update_state(OtaState.FAILED)
                return False

            # 9. 完成
            self._update_state(OtaState.COMPLETED)
            self.logger.info("OTA升级流程完成")
            return True

        except Exception as e:
            self.logger.error(f"OTA升级过程中发生异常: {e}")
            self._update_state(OtaState.FAILED)
            return False

    def stop_ota(self) -> bool:
        """
        强制停止OTA升级过程

        此方法用于外部程序调用，可以中断正在进行的OTA升级过程。
        它会发送中止命令到设备，并等待设备确认。

        Returns:
            停止命令是否发送成功
        """
        if not self._validate_connection():
            return False

        self.logger.info("正在强制停止OTA升级过程...")

        try:
            # 发送中止命令
            self._abort_ota()
            self._update_state(OtaState.ABORTED)
            self.logger.info("OTA升级过程已成功停止")
            return True
        except Exception as e:
            self.logger.error(f"停止OTA升级过程失败: {e}")
            return False
