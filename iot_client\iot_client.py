#!/usr/bin/env python
"""
IoT客户端
合并IoTPlatformManager和AmqpClient功能，提供统一的设备通信接口
支持阿里云物联网平台和EMQX平台的双平台管理
"""

import time
import logging
import threading
from typing import Callable, Optional, Union
from collections import OrderedDict
from iot_client.platform.platform_type import PlatformType
from iot_client.platform.amqp import AmqpConfig
from iot_client.platform.ali_mqtt_client import AliMQTTClient
from iot_client.platform.emqx_mqtt_client import EMQXConfig, EMQXMQTTClient
from iot_client.bin_block.bin_block import BinBlock
from dataclasses import dataclass
import re

@dataclass
class BinBlockRequest:
    data: BinBlock
    timeout: int
    retry_times: int
    on_success: Callable[[BinBlock], None]
    on_error: Callable[[BinBlock], None]
    last_send_time: float
    current_retry: int = 0
    topic: str = ""
    platform_type: PlatformType = (None,)
    is_executing: bool = False  # 是否正在执行


class IoTClient:
    """
    IoT客户端类
    同时管理阿里云物联网平台和EMQX平台，提供统一的设备通信接口
    """

    def __init__(
        self,
        topic_filters: list[str],
        logger: Optional[logging.Logger],
        ali_config: AmqpConfig,
        emqx_config: Optional[EMQXConfig],
    ):
        """
        初始化IoT客户端

        Args:
            topic_filters: 阿里云主题过滤器，正则表达式
            logger: 日志记录器，如果为None则使用默认日志记录器
            ali_config: 阿里云AMQP配置
            emqx_config: EMQX配置（可选）
        """
        self.logger = logger

        ali_topic_filters = ["^/[^/]+/[^/]+/user/update$", "^/[^/]+/[^/]+/user/ota_ack$"]

        emqx_topic_filters = ["^/[^/]+/[^/]+/user/get$", "^/[^/]+/[^/]+/user/ota_ack$"]

        self.request_topic_filters = topic_filters

        self._ali_client: AliMQTTClient = AliMQTTClient(config=ali_config, topic_filters=ali_topic_filters, logger=logger)
        self._emqx_client: EMQXMQTTClient = EMQXMQTTClient(
            config=emqx_config, topic_filters=emqx_topic_filters, logger=logger
        )

        # 消息请求队列<设备ID, <会话ID，请求消息>> - 使用OrderedDict保证顺序
        self.msg_request_map: dict[int, OrderedDict[int, BinBlockRequest]] = {}
        # 消息请求的会话id计数器<设备ID, 会话ID>
        self.msg_request_session_id: dict[int, int] = {}
        self._ali_client.set_on_message_handler(self.__ali_message_handler)
        self._emqx_client.set_on_message_handler(self.__emqx_message_handler)

        # 线程控制
        self.msg_request_loop_thread = None
        self._stop_event = threading.Event()
        self._request_lock = threading.Lock()

        # 所有消息回调
        self.on_message_callback = None
        self.on_request_cb = None

    def start(self):
        """
        启动IoT客户端
        """
        # 启动EMQX客户端
        self._emqx_client.start()
        # 启动阿里云客户端
        # self._ali_client.start()
        # 启动消息请求循环线程
        self._start_request_loop_thread()

    def stop(self):
        """停止IoT客户端"""
        # 停止消息请求循环线程
        self._stop_request_loop_thread()
        # 停止阿里云客户端
        self._ali_client.stop()
        # 停止EMQX客户端
        self._emqx_client.stop()

    def is_connected(self):
        return self._ali_client.is_connected() or self._emqx_client.is_connected()

    def _start_request_loop_thread(self):
        """启动消息请求循环线程"""
        if self.msg_request_loop_thread is None or not self.msg_request_loop_thread.is_alive():
            self._stop_event.clear()
            self.msg_request_loop_thread = threading.Thread(
                target=self._request_loop_worker, name="IoTClient-RequestLoop"
            )
            self.msg_request_loop_thread.daemon = True
            self.msg_request_loop_thread.start()
            self.logger.info("消息请求循环线程已启动")

    def _stop_request_loop_thread(self):
        """停止消息请求循环线程"""
        if self.msg_request_loop_thread is not None and self.msg_request_loop_thread.is_alive():
            self._stop_event.set()
            self.msg_request_loop_thread.join(timeout=5)
            self.logger.info("消息请求循环线程已停止")

    def _request_loop_worker(self):
        """消息请求循环工作线程"""
        self.logger.info("消息请求循环线程开始运行")

        while not self._stop_event.is_set():
            try:
                current_time = time.time()

                # 检查每个设备的请求队列
                with self._request_lock:
                    for device_id, request_queue in list(self.msg_request_map.items()):
                        if not request_queue or len(request_queue) == 0:
                            # 清理空的设备队列
                            self.msg_request_map.pop(device_id)
                            continue

                        # 获取队列中的第一个请求
                        session_id, request = next(iter(request_queue.items()))
                        if request.is_executing:
                            # 有正在执行的请求，检查是否超时
                            if current_time - request.last_send_time >= request.timeout:
                                # 超时处理
                                self._handle_timeout_request(device_id, session_id, request)
                        else:
                            # 没有正在执行的请求，启动队列中的第一个请求
                            self._start_request(device_id, session_id, request)

                # 短暂休眠以避免过度占用CPU
                time.sleep(0.11)

            except Exception as e:
                self.logger.error(f"消息请求循环线程发生错误: {e}")
                time.sleep(1)

        self.logger.info("消息请求循环线程已退出")

    def _handle_timeout_request(self, device_id: int, session_id: int, request: BinBlockRequest):
        """处理超时的请求"""
        # 检查是否还有重试机会
        if request.current_retry < request.retry_times:
            # 重试发送请求
            logging.info(f"请求 {request.topic} 超时，进行重试")
            self._start_request(device_id, session_id, request)
            request.current_retry += 1
        else:
            # 超出重试次数，调用错误回调
            self._complete_request(device_id, session_id, request, is_error=True)

    def _start_request(self, device_id: int, session_id: int, request: BinBlockRequest):
        try:
            # 发送消息
            success = self.write_message(request.topic, request.data.encode(), request.platform_type)
            if success:
                # 发送成功，标记为正在执行
                request.last_send_time = time.time()
                request.is_executing = True
            else:
                # 发送失败，移除请求并调用错误回调
                self._complete_request(request.data, session_id, request, is_error=True)
        except Exception as e:
            self.logger.error(f"启动请求时发生错误: {e}")
            self._complete_request(device_id, session_id, request, is_error=True)

    def _complete_request(
        self,
        device_id: int,
        session_id: int,
        request: BinBlockRequest,
        is_error: bool = False,
        response_msg: BinBlock = None,
    ):
        """完成请求处理（成功或失败）"""
        # 调用回调函数
        try:
            if is_error and request.on_error:
                request.on_error(request)
            elif not is_error and request.on_success:
                request.on_success(request, response_msg)
        except Exception as e:
            self.logger.error(f"调用回调函数时发生错误: {e}")

        # 从请求队列中移除
        if device_id in self.msg_request_map and session_id in self.msg_request_map[device_id]:
            self.msg_request_map[device_id].popitem(last=False)
            if len(self.msg_request_map[device_id]) == 0:
                self.msg_request_map.pop(device_id)

    def write_message(
        self, topic: str, payload: Union[bytes, bytearray, str], platform_type: Optional[PlatformType] = None
    ) -> bool:
        """
        向设备发送消息

        Args:
            topic: 主题名称
            payload: 消息
            platform_type: 平台类型

        Returns:
            True - 发送成功
            False - 发送失败
        """
        match platform_type:
            case PlatformType.ALIBABA_CLOUD:
                return self._ali_client.write_message(topic, payload)
            case PlatformType.EMQX:
                return self._emqx_client.write_message(topic, payload)
            case _:
                return False

    def __emqx_message_handler(self, topic: str, payload: Union[bytes, bytearray, str], qos: int, client_id: str):
        self.__on_message_handler(topic, payload, qos, client_id, PlatformType.EMQX)

    def __ali_message_handler(self, topic: str, payload: Union[bytes, bytearray, str], qos: int, client_id: str):
        self.__on_message_handler(topic, payload, qos, client_id, PlatformType.ALIBABA_CLOUD)

    def __on_message_handler(
        self, topic: str, payload: Union[bytes, bytearray, str], qos: int, client_id: str, platform_type: PlatformType
    ):
        # 有线处理所有消息回调函数
        try:
            if self.on_message_callback:
                self.on_message_callback(topic, payload, qos, client_id, platform_type)
        except Exception as e:
            self.logger.error(f"调用消息回调函数时发生错误: {e}")

        try:

            # 检查是否匹配任何过滤主题
            matched = False
            for filter in self.request_topic_filters:
                if re.match(filter, topic):
                    matched = True
                    break
            
            if not matched:
                # 没有匹配到请求所需回复的topic则跳过
                return

            # 解析主题获取设备信息
            topic_parts = topic.split("/")
            if len(topic_parts) < 2:
                self.logger.warning(f"主题格式错误: {topic}")
                return

            product_key = topic_parts[1]
            device_name = topic_parts[2]
            device_id = int(device_name)

            # 解析消息
            receive_msg: BinBlock = BinBlock.decode_header(payload)
            session_id = receive_msg.sessionId

            # 检查是否是响应消息
            if receive_msg.is_response():
                self._handle_response_message(device_id, session_id, receive_msg)
            else:
                # logging.info(f"收到请求消息 - 设备ID: {device_id}, 会话ID: {session_id}")
                self._handle_request_message(device_id, session_id, receive_msg)

        except Exception as e:
            self.logger.error(f"解析消息时发生错误: {e}")

    def _handle_response_message(self, device_id: int, session_id: int, response_msg: BinBlock):
        """处理响应消息"""
        with self._request_lock:
            # 该设备发送了请求并且当前收到的回复的会话ID在包含在请求队列中
            if device_id in self.msg_request_map and session_id in self.msg_request_map[device_id]:
                request = self.msg_request_map[device_id][session_id]
                self.logger.debug(f"收到响应消息 - 设备ID: {device_id}, 会话ID: {session_id}")
                self._complete_request(device_id, session_id, request, is_error=False, response_msg=response_msg)
            else:
                self.logger.warning(f"收到未知请求的响应 - 设备ID: {device_id}, 会话ID: {session_id}")

    def _handle_request_message(self, device_id: int, session_id: int, request_msg: BinBlock):
        """处理请求消息"""
        try:
            if self.on_request_cb:
                self.on_request_cb(device_id, session_id, request_msg)
        except Exception as e:
            self.logger.error(f"解析请求消息数据时发生错误: {e}")

    def set_on_request_cb(self, callback: Callable[[int, int, BinBlock], None]):
        self.on_request_cb = callback

    def request_asyc(
        self,
        topic: str,
        request_data: BinBlock,
        platform_type: Optional[PlatformType] = None,
        timeout: int = 3,
        retry_times: int = 0,
        on_success: Optional[Callable] = None,
        on_error: Optional[Callable] = None,
    ) -> bool:
        """
        向设备发送异步请求（仅加入队列，不直接发送）

        Args:
            topic: 主题名称
            request_data: 请求数据
            platform_type: 平台类型
            timeout: 超时时间
            retry_times: 重试次数
            on_success: 请求成功回调
            on_error: 请求失败回调

        Returns:
            请求结果
            True - 请求成功加入队列
            False - 请求失败（参数错误等）
        """
        # 判断是否为请求
        if not request_data.is_request():
            return False

        # 若是请求则首先增加设备下面的服务器请求的会话ID
        with self._request_lock:
            self.msg_request_session_id.setdefault(request_data.devid, 0)
            request_data.sessionId = self.msg_request_session_id[request_data.devid]
            self.msg_request_session_id[request_data.devid] += 1

            # 设置消息
            request = BinBlockRequest(
                data=request_data,
                timeout=timeout,
                retry_times=retry_times,
                on_success=on_success,
                on_error=on_error,
                last_send_time=0,
                current_retry=0,
                topic=topic,
                platform_type=platform_type,
                is_executing=False,
            )

            # 添加到请求队列，使用OrderedDict保证顺序
            self.msg_request_map.setdefault(request_data.devid, OrderedDict())
            self.msg_request_map[request_data.devid][request_data.sessionId] = request
        return True

    def request_syc(
        self,
        topic: str,
        request_data: BinBlock,
        platform_type: Optional[PlatformType] = None,
        timeout: int = 10,
        retry_times: int = 0,
    ) -> Optional[dict | None]:
        """
        向设备发送同步请求

        Args:
            topic: 主题名称
            request_data: 请求数据
            platform_type: 平台类型
            timeout: 超时时间
            retry_times: 重试次数

        Returns:
            请求结果
            None - 请求失败
        """
        result = None
        result_event = threading.Event()

        def on_success(req: BinBlockRequest, resp: BinBlock):
            nonlocal result
            try:
                result = resp.decode_data(req.data)
            except Exception as e:
                result = {"result": "decode_error", "error": str(e)}
            result_event.set()

        def on_error(req_data: BinBlock):
            nonlocal result
            result = {"result": "error", "error": "请求超时或失败"}
            result_event.set()

        # 发送异步请求
        success = self.request_asyc(topic, request_data, platform_type, timeout, retry_times, on_success, on_error)
        if not success:
            return None

        # 等待结果
        if result_event.wait(timeout + retry_times * timeout + 1):
            return result
        else:
            result = {"result": "error", "error": "请求超时或失败"}
            return result

    def batch_get_device_state(self, device_list: list[tuple[int, str]]):
        """
        批量查询设备在线状态

        Args:
            device_ids: 设备 ID 列表

        Returns:
            dict[int, int]: {device_id: 1 在线 / 0 离线}
        """
        # 1. 拆表
        emqx_devices: list[tuple[int, str]] = []
        ali_devices: list[tuple[int, str]] = []

        for did, pk in device_list:
            (emqx_devices if pk.startswith("wx") else ali_devices).append((did, pk))

        emqx_result: dict[int, int] = self._emqx_client.batch_get_device_state(emqx_devices)
        ali_result: dict[int, int] = self._ali_client.batch_get_device_state(ali_devices)

        return {**emqx_result, **ali_result}

    def set_on_message_callback(self, callback: Callable[[str, Union[bytes, bytearray, str], int, str], None]):
        self.on_message_callback = callback