#!/usr/bin/env python
"""
IoT客户端
合并IoTPlatformManager和AmqpClient功能，提供统一的设备通信接口
"""

import base64
import logging
import re
from typing import Callable, Optional, Union
from iot_client.platform.amqp import AmqpConfig, AmqpClient
from iot_client.platform.alibabacloud_client import AliIoTPlatform


class AliMQTTClient:
    """
    IoT客户端类
    合并IoTPlatformManager和AmqpClient功能，提供统一的设备通信接口
    """

    def __init__(self, config: AmqpConfig, topic_filters: list[str], logger: Optional[logging.Logger] = None):
        """
        初始化IoT客户端

        Args:
            config: AMQP配置
            topic_filters: 主题过滤器，正则表达式
            logger: 日志记录器，如果为None则使用默认日志记录器
        """
        self.config = config
        self.logger = logger

        # 创建阿里云物联网平台管理器实例
        self.ali_iot_platform = AliIoTPlatform(
            access_key_id=config.access_key,
            access_key_secret=config.access_secret,
            iot_instance_id=config.iot_instance_id,
        )

        # 创建AMQP客户端
        self.amqp_client = AmqpClient(config, self.logger)

        # 设置消息处理函数
        self.amqp_client.set_message_handler(self.__message_handler)

        # 连接状态
        self.connected: bool = False

        # 主题过滤器
        self.topic_filters: list[str] = topic_filters

        self.on_message_handler: Optional[Callable] = None

    def start(self, topic: str = "/topic/#", auto_reconnect: bool = True):
        """
        启动IoT客户端

        Args:
            topic: 订阅的主题
            auto_reconnect: 是否自动重连
        """
        self.amqp_client.start(topic, auto_reconnect)
        self.connected = True
        self.logger.info("IoT客户端已启动")

    def stop(self):
        """停止IoT客户端"""
        self.amqp_client.stop()
        self.connected = False
        self.logger.info("IoT客户端已停止")

    def __message_handler(self, frame):
        """
        消息处理函数

        Args:
            frame: 消息帧
        """
        if frame.cmd == "MESSAGE":
            # 主题过滤：正则表达式
            original_headers = frame.original_headers
            topic = original_headers["topic"]

            # 检查是否匹配任何过滤主题
            matched = False
            for filter in self.topic_filters:
                if re.match(filter, topic):
                    matched = True
                    break

            if matched:
                qos = original_headers["qos"]
                message_id = original_headers["message-id"]
                subscription = original_headers["subscription"]
                generateTime = original_headers["generateTime"]
                base64_body = frame.body
                bytes_body = base64.b64decode(base64_body)
                device_secret = topic.split("/")[1]
                device_name = topic.split("/")[2]
                device_id = int(device_name)
                payload = bytes_body
                if self.on_message_handler:
                    self.on_message_handler(topic, payload, qos, client_id=device_id)

    def write_message(self, topic: str, payload: Union[bytes, bytearray, str]) -> bool:
        """
        向设备发送消息

        Args:
            topic: 主题
            payload: 消息

        Returns:
            True - 发送成功
            False - 发送失败
        """
        # 发送消息
        result = self.ali_iot_platform.publish_message_by_topic(topic_full_name=topic, message=payload)
        self.logger.info(f"阿里云消息发布成功到主题: {topic}")
        # print(result)

        return result != None

    def set_on_message_handler(self, handler: Callable):
        self.on_message_handler = handler

    def is_connected(self) -> bool:
        return self.connected

    def batch_get_device_state(
        self,
        device_list: list[tuple[int, str]],
        *,
        page_size: int = 50,
    ) -> dict[int, int]:
        """
        批量获取设备状态（阿里云 IoT）

        Args:
            device_list: 设备列表 [(device_id, product_key)]
            page_size: 每批最多查询多少台设备，默认 50，阿里云接口上限 50

        Returns:
            dict[int, int]: {device_id: 1(在线) / 0(离线)}
        """
        result: dict[int, int] = {}
        if len(device_list) == 0:
            return result

        # 按 page_size 切片，分批调用阿里云接口
        for offset in range(0, len(device_list), page_size):
            slice_ = device_list[offset : offset + page_size]

            # 组装阿里云要求的参数
            device_params = [{"ProductKey": pk, "DeviceName": str(did)} for did, pk in slice_]

            batch_resp = self.ali_iot_platform.batch_get_device_state(device_params)

            if not batch_resp.get("body", {}).get("Success"):
                # 接口失败时，这批设备全记为离线
                for did, _ in slice_:
                    result[did] = 0
                continue

            # 解析返回状态
            for status_info in batch_resp["body"]["DeviceStatusList"]["DeviceStatus"]:
                try:
                    did = int(status_info["DeviceName"])
                    result[did] = 1 if status_info["Status"] == "ONLINE" else 0
                except (KeyError, ValueError):
                    pass  # 解析失败直接跳过

        return result
