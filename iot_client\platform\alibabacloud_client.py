import os
import sys
import json
import base64
import logging
from typing import List, Dict, Any, Optional, Union
from Tea.core import TeaCore
from alibabacloud_iot20180120.client import Client as AlibabaIotClient
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_iot20180120 import models as iot_models

# 配置日志
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger("IoTPlatform")


class AliIoTPlatform:
    """
    阿里云物联网平台管理类
    提供设备管理、消息发布、属性查询等功能
    """

    def __init__(
        self,
        access_key_id: Optional[str] = None,
        access_key_secret: Optional[str] = None,
        region_id: str = "cn-shanghai",
        iot_instance_id: Optional[str] = None,
    ):
        """
        初始化物联网平台管理器

        Args:
            access_key_id: 阿里云访问密钥ID，如果为None则从环境变量获取
            access_key_secret: 阿里云访问密钥密码，如果为None则从环境变量获取
            region_id: 区域ID，默认为上海
            iot_instance_id: 物联网平台实例ID，如果为None则从环境变量获取
        """
        self.access_key_id = access_key_id or os.environ.get("ALICLOUD_ACCESS_KEY_ID")
        self.access_key_secret = access_key_secret or os.environ.get("ALICLOUD_ACCESS_KEY_SECRET")
        self.region_id = region_id
        self.iot_instance_id = iot_instance_id or os.environ.get("ALICLOUD_IOT_INSTANCE_ID")

        if not self.access_key_id or not self.access_key_secret:
            raise ValueError("访问密钥未设置，请提供access_key_id和access_key_secret或设置环境变量")

        self.client = self._create_client()
        logger.info(f"物联网平台管理器初始化完成，区域: {self.region_id}")

    def _create_client(self) -> AlibabaIotClient:
        """
        创建阿里云IoT客户端

        Returns:
            __IotClient: 阿里云IoT客户端实例
        """
        config = open_api_models.Config()
        config.access_key_id = self.access_key_id
        config.access_key_secret = self.access_key_secret
        config.region_id = self.region_id
        return AlibabaIotClient(config)

    def publish_message(
        self, product_key: str, device_name: str, topic: str, message: Union[str, Dict, Any], qos: int = 0
    ) -> Dict:
        """
        发布消息到指定设备

        Args:
            product_key: 产品ProductKey
            device_name: 设备名称
            topic: 主题名称，不包含产品Key和设备名称前缀
            message: 消息内容，可以是字符串、字典或其他可序列化对象
            qos: 消息质量，0或1

        Returns:
            Dict: 响应结果
        """
        # 构建完整主题
        topic_full_name = f"/{product_key}/{device_name}/{topic}"
        return self.publish_message_by_topic(topic_full_name, message, qos)

    def publish_message_by_topic(
        self, topic_full_name: str, message: Union[str, Dict, bytes, bytearray, Any], qos: int = 0
    ) -> Dict:
        """
        通过完整主题名称发布消息

        Args:
            topic_full_name: 完整的主题名称，格式为 /{productKey}/{deviceName}/{topic}
            message: 消息内容，可以是字符串、字典或其他可序列化对象
            qos: 消息质量，0或1

        Returns:
            Dict: 响应结果
        """
        try:
            # 处理消息内容
            if isinstance(message, (dict, list)):
                message_content = base64.b64encode(json.dumps(message).encode()).decode()
            elif isinstance(message, str):
                message_content = base64.b64encode(message.encode()).decode()
            elif isinstance(message, bytes) or isinstance(message, bytearray):
                message_content = base64.b64encode(message).decode("utf-8")
            else:
                message_content = base64.b64encode(str(message).encode()).decode()

            # 从topic_full_name中提取product_key
            parts = topic_full_name.strip("/").split("/")
            if len(parts) < 2:
                raise ValueError(f"无效的主题格式: {topic_full_name}，应为 /{productKey}/{deviceName}/...")

            product_key = parts[0]

            # 创建请求
            request = iot_models.PubRequest(
                iot_instance_id=self.iot_instance_id,
                product_key=product_key,
                message_content=message_content,
                topic_full_name=topic_full_name,
                qos=qos,
            )

            # 发送请求
            response = self.client.pub(request)
            result = TeaCore.to_map(response)
            logger.info(f"消息发布成功: {topic_full_name}")
            return result
        except Exception as e:
            logger.error(f"消息发布失败: {str(e)}")
            raise

    async def publish_message_async(
        self, product_key: str, device_name: str, topic: str, message: Union[str, Dict, Any], qos: int = 0
    ) -> Dict:
        """
        异步发布消息到指定设备

        Args:
            product_key: 产品ProductKey
            device_name: 设备名称
            topic: 主题名称，不包含产品Key和设备名称前缀
            message: 消息内容，可以是字符串、字典或其他可序列化对象
            qos: 消息质量，0或1

        Returns:
            Dict: 响应结果
        """
        # 构建完整主题
        topic_full_name = f"/{product_key}/{device_name}/{topic}"
        return await self.publish_message_by_topic_async(topic_full_name, message, qos)

    async def publish_message_by_topic_async(
        self, topic_full_name: str, message: Union[str, Dict, Any], qos: int = 0
    ) -> Dict:
        """
        通过完整主题名称异步发布消息

        Args:
            topic_full_name: 完整的主题名称，格式为 /{productKey}/{deviceName}/{topic}
            message: 消息内容，可以是字符串、字典或其他可序列化对象
            qos: 消息质量，0或1

        Returns:
            Dict: 响应结果
        """
        try:
            # 处理消息内容
            if isinstance(message, (dict, list)):
                message_content = base64.b64encode(json.dumps(message).encode()).decode()
            elif isinstance(message, str):
                message_content = base64.b64encode(message.encode()).decode()
            else:
                message_content = base64.b64encode(str(message).encode()).decode()

            # 从topic_full_name中提取product_key
            parts = topic_full_name.strip("/").split("/")
            if len(parts) < 2:
                raise ValueError(f"无效的主题格式: {topic_full_name}，应为 /{productKey}/{deviceName}/...")

            product_key = parts[0]

            # 创建请求
            request = iot_models.PubRequest(
                iot_instance_id=self.iot_instance_id,
                product_key=product_key,
                message_content=message_content,
                topic_full_name=topic_full_name,
                qos=qos,
            )

            # 发送请求
            response = await self.client.pub_async(request)
            result = TeaCore.to_map(response)
            logger.info(f"异步消息发布成功: {topic_full_name}")
            return result
        except Exception as e:
            logger.error(f"异步消息发布失败: {str(e)}")
            raise

    def get_device_status(self, product_key: str, device_name: str) -> Dict:
        """
        获取设备在线状态

        Args:
            product_key: 产品ProductKey
            device_name: 设备名称

        Returns:
            Dict: 设备状态信息
        """
        try:
            request = iot_models.GetDeviceStatusRequest(
                iot_instance_id=self.iot_instance_id, product_key=product_key, device_name=device_name
            )
            response = self.client.get_device_status(request)
            result = TeaCore.to_map(response)
            logger.info(f"获取设备状态成功: {product_key}/{device_name}")
            return result
        except Exception as e:
            logger.error(f"获取设备状态失败: {str(e)}")
            raise

    def get_device_properties(self, product_key: str, device_name: str) -> Dict:
        """
        获取设备属性

        Args:
            product_key: 产品ProductKey
            device_name: 设备名称

        Returns:
            Dict: 设备属性信息
        """
        try:
            request = iot_models.GetDevicePropertyRequest(
                iot_instance_id=self.iot_instance_id, product_key=product_key, device_name=device_name
            )
            response = self.client.get_device_property(request)
            result = TeaCore.to_map(response)
            logger.info(f"获取设备属性成功: {product_key}/{device_name}")
            return result
        except Exception as e:
            logger.error(f"获取设备属性失败: {str(e)}")
            raise

    def set_device_properties(self, product_key: str, device_name: str, properties: Dict[str, Any]) -> Dict:
        """
        设置设备属性

        Args:
            product_key: 产品ProductKey
            device_name: 设备名称
            properties: 要设置的属性，格式为 {属性标识符: 属性值}

        Returns:
            Dict: 响应结果
        """
        try:
            # 将属性转换为Base64编码的JSON字符串
            properties_json = json.dumps(properties)
            properties_base64 = base64.b64encode(properties_json.encode()).decode()

            request = iot_models.SetDevicePropertyRequest(
                iot_instance_id=self.iot_instance_id,
                product_key=product_key,
                device_name=device_name,
                items=properties_base64,
            )
            response = self.client.set_device_property(request)
            result = TeaCore.to_map(response)
            logger.info(f"设置设备属性成功: {product_key}/{device_name}")
            return result
        except Exception as e:
            logger.error(f"设置设备属性失败: {str(e)}")
            raise

    def batch_get_device_state(self, device_list: List[Dict[str, str]]) -> Dict:
        """
        批量获取设备状态

        Args:
            device_list: 设备列表，格式为 [{"ProductKey": "xxx", "DeviceName": "xxx"}, ...]

        Returns:
            Dict: 设备状态信息
        """
        try:
            # 按产品Key分组设备
            product_devices = {}
            for device in device_list:
                if "ProductKey" in device and "DeviceName" in device:
                    product_key = device["ProductKey"]
                    if product_key not in product_devices:
                        product_devices[product_key] = []
                    product_devices[product_key].append(device["DeviceName"])

            # 如果没有有效的设备信息，抛出异常
            if not product_devices:
                raise ValueError("设备列表格式不正确，需要提供ProductKey和DeviceName")

            # 如果只有一个产品，直接调用API
            if len(product_devices) == 1:
                product_key = list(product_devices.keys())[0]
                device_names = product_devices[product_key]

                # 创建请求对象
                request = iot_models.BatchGetDeviceStateRequest(
                    device_name=device_names, iot_instance_id=self.iot_instance_id, product_key=product_key
                )

                # 发送请求
                response = self.client.batch_get_device_state(request)
                result = TeaCore.to_map(response)
                logger.info(f"批量获取设备状态成功，共{len(device_names)}个设备")
                return result
            else:
                # 如果有多个产品，需要分别调用API并合并结果
                all_results = {"body": {"Success": True, "DeviceStatusList": {"DeviceStatus": []}}}

                for product_key, device_names in product_devices.items():
                    # 创建请求对象
                    request = iot_models.BatchGetDeviceStateRequest(
                        device_name=device_names, iot_instance_id=self.iot_instance_id, product_key=product_key
                    )

                    # 发送请求
                    response = self.client.batch_get_device_state(request)
                    result = TeaCore.to_map(response)

                    # 检查请求是否成功
                    if result.get("body", {}).get("Success"):
                        # 合并设备状态列表
                        device_status_list = result.get("body", {}).get("DeviceStatusList", {}).get("DeviceStatus", [])
                        all_results["body"]["DeviceStatusList"]["DeviceStatus"].extend(device_status_list)
                    else:
                        # 如果有一个请求失败，整体标记为失败
                        all_results["body"]["Success"] = False
                        all_results["body"]["ErrorMessage"] = result.get("body", {}).get("ErrorMessage", "未知错误")
                        break

                logger.info(f"批量获取设备状态成功，共{len(device_list)}个设备")
                return all_results
        except Exception as e:
            logger.error(f"批量获取设备状态失败: {str(e)}")
            raise


# 使用示例
if __name__ == "__main__":
    try:
        # 创建物联网平台管理器实例
        iot_manager = AliIoTPlatform()

        # 发布消息示例 - 使用完整主题名称
        topic_full_name = "/your_product_key/your_device_name/user/get"
        message = {"command": "getStatus"}

        result = iot_manager.publish_message_by_topic(topic_full_name=topic_full_name, message=message)
        print(f"消息发布结果: {result}")

        # 获取设备状态示例
        status = iot_manager.get_device_status(product_key="your_product_key", device_name="your_device_name")
        print(f"设备状态: {status}")

    except Exception as e:
        logger.error(f"程序执行出错: {str(e)}")
        sys.exit(1)
