#!/usr/bin/env python
"""
阿里云AMQP客户端封装
提供简单易用的接口连接阿里云IoT平台并接收消息
"""

import time
import hashlib
import hmac
import base64
import stomp
import ssl
import schedule
import threading
import os
import logging
from typing import Callable, Optional, Dict, Any

class AmqpConfig:
    """阿里云AMQP配置类"""

    def __init__(
        self,
        consumer_group_id: str = "wWYyFMzvqNPysWZWNset000100",
        client_id: str = "kafa_2024_ota",
        iot_instance_id: str = "iot-06z00bj21rcxohx",
        host: str = "iot-06z00bj21rcxohx.amqp.iothub.aliyuncs.com",
        port: int = 61614,
        access_key: str = "LTAI5t9d6VnHjFFRgJeboXfU",
        access_secret: str = "******************************",
        device_id: str = "ECycle_G070B8_001",
        use_env_vars: bool = False,
    ):
        """
        初始化AMQP配置

        Args:
            consumer_group_id: 消费组ID
            client_id: 客户端ID
            iot_instance_id: 实例ID
            host: 接入域名
            port: 端口
            access_key: AccessKey
            access_secret: AccessSecret
            device_id: 设备ID
            use_env_vars: 是否使用环境变量获取AccessKey和AccessSecret
        """
        self.consumer_group_id = consumer_group_id
        self.client_id = client_id
        self.iot_instance_id = iot_instance_id
        self.host = host
        self.port = port
        self.device_id = device_id

        # 如果使用环境变量，则从环境变量获取AccessKey和AccessSecret
        if use_env_vars:
            self.access_key = os.environ.get("ALIBABA_CLOUD_ACCESS_KEY_ID", access_key)
            self.access_secret = os.environ.get("ALIBABA_CLOUD_ACCESS_KEY_SECRET", access_secret)
        else:
            self.access_key = access_key
            self.access_secret = access_secret


class AmqpClient:
    """阿里云AMQP客户端类"""

    def __init__(self, config: AmqpConfig, logger: Optional[logging.Logger] = None):
        """
        初始化AMQP客户端

        Args:
            config: AMQP配置对象
            logger: 日志记录器，如果为None则使用默认的print输出
        """
        self.config = config
        self.logger = logger or self._setup_default_logger()
        self.conn = None
        self.message_handler = None
        self.check_thread = None
        self.is_running = False

    def _setup_default_logger(self) -> logging.Logger:
        """设置默认日志记录器"""
        logger = logging.getLogger("AmqpClient")
        logger.setLevel(logging.INFO)

        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def _current_time_millis(self) -> str:
        """获取当前时间戳（毫秒）"""
        return str(int(round(time.time() * 1000)))

    def _do_sign(self, secret: bytes, sign_content: bytes) -> str:
        """
        计算签名

        Args:
            secret: 密钥
            sign_content: 签名内容

        Returns:
            签名结果
        """
        m = hmac.new(secret, sign_content, digestmod=hashlib.sha1)
        return base64.b64encode(m.digest()).decode("utf-8")

    def _connect_and_subscribe(self):
        """连接并订阅主题"""
        # 签名方法：支持hmacmd5，hmacsha1和hmacsha256
        sign_method = "hmacsha1"
        timestamp = self._current_time_millis()

        # 组装用户名
        # 当您需要传输二进制数据时，由于STOMP协议为文本协议，需要使用base64编码参数，否则消息体可能会被截断。
        # 本示例中，userName需要按以下方法添加encode=base64参数，使服务端将消息体base64编码后再推送。
        username = (
            f"{self.config.client_id}|authMode=aksign,signMethod={sign_method},"
            f"timestamp={timestamp},authId={self.config.access_key},"
            f"iotInstanceId={self.config.iot_instance_id},"
            f"consumerGroupId={self.config.consumer_group_id},"
            f"encode=base64|"
        )

        # 组装签名内容
        sign_content = f"authId={self.config.access_key}&timestamp={timestamp}"

        # 计算签名
        password = self._do_sign(self.config.access_secret.encode("utf-8"), sign_content.encode("utf-8"))

        # 设置监听器并连接
        self.conn.set_listener("", self._create_listener())
        self.conn.connect(username, password, wait=True)

        # 清除历史连接检查任务，新建连接检查任务
        schedule.clear("conn-check")
        schedule.every(1).seconds.do(self._check_connection).tag("conn-check")

    def _create_listener(self) -> "AmqpListener":
        """创建AMQP监听器"""
        return AmqpListener(self)

    def _check_connection(self):
        """检查连接状态，如果未连接则重新连接"""
        # self.logger.debug(f"检查连接状态, is_connected: {self.conn.is_connected()}")
        if not self.conn.is_connected():
            try:
                self._connect_and_subscribe()
            except Exception as e:
                self.logger.error(f"连接断开, 重连失败: {e}")

    def _connection_check_timer(self):
        """定时检查连接状态的线程函数"""
        while self.is_running:
            schedule.run_pending()
            time.sleep(10)

    def set_message_handler(self, handler: Callable[[stomp.utils.Frame], None]):
        """
        设置消息处理函数

        Args:
            handler: 处理消息的回调函数，接收消息内容作为参数
        """
        self.message_handler = handler

    def start(self, topic: str = "/topic/#", auto_reconnect: bool = True):
        """
        启动AMQP客户端

        Args:
            topic: 订阅的主题
            auto_reconnect: 是否自动重连
        """
        if self.is_running:
            self.logger.warning("AMQP客户端已经在运行中")
            return

        self.is_running = True

        # 创建连接
        self.conn = stomp.Connection([(self.config.host, self.config.port)], heartbeats=(0, 300))
        self.conn.set_ssl(for_hosts=[(self.config.host, self.config.port)], ssl_version=ssl.PROTOCOL_TLS)

        # 设置监听器
        self.conn.set_listener("", self._create_listener())

        # 连接并订阅
        try:
            self._connect_and_subscribe()
            self.logger.info(f"成功连接到AMQP服务器并订阅主题: {topic}")
        except Exception as e:
            self.logger.error(f"连接失败: {e}")
            raise

        # 启动连接检查线程
        if auto_reconnect:
            self.check_thread = threading.Thread(target=self._connection_check_timer)
            self.check_thread.daemon = True
            self.check_thread.start()
            self.logger.info("已启动自动重连线程")

    def stop(self):
        """停止AMQP客户端"""
        self.is_running = False

        if self.conn and self.conn.is_connected():
            self.conn.disconnect()
            self.logger.info("已断开AMQP连接")

        if self.check_thread and self.check_thread.is_alive():
            self.check_thread.join(timeout=5)
            self.logger.info("已停止连接检查线程")


class AmqpListener(stomp.ConnectionListener):
    """AMQP连接监听器"""

    def __init__(self, client: AmqpClient):
        """
        初始化监听器

        Args:
            client: AMQP客户端实例
        """
        self.client = client
        self.logger = client.logger

    def on_error(self, frame):
        """错误处理"""
        self.logger.error(f'收到错误: "{frame.body}"')

    def on_message(self, frame):
        """消息处理"""
        # self.logger.info(f'收到消息: "{frame.body}"')

        # 如果设置了消息处理函数，则调用
        if self.client.message_handler:
            try:
                self.client.message_handler(frame)
            except Exception as e:
                self.logger.error(f"消息处理出错: {e}")

    def on_heartbeat_timeout(self):
        """心跳超时处理"""
        self.logger.warning("心跳超时")

    def on_connected(self, headers):
        """连接成功处理"""
        self.logger.info("成功连接到AMQP服务器")
        self.client.conn.subscribe(destination="/topic/#", id=1, ack="auto")
        self.logger.info("成功订阅主题")

    def on_disconnected(self):
        """连接断开处理"""
        self.logger.warning("AMQP连接断开")
        if self.client.is_running:
            self.client._connect_and_subscribe()


# 使用示例
if __name__ == "__main__":
    # 创建配置
    config = AmqpConfig()

    # 创建客户端
    client = AmqpClient(config)

    # 设置消息处理函数
    def message_handler(frame):
        print(f"处理消息: {frame}")

    client.set_message_handler(message_handler)

    # 启动客户端
    client.start()

    # 保持程序运行
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("正在停止...")
        client.stop()
        print("已停止")
