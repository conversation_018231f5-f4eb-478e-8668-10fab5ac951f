#!/usr/bin/env python
import paho.mqtt.client as mqtt_client
import logging
import re
from typing import Callable, Optional, Union
import requests
from requests.auth import HTTPBasicAuth
import concurrent.futures as cf
import math
import requests
from typing import Dict, List, Tuple
import random

# ---------- 工具 ----------
def build_client_id(device_id: int) -> str:
    """根据 device_id 生成 EMQX 中的 client_id"""
    return f"{device_id}|securemode=3,signmethod=hmacsha256,timestamp=1|"


def _fetch_page(
    base_url: str,
    auth: Tuple[str, str],
    headers: Dict[str, str],
    page: int,
    page_size: int,
) -> List[str]:
    """拉取一页在线客户端，返回 client_id 列表"""
    try:
        resp = requests.get(
            f"{base_url}/clients",
            auth=auth,
            headers=headers,
            params={"page": page, "limit": page_size, "conn_state": "connected"},
            timeout=10,
        )
        resp.raise_for_status()
        return [item["clientid"] for item in resp.json().get("data", [])]
    except Exception:
        return []  # 出错时返回空列表，外层继续


class EMQXConfig:
    """EMQX平台配置"""

    def __init__(
        self,
        host: str = "mqtt01.yunpusher.com",
        port: int = 1883,
        username: str = "kafang@",
        password: str = "kafang@_2025",
        client_id: str = "kafang_emqx_ota_server_",
        dash_user: str = "1ca5ee6acad17589",
        dash_password: str = "nVNHMtMMFfgKVYjqq9Bvr2cUu7e8QINT5qayi6yUG0jM",
    ):
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        random_suffix = "".join(chr(random.randint(7, 12)) for _ in range(6))
        self.client_id = client_id + random_suffix
        self.dash_url = f"http://{self.host}:{18083}"
        self.dash_api_url = f"{self.dash_url}/api/v5"
        self.dash_user = dash_user
        self.dash_password = dash_password


class EMQXMQTTClient:
    """
    IoT客户端类
    合并IoTPlatformManager和AmqpClient功能，提供统一的设备通信接口
    """

    def __init__(self, config: EMQXConfig, topic_filters: list[str], logger: Optional[logging.Logger] = None):
        """
        初始化IoT客户端

        Args:
            config: AMQP配置
            topic_filters: 主题过滤器，正则表达式
            logger: 日志记录器，如果为None则使用默认日志记录器
        """
        self.config = config
        self.logger = logger

        # 创建EMQX物联网平台客户端实例
        self.client = mqtt_client.Client(
            callback_api_version=mqtt_client.CallbackAPIVersion.VERSION1, client_id=self.config.client_id
        )

        # 连接状态
        self.connected: bool = False

        # 主题过滤器
        self.topic_filters: list[str] = topic_filters

        self.on_message_handler: Optional[Callable] = None

        self.subscribed_topics = []

    def _on_connect(self, client, userdata, flags, rc):
        """连接回调"""
        if rc == 0:
            self.connected = True
            self.logger.info("EMQX客户端连接成功")
            # 重新订阅主题
            for topic in self.subscribed_topics:
                client.subscribe(topic)
                self.logger.info(f"重新订阅主题: {topic}")
        else:
            self.logger.error(f"EMQX客户端连接失败，错误代码: {rc}")

    def _on_disconnect(self, client, userdata, rc):
        """断开连接回调"""
        self.connected = False
        self.logger.info("EMQX客户端断开连接")

    def start(self, topic: str = "/#", auto_reconnect: bool = True):
        """
        启动IoT客户端

        Args:
            topic: 订阅的主题
            auto_reconnect: 是否自动重连
        """
        self.client.username_pw_set(self.config.username, self.config.password)

        # 设置回调函数
        self.client.on_connect = self._on_connect
        self.client.on_disconnect = self._on_disconnect
        self.client.on_message = self._on_message

        # 连接到服务器
        try:
            self.client.connect(self.config.host, self.config.port, 60)
            self.client.loop_start()

            # 订阅主题
            self.subscribed_topics.append(topic)
            self.client.subscribe(topic)
            self.logger.info(f"EMQX客户端已启动，订阅主题: {topic}")

        except Exception as e:
            self.logger.error(f"EMQX客户端启动失败: {e}")

    def stop(self):
        """停止EMQX客户端"""
        if self.client:
            self.client.loop_stop()
            self.client.disconnect()
            self.connected = False
            self.logger.info("EMQX客户端已停止")

    def _on_message(self, client, userdata, msg):
        """
        消息处理函数

        Args:
            frame: 消息帧
        """
        # 主题过滤：正则表达式
        topic = msg.topic

        # 检查是否匹配任何过滤主题
        matched = False
        for filter in self.topic_filters:
            if re.match(filter, topic):
                matched = True
                break

        if matched:
            qos = msg.qos
            payload = msg.payload
            client_id = 0
            if self.on_message_handler:
                self.on_message_handler(topic, payload, qos, client_id=client_id)

    def write_message(self, topic: str, payload: Union[bytes, bytearray, str]) -> bool:
        """
        向设备发送消息

        Args:
            topic: 主题
            payload: 消息

        Returns:
            True - 发送成功
            False - 发送失败
        """
        if not self.connected:
            self.logger.error("EMQX客户端未连接")
            return False

        try:
            result = self.client.publish(topic, payload)
            if result.rc == mqtt_client.MQTT_ERR_SUCCESS:
                self.logger.info(f"消息发布成功到主题: {topic}")
                return True
            else:
                self.logger.error(f"消息发布失败，错误代码: {result.rc}")
                return False
        except Exception as e:
            self.logger.error(f"发布消息时发生错误: {e}")
            return False

    def set_on_message_handler(self, handler: Callable):
        self.on_message_handler = handler

    def is_connected(self) -> bool:
        return self.connected

    def batch_get_device_state(
        self,
        device_ids: list[int],
        *,
        page_size: int = 1_000,
        max_workers: int = 1,
    ) -> dict[int, int]:
        """
        批量查询设备在线状态

        Args:
            device_ids: 设备 ID 列表
            page_size : 每页拉取数量，最大 1 000
            max_workers: 并发页数

        Returns:
            {device_id: 1 在线 / 0 离线}
        """
        result = {}
        if len(device_ids) == 0:
            return result

        # 1. 预生成所有待查 client_id → device_id 映射
        want: dict[str, int] = {build_client_id(device[0]): device[0] for device in device_ids}
        result: dict[int, int] = {did[0]: 0 for did in device_ids}  # 默认离线
        auth = (self.config.dash_user, self.config.dash_password)
        headers = {"Content-Type": "application/json", "Accept": "application/json"}
        # 2. 计算总页数（先拉 meta）
        meta_resp = requests.get(
            f"{self.config.dash_api_url}/clients",
            auth=auth,
            headers=headers,
            params={"page": 1, "limit": page_size, "conn_state": "connected"},
            timeout=10,
        )
        meta_resp.raise_for_status()
        total = meta_resp.json().get("meta", {}).get("count", 0)
        pages = max(1, math.ceil(total / page_size))

        # 3. 并发拉所有页
        with cf.ThreadPoolExecutor(max_workers=min(max_workers, pages)) as pool:
            for cid_list in pool.map(
                lambda p: _fetch_page(self.config.dash_api_url, auth, headers, p, page_size),
                range(1, pages + 1),
            ):
                for cid in cid_list:
                    if cid in want:
                        result[want[cid]] = 1  # 在线
        return result
