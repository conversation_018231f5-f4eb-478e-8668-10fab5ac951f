"""Add device location table

Revision ID: 45d267051dd8
Revises: aedf179a0b86
Create Date: 2025-04-20 14:28:12.457853

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '45d267051dd8'
down_revision = 'aedf179a0b86'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('devices',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('device_id', sa.String(length=80), nullable=False),
    sa.Column('device_remark', sa.String(length=200), nullable=True),
    sa.Column('product_key', sa.String(length=80), nullable=False),
    sa.Column('firmware_version', sa.String(length=20), nullable=True),
    sa.Column('last_ota_time', sa.DateTime(), nullable=True),
    sa.Column('last_ota_status', sa.String(length=20), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('device_id')
    )
    op.create_table('device_locations',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('device_id', sa.String(length=50), nullable=False),
    sa.Column('latitude', sa.Float(), nullable=False),
    sa.Column('longitude', sa.Float(), nullable=False),
    sa.Column('address', sa.String(length=200), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['device_id'], ['devices.device_id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('device_parameters',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('device_id', sa.Integer(), nullable=False),
    sa.Column('param_name', sa.String(length=50), nullable=False),
    sa.Column('param_value', sa.String(length=100), nullable=True),
    sa.Column('description', sa.String(length=200), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['device_id'], ['devices.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('ota_tasks',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('device_id', sa.Integer(), nullable=False),
    sa.Column('firmware_path', sa.String(length=255), nullable=False),
    sa.Column('firmware_version', sa.String(length=20), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('progress', sa.Integer(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['device_id'], ['devices.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.drop_table('device_parameter')
    op.drop_table('device')
    op.drop_table('ota_task')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('ota_task',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('device_id', sa.INTEGER(), nullable=False),
    sa.Column('firmware_path', sa.VARCHAR(length=255), nullable=False),
    sa.Column('firmware_version', sa.VARCHAR(length=20), nullable=False),
    sa.Column('status', sa.VARCHAR(length=20), nullable=True),
    sa.Column('progress', sa.INTEGER(), nullable=True),
    sa.Column('error_message', sa.TEXT(), nullable=True),
    sa.Column('created_at', sa.DATETIME(), nullable=True),
    sa.Column('updated_at', sa.DATETIME(), nullable=True),
    sa.ForeignKeyConstraint(['device_id'], ['device.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('device',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('device_id', sa.VARCHAR(length=80), nullable=False),
    sa.Column('device_remark', sa.VARCHAR(length=200), nullable=True),
    sa.Column('product_key', sa.VARCHAR(length=80), nullable=False),
    sa.Column('firmware_version', sa.VARCHAR(length=20), nullable=True),
    sa.Column('last_ota_time', sa.DATETIME(), nullable=True),
    sa.Column('last_ota_status', sa.VARCHAR(length=20), nullable=True),
    sa.Column('created_at', sa.DATETIME(), nullable=True),
    sa.Column('updated_at', sa.DATETIME(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('device_id')
    )
    op.create_table('device_parameter',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('device_id', sa.INTEGER(), nullable=False),
    sa.Column('param_name', sa.VARCHAR(length=50), nullable=False),
    sa.Column('param_value', sa.VARCHAR(length=100), nullable=True),
    sa.Column('description', sa.VARCHAR(length=200), nullable=True),
    sa.Column('updated_at', sa.DATETIME(), nullable=True),
    sa.ForeignKeyConstraint(['device_id'], ['device.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.drop_table('ota_tasks')
    op.drop_table('device_parameters')
    op.drop_table('device_locations')
    op.drop_table('devices')
    # ### end Alembic commands ###
