"""add crc32 field to firmware table

Revision ID: 94909021d440
Revises: 
Create Date: 2025-04-17 07:44:49.993173

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '94909021d440'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('firmware', schema=None) as batch_op:
        batch_op.add_column(sa.Column('crc32', sa.String(length=8), nullable=False, server_default='00000000'))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('firmware', schema=None) as batch_op:
        batch_op.drop_column('crc32')

    # ### end Alembic commands ###
