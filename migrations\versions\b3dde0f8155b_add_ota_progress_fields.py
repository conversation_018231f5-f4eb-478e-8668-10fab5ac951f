"""add_ota_progress_fields

Revision ID: b3dde0f8155b
Revises: 5ad536ba7502
Create Date: 2025-09-18 17:15:54.203875

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'b3dde0f8155b'
down_revision = '5ad536ba7502'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('prod_time_series_data_2025_06', schema=None) as batch_op:
        batch_op.drop_index('idx_prod_time_series_data_2025_06_device_time')
        batch_op.drop_index('idx_prod_time_series_data_2025_06_time_type')

    op.drop_table('prod_time_series_data_2025_06')
    op.drop_table('prod_time_series_buffer')
    with op.batch_alter_table('prod_time_series_data_2025_09', schema=None) as batch_op:
        batch_op.drop_index('idx_prod_time_series_data_2025_09_device_time')
        batch_op.drop_index('idx_prod_time_series_data_2025_09_time_type')

    op.drop_table('prod_time_series_data_2025_09')
    with op.batch_alter_table('prod_time_series_data_2025_12', schema=None) as batch_op:
        batch_op.drop_index('idx_prod_time_series_data_2025_12_device_time')
        batch_op.drop_index('idx_prod_time_series_data_2025_12_time_type')

    op.drop_table('prod_time_series_data_2025_12')
    with op.batch_alter_table('dev_time_series_data_2025_10', schema=None) as batch_op:
        batch_op.drop_index('idx_dev_time_series_data_2025_10_device_time')
        batch_op.drop_index('idx_dev_time_series_data_2025_10_time_type')

    op.drop_table('dev_time_series_data_2025_10')
    with op.batch_alter_table('prod_time_series_data_2025_03', schema=None) as batch_op:
        batch_op.drop_index('idx_prod_time_series_data_2025_03_device_time')
        batch_op.drop_index('idx_prod_time_series_data_2025_03_time_type')

    op.drop_table('prod_time_series_data_2025_03')
    with op.batch_alter_table('prod_time_series_data_2025_08', schema=None) as batch_op:
        batch_op.drop_index('idx_prod_time_series_data_2025_08_device_time')
        batch_op.drop_index('idx_prod_time_series_data_2025_08_time_type')

    op.drop_table('prod_time_series_data_2025_08')
    with op.batch_alter_table('dev_time_series_data_2025_09', schema=None) as batch_op:
        batch_op.drop_index('idx_dev_time_series_data_2025_09_device_time')
        batch_op.drop_index('idx_dev_time_series_data_2025_09_time_type')

    op.drop_table('dev_time_series_data_2025_09')
    op.drop_table('prod_time_series_data')
    with op.batch_alter_table('dev_time_series_data_2025_12', schema=None) as batch_op:
        batch_op.drop_index('idx_dev_time_series_data_2025_12_device_time')
        batch_op.drop_index('idx_dev_time_series_data_2025_12_time_type')

    op.drop_table('dev_time_series_data_2025_12')
    with op.batch_alter_table('dev_time_series_data_2025_03', schema=None) as batch_op:
        batch_op.drop_index('idx_dev_time_series_data_2025_03_device_time')
        batch_op.drop_index('idx_dev_time_series_data_2025_03_time_type')

    op.drop_table('dev_time_series_data_2025_03')
    with op.batch_alter_table('prod_time_series_data_2025_10', schema=None) as batch_op:
        batch_op.drop_index('idx_prod_time_series_data_2025_10_device_time')
        batch_op.drop_index('idx_prod_time_series_data_2025_10_time_type')

    op.drop_table('prod_time_series_data_2025_10')
    with op.batch_alter_table('dev_time_series_data_2025_02', schema=None) as batch_op:
        batch_op.drop_index('idx_dev_time_series_data_2025_02_device_time')
        batch_op.drop_index('idx_dev_time_series_data_2025_02_time_type')

    op.drop_table('dev_time_series_data_2025_02')
    with op.batch_alter_table('dev_time_series_data_2025_11', schema=None) as batch_op:
        batch_op.drop_index('idx_dev_time_series_data_2025_11_device_time')
        batch_op.drop_index('idx_dev_time_series_data_2025_11_time_type')

    op.drop_table('dev_time_series_data_2025_11')
    with op.batch_alter_table('dev_time_series_data_2025_07', schema=None) as batch_op:
        batch_op.drop_index('idx_dev_time_series_data_2025_07_device_time')
        batch_op.drop_index('idx_dev_time_series_data_2025_07_time_type')

    op.drop_table('dev_time_series_data_2025_07')
    with op.batch_alter_table('prod_time_series_data_2025_05', schema=None) as batch_op:
        batch_op.drop_index('idx_prod_time_series_data_2025_05_device_time')
        batch_op.drop_index('idx_prod_time_series_data_2025_05_time_type')

    op.drop_table('prod_time_series_data_2025_05')
    with op.batch_alter_table('dev_time_series_data_2025_06', schema=None) as batch_op:
        batch_op.drop_index('idx_dev_time_series_data_2025_06_device_time')
        batch_op.drop_index('idx_dev_time_series_data_2025_06_time_type')

    op.drop_table('dev_time_series_data_2025_06')
    with op.batch_alter_table('dev_time_series_data_2025_05', schema=None) as batch_op:
        batch_op.drop_index('idx_dev_time_series_data_2025_05_device_time')
        batch_op.drop_index('idx_dev_time_series_data_2025_05_time_type')

    op.drop_table('dev_time_series_data_2025_05')
    with op.batch_alter_table('prod_time_series_data_2025_11', schema=None) as batch_op:
        batch_op.drop_index('idx_prod_time_series_data_2025_11_device_time')
        batch_op.drop_index('idx_prod_time_series_data_2025_11_time_type')

    op.drop_table('prod_time_series_data_2025_11')
    op.drop_table('dev_time_series_buffer')
    op.drop_table('dev_time_series_data')
    with op.batch_alter_table('dev_time_series_data_2025_04', schema=None) as batch_op:
        batch_op.drop_index('idx_dev_time_series_data_2025_04_device_time')
        batch_op.drop_index('idx_dev_time_series_data_2025_04_time_type')

    op.drop_table('dev_time_series_data_2025_04')
    with op.batch_alter_table('prod_time_series_data_2025_04', schema=None) as batch_op:
        batch_op.drop_index('idx_prod_time_series_data_2025_04_device_time')
        batch_op.drop_index('idx_prod_time_series_data_2025_04_time_type')

    op.drop_table('prod_time_series_data_2025_04')
    with op.batch_alter_table('prod_time_series_data_2025_02', schema=None) as batch_op:
        batch_op.drop_index('idx_prod_time_series_data_2025_02_device_time')
        batch_op.drop_index('idx_prod_time_series_data_2025_02_time_type')

    op.drop_table('prod_time_series_data_2025_02')
    with op.batch_alter_table('dev_time_series_data_2025_08', schema=None) as batch_op:
        batch_op.drop_index('idx_dev_time_series_data_2025_08_device_time')
        batch_op.drop_index('idx_dev_time_series_data_2025_08_time_type')

    op.drop_table('dev_time_series_data_2025_08')
    with op.batch_alter_table('prod_time_series_data_2025_07', schema=None) as batch_op:
        batch_op.drop_index('idx_prod_time_series_data_2025_07_device_time')
        batch_op.drop_index('idx_prod_time_series_data_2025_07_time_type')

    op.drop_table('prod_time_series_data_2025_07')
    with op.batch_alter_table('batch_ota_detail', schema=None) as batch_op:
        batch_op.alter_column('batch_id',
               existing_type=sa.VARCHAR(length=50),
               comment=None,
               existing_comment='关联的批次ID',
               existing_nullable=False)
        batch_op.alter_column('device_id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='设备ID',
               existing_nullable=False)
        batch_op.alter_column('device_type',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='设备类型',
               existing_nullable=True)
        batch_op.alter_column('current_firmware_crc',
               existing_type=sa.VARCHAR(length=8),
               comment=None,
               existing_comment='当前固件CRC32',
               existing_nullable=True,
               existing_server_default=sa.text('NULL::character varying'))
        batch_op.alter_column('target_firmware_id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='目标固件ID',
               existing_nullable=True)
        batch_op.alter_column('target_firmware_crc',
               existing_type=sa.VARCHAR(length=8),
               comment=None,
               existing_comment='目标固件CRC32',
               existing_nullable=True,
               existing_server_default=sa.text('NULL::character varying'))
        batch_op.alter_column('status',
               existing_type=sa.VARCHAR(length=20),
               comment=None,
               existing_comment='更新状态',
               existing_nullable=False,
               existing_server_default=sa.text("'等待中'::character varying"))
        batch_op.alter_column('stage',
               existing_type=sa.VARCHAR(length=50),
               comment=None,
               existing_comment='当前阶段',
               existing_nullable=True,
               existing_server_default=sa.text('NULL::character varying'))
        batch_op.alter_column('error_message',
               existing_type=sa.TEXT(),
               comment=None,
               existing_comment='错误信息',
               existing_nullable=True)
        batch_op.drop_index('idx_batch_ota_detail_batch_id')
        batch_op.drop_index('idx_batch_ota_detail_device_id')
        batch_op.drop_index('idx_batch_ota_detail_status')
        batch_op.drop_constraint('batch_ota_detail_target_firmware_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('batch_ota_detail_batch_id_fkey', type_='foreignkey')
        batch_op.drop_constraint('batch_ota_detail_device_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'firmware', ['target_firmware_id'], ['id'])
        batch_op.create_foreign_key(None, 'batch_ota_report', ['batch_id'], ['batch_id'])
        batch_op.create_foreign_key(None, 'device', ['device_id'], ['id'])
        batch_op.drop_table_comment(
        existing_comment='批量OTA更新详细记录'
    )

    with op.batch_alter_table('batch_ota_report', schema=None) as batch_op:
        batch_op.alter_column('batch_id',
               existing_type=sa.VARCHAR(length=50),
               comment=None,
               existing_comment='批次唯一标识',
               existing_nullable=False)
        batch_op.alter_column('total_devices',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='总设备数量',
               existing_nullable=False,
               existing_server_default=sa.text('0'))
        batch_op.alter_column('success_count',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='成功更新数量',
               existing_nullable=False,
               existing_server_default=sa.text('0'))
        batch_op.alter_column('failed_count',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='失败数量',
               existing_nullable=False,
               existing_server_default=sa.text('0'))
        batch_op.alter_column('skipped_count',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='跳过数量（无需更新）',
               existing_nullable=False,
               existing_server_default=sa.text('0'))
        batch_op.alter_column('status',
               existing_type=sa.VARCHAR(length=20),
               comment=None,
               existing_comment='批次状态',
               existing_nullable=False,
               existing_server_default=sa.text("'进行中'::character varying"))
        batch_op.drop_index('idx_batch_ota_report_batch_id')
        batch_op.drop_index('idx_batch_ota_report_started_at')
        batch_op.drop_index('idx_batch_ota_report_status')
        batch_op.drop_table_comment(
        existing_comment='批量OTA更新报表'
    )

    with op.batch_alter_table('debug_script', schema=None) as batch_op:
        batch_op.drop_index('idx_debug_script_device_id')

    with op.batch_alter_table('device', schema=None) as batch_op:
        batch_op.add_column(sa.Column('ota_in_progress', sa.Boolean(), nullable=True))
        batch_op.add_column(sa.Column('ota_start_time', sa.DateTime(), nullable=True))
        batch_op.alter_column('device_type',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='设备类型：10=V2(旧版霍尔传感器版本，黑色PCB), 50=V5(新版BL0910 10通道版本), 51=V51(新版BL0939 2通道版本)',
               existing_nullable=True)
        batch_op.drop_index('idx_device_device_id')

    with op.batch_alter_table('device_locations', schema=None) as batch_op:
        batch_op.drop_index('idx_device_locations_device_id')

    with op.batch_alter_table('device_parameter', schema=None) as batch_op:
        batch_op.drop_index('idx_device_parameter_device_id')

    with op.batch_alter_table('download_orders', schema=None) as batch_op:
        batch_op.alter_column('user_id',
               existing_type=sa.INTEGER(),
               comment='用户ID',
               existing_nullable=False)
        batch_op.alter_column('download_id',
               existing_type=sa.INTEGER(),
               comment='下载ID',
               existing_nullable=False)
        batch_op.alter_column('order_no',
               existing_type=sa.VARCHAR(length=32),
               comment='订单号',
               existing_nullable=False)
        batch_op.alter_column('amount',
               existing_type=sa.NUMERIC(precision=10, scale=2),
               comment='订单金额',
               existing_nullable=False)
        batch_op.alter_column('status',
               existing_type=sa.VARCHAR(length=20),
               comment='订单状态',
               existing_nullable=False,
               existing_server_default=sa.text("'pending'::character varying"))
        batch_op.alter_column('payment_time',
               existing_type=postgresql.TIMESTAMP(),
               comment='支付时间',
               existing_nullable=True)
        batch_op.alter_column('payment_method',
               existing_type=sa.VARCHAR(length=20),
               comment='支付方式',
               existing_nullable=True)
        batch_op.alter_column('created_at',
               existing_type=postgresql.TIMESTAMP(),
               comment='创建时间',
               existing_nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
        batch_op.alter_column('updated_at',
               existing_type=postgresql.TIMESTAMP(),
               comment='更新时间',
               existing_nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
        batch_op.drop_index('idx_download_orders_download_id')
        batch_op.drop_index('idx_download_orders_user_id')

    with op.batch_alter_table('firmware', schema=None) as batch_op:
        batch_op.alter_column('device_type',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='设备类型：10=V2(旧版霍尔传感器版本，黑色PCB), 50=V5(新版BL0910 10通道版本), 51=V51(新版BL0939 2通道版本)',
               existing_nullable=False,
               existing_server_default=sa.text('50'))

    with op.batch_alter_table('latest_firmware', schema=None) as batch_op:
        batch_op.drop_index('idx_latest_firmware_device_type')
        batch_op.drop_index('idx_latest_firmware_firmware_id')
        batch_op.drop_constraint('latest_firmware_firmware_id_fkey', type_='foreignkey')
        batch_op.create_foreign_key(None, 'firmware', ['firmware_id'], ['id'])
        batch_op.drop_table_comment(
        existing_comment='各设备类型最新固件管理表'
    )

    with op.batch_alter_table('login_logs', schema=None) as batch_op:
        batch_op.drop_index('idx_login_logs_login_time')
        batch_op.drop_index('idx_login_logs_user_id')

    with op.batch_alter_table('merchants', schema=None) as batch_op:
        batch_op.alter_column('name',
               existing_type=sa.VARCHAR(length=100),
               comment='商户名称',
               existing_nullable=False)
        batch_op.alter_column('contact_person',
               existing_type=sa.VARCHAR(length=50),
               comment='联系人',
               existing_nullable=False)
        batch_op.alter_column('contact_phone',
               existing_type=sa.VARCHAR(length=20),
               comment='联系电话',
               existing_nullable=False)
        batch_op.alter_column('contact_email',
               existing_type=sa.VARCHAR(length=100),
               comment='联系邮箱',
               existing_nullable=True)
        batch_op.alter_column('address',
               existing_type=sa.VARCHAR(length=200),
               comment='商户地址',
               existing_nullable=True)
        batch_op.alter_column('business_license',
               existing_type=sa.VARCHAR(length=100),
               comment='营业执照号',
               existing_nullable=True)
        batch_op.alter_column('tax_number',
               existing_type=sa.VARCHAR(length=100),
               comment='税号',
               existing_nullable=True)
        batch_op.alter_column('bank_name',
               existing_type=sa.VARCHAR(length=100),
               comment='开户银行',
               existing_nullable=True)
        batch_op.alter_column('bank_account',
               existing_type=sa.VARCHAR(length=50),
               comment='银行账号',
               existing_nullable=True)
        batch_op.alter_column('bank_account_name',
               existing_type=sa.VARCHAR(length=100),
               comment='开户名',
               existing_nullable=True)
        batch_op.alter_column('status',
               existing_type=sa.VARCHAR(length=20),
               comment='商户状态：正常、禁用',
               existing_nullable=True,
               existing_server_default=sa.text("'正常'::character varying"))
        batch_op.alter_column('remark',
               existing_type=sa.TEXT(),
               comment='备注',
               existing_nullable=True)
        batch_op.alter_column('created_at',
               existing_type=postgresql.TIMESTAMP(),
               comment='创建时间',
               existing_nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
        batch_op.alter_column('updated_at',
               existing_type=postgresql.TIMESTAMP(),
               comment='更新时间',
               existing_nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))

    with op.batch_alter_table('ota_task', schema=None) as batch_op:
        batch_op.alter_column('detailed_status',
               existing_type=sa.VARCHAR(length=50),
               comment=None,
               existing_comment='详细状态：等待中、初始化中、连接设备中等',
               existing_nullable=True,
               existing_server_default=sa.text("'等待中'::character varying"))
        batch_op.alter_column('stage_info',
               existing_type=sa.TEXT(),
               comment=None,
               existing_comment='当前阶段的详细信息',
               existing_nullable=True,
               existing_server_default=sa.text("''::text"))
        batch_op.alter_column('retry_count',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='当前重试次数',
               existing_nullable=True,
               existing_server_default=sa.text('0'))
        batch_op.alter_column('max_retries',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='最大允许重试次数',
               existing_nullable=True,
               existing_server_default=sa.text('3'))
        batch_op.alter_column('started_at',
               existing_type=postgresql.TIMESTAMP(),
               comment=None,
               existing_comment='任务开始执行时间',
               existing_nullable=True)
        batch_op.alter_column('completed_at',
               existing_type=postgresql.TIMESTAMP(),
               comment=None,
               existing_comment='任务完成时间（成功或失败）',
               existing_nullable=True)
        batch_op.drop_index('idx_ota_task_detailed_status')
        batch_op.drop_index('idx_ota_task_device_id')
        batch_op.drop_index('idx_ota_task_retry_count')
        batch_op.drop_index('idx_ota_task_started_at')
        batch_op.drop_index('idx_ota_task_status')

    with op.batch_alter_table('paid_downloads', schema=None) as batch_op:
        batch_op.alter_column('name',
               existing_type=sa.VARCHAR(length=100),
               comment='下载名称',
               existing_nullable=False)
        batch_op.alter_column('description',
               existing_type=sa.TEXT(),
               comment='下载描述',
               existing_nullable=True)
        batch_op.alter_column('file_path',
               existing_type=sa.VARCHAR(length=255),
               comment='文件路径',
               existing_nullable=False)
        batch_op.alter_column('file_size',
               existing_type=sa.INTEGER(),
               comment='文件大小(字节)',
               existing_nullable=True)
        batch_op.alter_column('price',
               existing_type=sa.NUMERIC(precision=10, scale=2),
               comment='价格',
               existing_nullable=False)
        batch_op.alter_column('download_count',
               existing_type=sa.INTEGER(),
               comment='下载次数',
               existing_nullable=True,
               existing_server_default=sa.text('0'))
        batch_op.alter_column('created_at',
               existing_type=postgresql.TIMESTAMP(),
               comment='创建时间',
               existing_nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
        batch_op.alter_column('updated_at',
               existing_type=postgresql.TIMESTAMP(),
               comment='更新时间',
               existing_nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))

    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.drop_index('idx_users_username')

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.create_index('idx_users_username', ['username'], unique=False)

    with op.batch_alter_table('paid_downloads', schema=None) as batch_op:
        batch_op.alter_column('updated_at',
               existing_type=postgresql.TIMESTAMP(),
               comment=None,
               existing_comment='更新时间',
               existing_nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
        batch_op.alter_column('created_at',
               existing_type=postgresql.TIMESTAMP(),
               comment=None,
               existing_comment='创建时间',
               existing_nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
        batch_op.alter_column('download_count',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='下载次数',
               existing_nullable=True,
               existing_server_default=sa.text('0'))
        batch_op.alter_column('price',
               existing_type=sa.NUMERIC(precision=10, scale=2),
               comment=None,
               existing_comment='价格',
               existing_nullable=False)
        batch_op.alter_column('file_size',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='文件大小(字节)',
               existing_nullable=True)
        batch_op.alter_column('file_path',
               existing_type=sa.VARCHAR(length=255),
               comment=None,
               existing_comment='文件路径',
               existing_nullable=False)
        batch_op.alter_column('description',
               existing_type=sa.TEXT(),
               comment=None,
               existing_comment='下载描述',
               existing_nullable=True)
        batch_op.alter_column('name',
               existing_type=sa.VARCHAR(length=100),
               comment=None,
               existing_comment='下载名称',
               existing_nullable=False)

    with op.batch_alter_table('ota_task', schema=None) as batch_op:
        batch_op.create_index('idx_ota_task_status', ['status'], unique=False)
        batch_op.create_index('idx_ota_task_started_at', ['started_at'], unique=False)
        batch_op.create_index('idx_ota_task_retry_count', ['retry_count'], unique=False)
        batch_op.create_index('idx_ota_task_device_id', ['device_id'], unique=False)
        batch_op.create_index('idx_ota_task_detailed_status', ['detailed_status'], unique=False)
        batch_op.alter_column('completed_at',
               existing_type=postgresql.TIMESTAMP(),
               comment='任务完成时间（成功或失败）',
               existing_nullable=True)
        batch_op.alter_column('started_at',
               existing_type=postgresql.TIMESTAMP(),
               comment='任务开始执行时间',
               existing_nullable=True)
        batch_op.alter_column('max_retries',
               existing_type=sa.INTEGER(),
               comment='最大允许重试次数',
               existing_nullable=True,
               existing_server_default=sa.text('3'))
        batch_op.alter_column('retry_count',
               existing_type=sa.INTEGER(),
               comment='当前重试次数',
               existing_nullable=True,
               existing_server_default=sa.text('0'))
        batch_op.alter_column('stage_info',
               existing_type=sa.TEXT(),
               comment='当前阶段的详细信息',
               existing_nullable=True,
               existing_server_default=sa.text("''::text"))
        batch_op.alter_column('detailed_status',
               existing_type=sa.VARCHAR(length=50),
               comment='详细状态：等待中、初始化中、连接设备中等',
               existing_nullable=True,
               existing_server_default=sa.text("'等待中'::character varying"))

    with op.batch_alter_table('merchants', schema=None) as batch_op:
        batch_op.alter_column('updated_at',
               existing_type=postgresql.TIMESTAMP(),
               comment=None,
               existing_comment='更新时间',
               existing_nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
        batch_op.alter_column('created_at',
               existing_type=postgresql.TIMESTAMP(),
               comment=None,
               existing_comment='创建时间',
               existing_nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
        batch_op.alter_column('remark',
               existing_type=sa.TEXT(),
               comment=None,
               existing_comment='备注',
               existing_nullable=True)
        batch_op.alter_column('status',
               existing_type=sa.VARCHAR(length=20),
               comment=None,
               existing_comment='商户状态：正常、禁用',
               existing_nullable=True,
               existing_server_default=sa.text("'正常'::character varying"))
        batch_op.alter_column('bank_account_name',
               existing_type=sa.VARCHAR(length=100),
               comment=None,
               existing_comment='开户名',
               existing_nullable=True)
        batch_op.alter_column('bank_account',
               existing_type=sa.VARCHAR(length=50),
               comment=None,
               existing_comment='银行账号',
               existing_nullable=True)
        batch_op.alter_column('bank_name',
               existing_type=sa.VARCHAR(length=100),
               comment=None,
               existing_comment='开户银行',
               existing_nullable=True)
        batch_op.alter_column('tax_number',
               existing_type=sa.VARCHAR(length=100),
               comment=None,
               existing_comment='税号',
               existing_nullable=True)
        batch_op.alter_column('business_license',
               existing_type=sa.VARCHAR(length=100),
               comment=None,
               existing_comment='营业执照号',
               existing_nullable=True)
        batch_op.alter_column('address',
               existing_type=sa.VARCHAR(length=200),
               comment=None,
               existing_comment='商户地址',
               existing_nullable=True)
        batch_op.alter_column('contact_email',
               existing_type=sa.VARCHAR(length=100),
               comment=None,
               existing_comment='联系邮箱',
               existing_nullable=True)
        batch_op.alter_column('contact_phone',
               existing_type=sa.VARCHAR(length=20),
               comment=None,
               existing_comment='联系电话',
               existing_nullable=False)
        batch_op.alter_column('contact_person',
               existing_type=sa.VARCHAR(length=50),
               comment=None,
               existing_comment='联系人',
               existing_nullable=False)
        batch_op.alter_column('name',
               existing_type=sa.VARCHAR(length=100),
               comment=None,
               existing_comment='商户名称',
               existing_nullable=False)

    with op.batch_alter_table('login_logs', schema=None) as batch_op:
        batch_op.create_index('idx_login_logs_user_id', ['user_id'], unique=False)
        batch_op.create_index('idx_login_logs_login_time', ['login_time'], unique=False)

    with op.batch_alter_table('latest_firmware', schema=None) as batch_op:
        batch_op.create_table_comment(
        '各设备类型最新固件管理表',
        existing_comment=None
    )
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('latest_firmware_firmware_id_fkey', 'firmware', ['firmware_id'], ['id'], ondelete='CASCADE')
        batch_op.create_index('idx_latest_firmware_firmware_id', ['firmware_id'], unique=False)
        batch_op.create_index('idx_latest_firmware_device_type', ['device_type'], unique=False)

    with op.batch_alter_table('firmware', schema=None) as batch_op:
        batch_op.alter_column('device_type',
               existing_type=sa.INTEGER(),
               comment='设备类型：10=V2(旧版霍尔传感器版本，黑色PCB), 50=V5(新版BL0910 10通道版本), 51=V51(新版BL0939 2通道版本)',
               existing_nullable=False,
               existing_server_default=sa.text('50'))

    with op.batch_alter_table('download_orders', schema=None) as batch_op:
        batch_op.create_index('idx_download_orders_user_id', ['user_id'], unique=False)
        batch_op.create_index('idx_download_orders_download_id', ['download_id'], unique=False)
        batch_op.alter_column('updated_at',
               existing_type=postgresql.TIMESTAMP(),
               comment=None,
               existing_comment='更新时间',
               existing_nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
        batch_op.alter_column('created_at',
               existing_type=postgresql.TIMESTAMP(),
               comment=None,
               existing_comment='创建时间',
               existing_nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
        batch_op.alter_column('payment_method',
               existing_type=sa.VARCHAR(length=20),
               comment=None,
               existing_comment='支付方式',
               existing_nullable=True)
        batch_op.alter_column('payment_time',
               existing_type=postgresql.TIMESTAMP(),
               comment=None,
               existing_comment='支付时间',
               existing_nullable=True)
        batch_op.alter_column('status',
               existing_type=sa.VARCHAR(length=20),
               comment=None,
               existing_comment='订单状态',
               existing_nullable=False,
               existing_server_default=sa.text("'pending'::character varying"))
        batch_op.alter_column('amount',
               existing_type=sa.NUMERIC(precision=10, scale=2),
               comment=None,
               existing_comment='订单金额',
               existing_nullable=False)
        batch_op.alter_column('order_no',
               existing_type=sa.VARCHAR(length=32),
               comment=None,
               existing_comment='订单号',
               existing_nullable=False)
        batch_op.alter_column('download_id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='下载ID',
               existing_nullable=False)
        batch_op.alter_column('user_id',
               existing_type=sa.INTEGER(),
               comment=None,
               existing_comment='用户ID',
               existing_nullable=False)

    with op.batch_alter_table('device_parameter', schema=None) as batch_op:
        batch_op.create_index('idx_device_parameter_device_id', ['device_id'], unique=False)

    with op.batch_alter_table('device_locations', schema=None) as batch_op:
        batch_op.create_index('idx_device_locations_device_id', ['device_id'], unique=False)

    with op.batch_alter_table('device', schema=None) as batch_op:
        batch_op.create_index('idx_device_device_id', ['device_id'], unique=False)
        batch_op.alter_column('device_type',
               existing_type=sa.INTEGER(),
               comment='设备类型：10=V2(旧版霍尔传感器版本，黑色PCB), 50=V5(新版BL0910 10通道版本), 51=V51(新版BL0939 2通道版本)',
               existing_nullable=True)
        batch_op.drop_column('ota_start_time')
        batch_op.drop_column('ota_in_progress')

    with op.batch_alter_table('debug_script', schema=None) as batch_op:
        batch_op.create_index('idx_debug_script_device_id', ['device_id'], unique=False)

    with op.batch_alter_table('batch_ota_report', schema=None) as batch_op:
        batch_op.create_table_comment(
        '批量OTA更新报表',
        existing_comment=None
    )
        batch_op.create_index('idx_batch_ota_report_status', ['status'], unique=False)
        batch_op.create_index('idx_batch_ota_report_started_at', ['started_at'], unique=False)
        batch_op.create_index('idx_batch_ota_report_batch_id', ['batch_id'], unique=False)
        batch_op.alter_column('status',
               existing_type=sa.VARCHAR(length=20),
               comment='批次状态',
               existing_nullable=False,
               existing_server_default=sa.text("'进行中'::character varying"))
        batch_op.alter_column('skipped_count',
               existing_type=sa.INTEGER(),
               comment='跳过数量（无需更新）',
               existing_nullable=False,
               existing_server_default=sa.text('0'))
        batch_op.alter_column('failed_count',
               existing_type=sa.INTEGER(),
               comment='失败数量',
               existing_nullable=False,
               existing_server_default=sa.text('0'))
        batch_op.alter_column('success_count',
               existing_type=sa.INTEGER(),
               comment='成功更新数量',
               existing_nullable=False,
               existing_server_default=sa.text('0'))
        batch_op.alter_column('total_devices',
               existing_type=sa.INTEGER(),
               comment='总设备数量',
               existing_nullable=False,
               existing_server_default=sa.text('0'))
        batch_op.alter_column('batch_id',
               existing_type=sa.VARCHAR(length=50),
               comment='批次唯一标识',
               existing_nullable=False)

    with op.batch_alter_table('batch_ota_detail', schema=None) as batch_op:
        batch_op.create_table_comment(
        '批量OTA更新详细记录',
        existing_comment=None
    )
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key('batch_ota_detail_device_id_fkey', 'device', ['device_id'], ['id'], ondelete='CASCADE')
        batch_op.create_foreign_key('batch_ota_detail_batch_id_fkey', 'batch_ota_report', ['batch_id'], ['batch_id'], ondelete='CASCADE')
        batch_op.create_foreign_key('batch_ota_detail_target_firmware_id_fkey', 'firmware', ['target_firmware_id'], ['id'], ondelete='SET NULL')
        batch_op.create_index('idx_batch_ota_detail_status', ['status'], unique=False)
        batch_op.create_index('idx_batch_ota_detail_device_id', ['device_id'], unique=False)
        batch_op.create_index('idx_batch_ota_detail_batch_id', ['batch_id'], unique=False)
        batch_op.alter_column('error_message',
               existing_type=sa.TEXT(),
               comment='错误信息',
               existing_nullable=True)
        batch_op.alter_column('stage',
               existing_type=sa.VARCHAR(length=50),
               comment='当前阶段',
               existing_nullable=True,
               existing_server_default=sa.text('NULL::character varying'))
        batch_op.alter_column('status',
               existing_type=sa.VARCHAR(length=20),
               comment='更新状态',
               existing_nullable=False,
               existing_server_default=sa.text("'等待中'::character varying"))
        batch_op.alter_column('target_firmware_crc',
               existing_type=sa.VARCHAR(length=8),
               comment='目标固件CRC32',
               existing_nullable=True,
               existing_server_default=sa.text('NULL::character varying'))
        batch_op.alter_column('target_firmware_id',
               existing_type=sa.INTEGER(),
               comment='目标固件ID',
               existing_nullable=True)
        batch_op.alter_column('current_firmware_crc',
               existing_type=sa.VARCHAR(length=8),
               comment='当前固件CRC32',
               existing_nullable=True,
               existing_server_default=sa.text('NULL::character varying'))
        batch_op.alter_column('device_type',
               existing_type=sa.INTEGER(),
               comment='设备类型',
               existing_nullable=True)
        batch_op.alter_column('device_id',
               existing_type=sa.INTEGER(),
               comment='设备ID',
               existing_nullable=False)
        batch_op.alter_column('batch_id',
               existing_type=sa.VARCHAR(length=50),
               comment='关联的批次ID',
               existing_nullable=False)

    op.create_table('prod_time_series_data_2025_07',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('device_id', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('timestamp', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('data_type', sa.SMALLINT(), autoincrement=False, nullable=False),
    sa.Column('power_values', postgresql.ARRAY(sa.REAL()), autoincrement=False, nullable=True),
    sa.Column('voltage', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('temperature', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('total_power', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('csq', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('ber', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('error_counts', postgresql.ARRAY(sa.INTEGER()), autoincrement=False, nullable=True),
    sa.Column('relay_state', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('relay_bits', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('zero_cross_time', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', 'timestamp', name='prod_time_series_data_2025_07_pkey')
    )
    with op.batch_alter_table('prod_time_series_data_2025_07', schema=None) as batch_op:
        batch_op.create_index('idx_prod_time_series_data_2025_07_time_type', [sa.literal_column('timestamp DESC'), 'data_type'], unique=False)
        batch_op.create_index('idx_prod_time_series_data_2025_07_device_time', ['device_id', sa.literal_column('timestamp DESC')], unique=False)

    op.create_table('dev_time_series_data_2025_08',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('device_id', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('timestamp', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('data_type', sa.SMALLINT(), autoincrement=False, nullable=False),
    sa.Column('power_values', postgresql.ARRAY(sa.REAL()), autoincrement=False, nullable=True),
    sa.Column('voltage', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('temperature', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('total_power', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('csq', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('ber', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('error_counts', postgresql.ARRAY(sa.INTEGER()), autoincrement=False, nullable=True),
    sa.Column('relay_state', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('relay_bits', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('zero_cross_time', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', 'timestamp', name='dev_time_series_data_2025_08_pkey')
    )
    with op.batch_alter_table('dev_time_series_data_2025_08', schema=None) as batch_op:
        batch_op.create_index('idx_dev_time_series_data_2025_08_time_type', [sa.literal_column('timestamp DESC'), 'data_type'], unique=False)
        batch_op.create_index('idx_dev_time_series_data_2025_08_device_time', ['device_id', sa.literal_column('timestamp DESC')], unique=False)

    op.create_table('prod_time_series_data_2025_02',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('device_id', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('timestamp', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('data_type', sa.SMALLINT(), autoincrement=False, nullable=False),
    sa.Column('power_values', postgresql.ARRAY(sa.REAL()), autoincrement=False, nullable=True),
    sa.Column('voltage', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('temperature', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('total_power', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('csq', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('ber', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('error_counts', postgresql.ARRAY(sa.INTEGER()), autoincrement=False, nullable=True),
    sa.Column('relay_state', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('relay_bits', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('zero_cross_time', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', 'timestamp', name='prod_time_series_data_2025_02_pkey')
    )
    with op.batch_alter_table('prod_time_series_data_2025_02', schema=None) as batch_op:
        batch_op.create_index('idx_prod_time_series_data_2025_02_time_type', [sa.literal_column('timestamp DESC'), 'data_type'], unique=False)
        batch_op.create_index('idx_prod_time_series_data_2025_02_device_time', ['device_id', sa.literal_column('timestamp DESC')], unique=False)

    op.create_table('prod_time_series_data_2025_04',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('device_id', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('timestamp', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('data_type', sa.SMALLINT(), autoincrement=False, nullable=False),
    sa.Column('power_values', postgresql.ARRAY(sa.REAL()), autoincrement=False, nullable=True),
    sa.Column('voltage', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('temperature', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('total_power', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('csq', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('ber', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('error_counts', postgresql.ARRAY(sa.INTEGER()), autoincrement=False, nullable=True),
    sa.Column('relay_state', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('relay_bits', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('zero_cross_time', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', 'timestamp', name='prod_time_series_data_2025_04_pkey')
    )
    with op.batch_alter_table('prod_time_series_data_2025_04', schema=None) as batch_op:
        batch_op.create_index('idx_prod_time_series_data_2025_04_time_type', [sa.literal_column('timestamp DESC'), 'data_type'], unique=False)
        batch_op.create_index('idx_prod_time_series_data_2025_04_device_time', ['device_id', sa.literal_column('timestamp DESC')], unique=False)

    op.create_table('dev_time_series_data_2025_04',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('device_id', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('timestamp', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('data_type', sa.SMALLINT(), autoincrement=False, nullable=False),
    sa.Column('power_values', postgresql.ARRAY(sa.REAL()), autoincrement=False, nullable=True),
    sa.Column('voltage', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('temperature', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('total_power', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('csq', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('ber', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('error_counts', postgresql.ARRAY(sa.INTEGER()), autoincrement=False, nullable=True),
    sa.Column('relay_state', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('relay_bits', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('zero_cross_time', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', 'timestamp', name='dev_time_series_data_2025_04_pkey')
    )
    with op.batch_alter_table('dev_time_series_data_2025_04', schema=None) as batch_op:
        batch_op.create_index('idx_dev_time_series_data_2025_04_time_type', [sa.literal_column('timestamp DESC'), 'data_type'], unique=False)
        batch_op.create_index('idx_dev_time_series_data_2025_04_device_time', ['device_id', sa.literal_column('timestamp DESC')], unique=False)

    op.create_table('dev_time_series_data',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('device_id', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('timestamp', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('data_type', sa.SMALLINT(), autoincrement=False, nullable=False),
    sa.Column('power_values', postgresql.ARRAY(sa.REAL()), autoincrement=False, nullable=True),
    sa.Column('voltage', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('temperature', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('total_power', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('csq', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('ber', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('error_counts', postgresql.ARRAY(sa.INTEGER()), autoincrement=False, nullable=True),
    sa.Column('relay_state', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('relay_bits', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('zero_cross_time', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', 'timestamp', name='dev_time_series_data_pkey')
    )
    op.create_table('dev_time_series_buffer',
    sa.Column('device_id', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('timestamp', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('data_type', sa.SMALLINT(), autoincrement=False, nullable=False),
    sa.Column('power_values', postgresql.ARRAY(sa.REAL()), autoincrement=False, nullable=True),
    sa.Column('voltage', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('temperature', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('total_power', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('csq', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('ber', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('error_counts', postgresql.ARRAY(sa.INTEGER()), autoincrement=False, nullable=True),
    sa.Column('relay_state', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('relay_bits', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('zero_cross_time', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True)
    )
    op.create_table('prod_time_series_data_2025_11',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('device_id', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('timestamp', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('data_type', sa.SMALLINT(), autoincrement=False, nullable=False),
    sa.Column('power_values', postgresql.ARRAY(sa.REAL()), autoincrement=False, nullable=True),
    sa.Column('voltage', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('temperature', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('total_power', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('csq', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('ber', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('error_counts', postgresql.ARRAY(sa.INTEGER()), autoincrement=False, nullable=True),
    sa.Column('relay_state', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('relay_bits', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('zero_cross_time', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', 'timestamp', name='prod_time_series_data_2025_11_pkey')
    )
    with op.batch_alter_table('prod_time_series_data_2025_11', schema=None) as batch_op:
        batch_op.create_index('idx_prod_time_series_data_2025_11_time_type', [sa.literal_column('timestamp DESC'), 'data_type'], unique=False)
        batch_op.create_index('idx_prod_time_series_data_2025_11_device_time', ['device_id', sa.literal_column('timestamp DESC')], unique=False)

    op.create_table('dev_time_series_data_2025_05',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('device_id', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('timestamp', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('data_type', sa.SMALLINT(), autoincrement=False, nullable=False),
    sa.Column('power_values', postgresql.ARRAY(sa.REAL()), autoincrement=False, nullable=True),
    sa.Column('voltage', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('temperature', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('total_power', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('csq', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('ber', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('error_counts', postgresql.ARRAY(sa.INTEGER()), autoincrement=False, nullable=True),
    sa.Column('relay_state', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('relay_bits', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('zero_cross_time', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', 'timestamp', name='dev_time_series_data_2025_05_pkey')
    )
    with op.batch_alter_table('dev_time_series_data_2025_05', schema=None) as batch_op:
        batch_op.create_index('idx_dev_time_series_data_2025_05_time_type', [sa.literal_column('timestamp DESC'), 'data_type'], unique=False)
        batch_op.create_index('idx_dev_time_series_data_2025_05_device_time', ['device_id', sa.literal_column('timestamp DESC')], unique=False)

    op.create_table('dev_time_series_data_2025_06',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('device_id', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('timestamp', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('data_type', sa.SMALLINT(), autoincrement=False, nullable=False),
    sa.Column('power_values', postgresql.ARRAY(sa.REAL()), autoincrement=False, nullable=True),
    sa.Column('voltage', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('temperature', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('total_power', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('csq', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('ber', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('error_counts', postgresql.ARRAY(sa.INTEGER()), autoincrement=False, nullable=True),
    sa.Column('relay_state', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('relay_bits', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('zero_cross_time', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', 'timestamp', name='dev_time_series_data_2025_06_pkey')
    )
    with op.batch_alter_table('dev_time_series_data_2025_06', schema=None) as batch_op:
        batch_op.create_index('idx_dev_time_series_data_2025_06_time_type', [sa.literal_column('timestamp DESC'), 'data_type'], unique=False)
        batch_op.create_index('idx_dev_time_series_data_2025_06_device_time', ['device_id', sa.literal_column('timestamp DESC')], unique=False)

    op.create_table('prod_time_series_data_2025_05',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('device_id', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('timestamp', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('data_type', sa.SMALLINT(), autoincrement=False, nullable=False),
    sa.Column('power_values', postgresql.ARRAY(sa.REAL()), autoincrement=False, nullable=True),
    sa.Column('voltage', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('temperature', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('total_power', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('csq', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('ber', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('error_counts', postgresql.ARRAY(sa.INTEGER()), autoincrement=False, nullable=True),
    sa.Column('relay_state', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('relay_bits', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('zero_cross_time', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', 'timestamp', name='prod_time_series_data_2025_05_pkey')
    )
    with op.batch_alter_table('prod_time_series_data_2025_05', schema=None) as batch_op:
        batch_op.create_index('idx_prod_time_series_data_2025_05_time_type', [sa.literal_column('timestamp DESC'), 'data_type'], unique=False)
        batch_op.create_index('idx_prod_time_series_data_2025_05_device_time', ['device_id', sa.literal_column('timestamp DESC')], unique=False)

    op.create_table('dev_time_series_data_2025_07',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('device_id', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('timestamp', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('data_type', sa.SMALLINT(), autoincrement=False, nullable=False),
    sa.Column('power_values', postgresql.ARRAY(sa.REAL()), autoincrement=False, nullable=True),
    sa.Column('voltage', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('temperature', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('total_power', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('csq', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('ber', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('error_counts', postgresql.ARRAY(sa.INTEGER()), autoincrement=False, nullable=True),
    sa.Column('relay_state', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('relay_bits', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('zero_cross_time', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', 'timestamp', name='dev_time_series_data_2025_07_pkey')
    )
    with op.batch_alter_table('dev_time_series_data_2025_07', schema=None) as batch_op:
        batch_op.create_index('idx_dev_time_series_data_2025_07_time_type', [sa.literal_column('timestamp DESC'), 'data_type'], unique=False)
        batch_op.create_index('idx_dev_time_series_data_2025_07_device_time', ['device_id', sa.literal_column('timestamp DESC')], unique=False)

    op.create_table('dev_time_series_data_2025_11',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('device_id', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('timestamp', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('data_type', sa.SMALLINT(), autoincrement=False, nullable=False),
    sa.Column('power_values', postgresql.ARRAY(sa.REAL()), autoincrement=False, nullable=True),
    sa.Column('voltage', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('temperature', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('total_power', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('csq', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('ber', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('error_counts', postgresql.ARRAY(sa.INTEGER()), autoincrement=False, nullable=True),
    sa.Column('relay_state', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('relay_bits', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('zero_cross_time', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', 'timestamp', name='dev_time_series_data_2025_11_pkey')
    )
    with op.batch_alter_table('dev_time_series_data_2025_11', schema=None) as batch_op:
        batch_op.create_index('idx_dev_time_series_data_2025_11_time_type', [sa.literal_column('timestamp DESC'), 'data_type'], unique=False)
        batch_op.create_index('idx_dev_time_series_data_2025_11_device_time', ['device_id', sa.literal_column('timestamp DESC')], unique=False)

    op.create_table('dev_time_series_data_2025_02',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('device_id', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('timestamp', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('data_type', sa.SMALLINT(), autoincrement=False, nullable=False),
    sa.Column('power_values', postgresql.ARRAY(sa.REAL()), autoincrement=False, nullable=True),
    sa.Column('voltage', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('temperature', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('total_power', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('csq', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('ber', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('error_counts', postgresql.ARRAY(sa.INTEGER()), autoincrement=False, nullable=True),
    sa.Column('relay_state', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('relay_bits', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('zero_cross_time', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', 'timestamp', name='dev_time_series_data_2025_02_pkey')
    )
    with op.batch_alter_table('dev_time_series_data_2025_02', schema=None) as batch_op:
        batch_op.create_index('idx_dev_time_series_data_2025_02_time_type', [sa.literal_column('timestamp DESC'), 'data_type'], unique=False)
        batch_op.create_index('idx_dev_time_series_data_2025_02_device_time', ['device_id', sa.literal_column('timestamp DESC')], unique=False)

    op.create_table('prod_time_series_data_2025_10',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('device_id', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('timestamp', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('data_type', sa.SMALLINT(), autoincrement=False, nullable=False),
    sa.Column('power_values', postgresql.ARRAY(sa.REAL()), autoincrement=False, nullable=True),
    sa.Column('voltage', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('temperature', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('total_power', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('csq', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('ber', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('error_counts', postgresql.ARRAY(sa.INTEGER()), autoincrement=False, nullable=True),
    sa.Column('relay_state', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('relay_bits', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('zero_cross_time', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', 'timestamp', name='prod_time_series_data_2025_10_pkey')
    )
    with op.batch_alter_table('prod_time_series_data_2025_10', schema=None) as batch_op:
        batch_op.create_index('idx_prod_time_series_data_2025_10_time_type', [sa.literal_column('timestamp DESC'), 'data_type'], unique=False)
        batch_op.create_index('idx_prod_time_series_data_2025_10_device_time', ['device_id', sa.literal_column('timestamp DESC')], unique=False)

    op.create_table('dev_time_series_data_2025_03',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('device_id', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('timestamp', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('data_type', sa.SMALLINT(), autoincrement=False, nullable=False),
    sa.Column('power_values', postgresql.ARRAY(sa.REAL()), autoincrement=False, nullable=True),
    sa.Column('voltage', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('temperature', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('total_power', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('csq', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('ber', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('error_counts', postgresql.ARRAY(sa.INTEGER()), autoincrement=False, nullable=True),
    sa.Column('relay_state', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('relay_bits', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('zero_cross_time', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', 'timestamp', name='dev_time_series_data_2025_03_pkey')
    )
    with op.batch_alter_table('dev_time_series_data_2025_03', schema=None) as batch_op:
        batch_op.create_index('idx_dev_time_series_data_2025_03_time_type', [sa.literal_column('timestamp DESC'), 'data_type'], unique=False)
        batch_op.create_index('idx_dev_time_series_data_2025_03_device_time', ['device_id', sa.literal_column('timestamp DESC')], unique=False)

    op.create_table('dev_time_series_data_2025_12',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('device_id', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('timestamp', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('data_type', sa.SMALLINT(), autoincrement=False, nullable=False),
    sa.Column('power_values', postgresql.ARRAY(sa.REAL()), autoincrement=False, nullable=True),
    sa.Column('voltage', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('temperature', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('total_power', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('csq', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('ber', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('error_counts', postgresql.ARRAY(sa.INTEGER()), autoincrement=False, nullable=True),
    sa.Column('relay_state', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('relay_bits', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('zero_cross_time', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', 'timestamp', name='dev_time_series_data_2025_12_pkey')
    )
    with op.batch_alter_table('dev_time_series_data_2025_12', schema=None) as batch_op:
        batch_op.create_index('idx_dev_time_series_data_2025_12_time_type', [sa.literal_column('timestamp DESC'), 'data_type'], unique=False)
        batch_op.create_index('idx_dev_time_series_data_2025_12_device_time', ['device_id', sa.literal_column('timestamp DESC')], unique=False)

    op.create_table('prod_time_series_data',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('device_id', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('timestamp', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('data_type', sa.SMALLINT(), autoincrement=False, nullable=False),
    sa.Column('power_values', postgresql.ARRAY(sa.REAL()), autoincrement=False, nullable=True),
    sa.Column('voltage', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('temperature', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('total_power', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('csq', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('ber', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('error_counts', postgresql.ARRAY(sa.INTEGER()), autoincrement=False, nullable=True),
    sa.Column('relay_state', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('relay_bits', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('zero_cross_time', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', 'timestamp', name='prod_time_series_data_pkey')
    )
    op.create_table('dev_time_series_data_2025_09',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('device_id', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('timestamp', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('data_type', sa.SMALLINT(), autoincrement=False, nullable=False),
    sa.Column('power_values', postgresql.ARRAY(sa.REAL()), autoincrement=False, nullable=True),
    sa.Column('voltage', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('temperature', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('total_power', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('csq', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('ber', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('error_counts', postgresql.ARRAY(sa.INTEGER()), autoincrement=False, nullable=True),
    sa.Column('relay_state', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('relay_bits', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('zero_cross_time', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', 'timestamp', name='dev_time_series_data_2025_09_pkey')
    )
    with op.batch_alter_table('dev_time_series_data_2025_09', schema=None) as batch_op:
        batch_op.create_index('idx_dev_time_series_data_2025_09_time_type', [sa.literal_column('timestamp DESC'), 'data_type'], unique=False)
        batch_op.create_index('idx_dev_time_series_data_2025_09_device_time', ['device_id', sa.literal_column('timestamp DESC')], unique=False)

    op.create_table('prod_time_series_data_2025_08',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('device_id', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('timestamp', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('data_type', sa.SMALLINT(), autoincrement=False, nullable=False),
    sa.Column('power_values', postgresql.ARRAY(sa.REAL()), autoincrement=False, nullable=True),
    sa.Column('voltage', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('temperature', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('total_power', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('csq', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('ber', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('error_counts', postgresql.ARRAY(sa.INTEGER()), autoincrement=False, nullable=True),
    sa.Column('relay_state', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('relay_bits', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('zero_cross_time', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', 'timestamp', name='prod_time_series_data_2025_08_pkey')
    )
    with op.batch_alter_table('prod_time_series_data_2025_08', schema=None) as batch_op:
        batch_op.create_index('idx_prod_time_series_data_2025_08_time_type', [sa.literal_column('timestamp DESC'), 'data_type'], unique=False)
        batch_op.create_index('idx_prod_time_series_data_2025_08_device_time', ['device_id', sa.literal_column('timestamp DESC')], unique=False)

    op.create_table('prod_time_series_data_2025_03',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('device_id', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('timestamp', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('data_type', sa.SMALLINT(), autoincrement=False, nullable=False),
    sa.Column('power_values', postgresql.ARRAY(sa.REAL()), autoincrement=False, nullable=True),
    sa.Column('voltage', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('temperature', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('total_power', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('csq', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('ber', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('error_counts', postgresql.ARRAY(sa.INTEGER()), autoincrement=False, nullable=True),
    sa.Column('relay_state', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('relay_bits', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('zero_cross_time', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', 'timestamp', name='prod_time_series_data_2025_03_pkey')
    )
    with op.batch_alter_table('prod_time_series_data_2025_03', schema=None) as batch_op:
        batch_op.create_index('idx_prod_time_series_data_2025_03_time_type', [sa.literal_column('timestamp DESC'), 'data_type'], unique=False)
        batch_op.create_index('idx_prod_time_series_data_2025_03_device_time', ['device_id', sa.literal_column('timestamp DESC')], unique=False)

    op.create_table('dev_time_series_data_2025_10',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('device_id', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('timestamp', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('data_type', sa.SMALLINT(), autoincrement=False, nullable=False),
    sa.Column('power_values', postgresql.ARRAY(sa.REAL()), autoincrement=False, nullable=True),
    sa.Column('voltage', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('temperature', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('total_power', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('csq', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('ber', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('error_counts', postgresql.ARRAY(sa.INTEGER()), autoincrement=False, nullable=True),
    sa.Column('relay_state', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('relay_bits', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('zero_cross_time', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', 'timestamp', name='dev_time_series_data_2025_10_pkey')
    )
    with op.batch_alter_table('dev_time_series_data_2025_10', schema=None) as batch_op:
        batch_op.create_index('idx_dev_time_series_data_2025_10_time_type', [sa.literal_column('timestamp DESC'), 'data_type'], unique=False)
        batch_op.create_index('idx_dev_time_series_data_2025_10_device_time', ['device_id', sa.literal_column('timestamp DESC')], unique=False)

    op.create_table('prod_time_series_data_2025_12',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('device_id', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('timestamp', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('data_type', sa.SMALLINT(), autoincrement=False, nullable=False),
    sa.Column('power_values', postgresql.ARRAY(sa.REAL()), autoincrement=False, nullable=True),
    sa.Column('voltage', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('temperature', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('total_power', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('csq', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('ber', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('error_counts', postgresql.ARRAY(sa.INTEGER()), autoincrement=False, nullable=True),
    sa.Column('relay_state', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('relay_bits', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('zero_cross_time', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', 'timestamp', name='prod_time_series_data_2025_12_pkey')
    )
    with op.batch_alter_table('prod_time_series_data_2025_12', schema=None) as batch_op:
        batch_op.create_index('idx_prod_time_series_data_2025_12_time_type', [sa.literal_column('timestamp DESC'), 'data_type'], unique=False)
        batch_op.create_index('idx_prod_time_series_data_2025_12_device_time', ['device_id', sa.literal_column('timestamp DESC')], unique=False)

    op.create_table('prod_time_series_data_2025_09',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('device_id', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('timestamp', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('data_type', sa.SMALLINT(), autoincrement=False, nullable=False),
    sa.Column('power_values', postgresql.ARRAY(sa.REAL()), autoincrement=False, nullable=True),
    sa.Column('voltage', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('temperature', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('total_power', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('csq', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('ber', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('error_counts', postgresql.ARRAY(sa.INTEGER()), autoincrement=False, nullable=True),
    sa.Column('relay_state', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('relay_bits', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('zero_cross_time', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', 'timestamp', name='prod_time_series_data_2025_09_pkey')
    )
    with op.batch_alter_table('prod_time_series_data_2025_09', schema=None) as batch_op:
        batch_op.create_index('idx_prod_time_series_data_2025_09_time_type', [sa.literal_column('timestamp DESC'), 'data_type'], unique=False)
        batch_op.create_index('idx_prod_time_series_data_2025_09_device_time', ['device_id', sa.literal_column('timestamp DESC')], unique=False)

    op.create_table('prod_time_series_buffer',
    sa.Column('device_id', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('timestamp', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('data_type', sa.SMALLINT(), autoincrement=False, nullable=False),
    sa.Column('power_values', postgresql.ARRAY(sa.REAL()), autoincrement=False, nullable=True),
    sa.Column('voltage', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('temperature', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('total_power', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('csq', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('ber', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('error_counts', postgresql.ARRAY(sa.INTEGER()), autoincrement=False, nullable=True),
    sa.Column('relay_state', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('relay_bits', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('zero_cross_time', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True)
    )
    op.create_table('prod_time_series_data_2025_06',
    sa.Column('id', sa.BIGINT(), autoincrement=True, nullable=False),
    sa.Column('device_id', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('timestamp', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('data_type', sa.SMALLINT(), autoincrement=False, nullable=False),
    sa.Column('power_values', postgresql.ARRAY(sa.REAL()), autoincrement=False, nullable=True),
    sa.Column('voltage', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('temperature', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('total_power', sa.REAL(), autoincrement=False, nullable=True),
    sa.Column('csq', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('ber', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('error_counts', postgresql.ARRAY(sa.INTEGER()), autoincrement=False, nullable=True),
    sa.Column('relay_state', sa.SMALLINT(), autoincrement=False, nullable=True),
    sa.Column('relay_bits', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('zero_cross_time', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', 'timestamp', name='prod_time_series_data_2025_06_pkey')
    )
    with op.batch_alter_table('prod_time_series_data_2025_06', schema=None) as batch_op:
        batch_op.create_index('idx_prod_time_series_data_2025_06_time_type', [sa.literal_column('timestamp DESC'), 'data_type'], unique=False)
        batch_op.create_index('idx_prod_time_series_data_2025_06_device_time', ['device_id', sa.literal_column('timestamp DESC')], unique=False)

    # ### end Alembic commands ###
