"""add created_by and is_active columns to paid_downloads

Revision ID: cadaa0ff9efe
Revises: 45d267051dd8
Create Date: 2025-04-21 09:25:43.113090

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'cadaa0ff9efe'
down_revision = '45d267051dd8'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('device', schema=None) as batch_op:
        batch_op.create_unique_constraint('uq_device_device_id', ['device_id'])

    with op.batch_alter_table('device_locations', schema=None) as batch_op:
        batch_op.drop_constraint('device_locations_device_FK', type_='foreignkey')
        batch_op.create_foreign_key('fk_device_locations_device_id', 'device', ['device_id'], ['device_id'])

    with op.batch_alter_table('ota_task', schema=None) as batch_op:
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=False,
               autoincrement=True)
        batch_op.alter_column('device_id',
               existing_type=sa.INTEGER(),
               nullable=False)
        batch_op.alter_column('firmware_path',
               existing_type=sa.VARCHAR(),
               nullable=False)
        batch_op.alter_column('firmware_version',
               existing_type=sa.VARCHAR(),
               nullable=False)
        batch_op.drop_constraint('ota_task_device_FK', type_='foreignkey')
        batch_op.create_foreign_key('fk_ota_task_device_id', 'device', ['device_id'], ['id'])

    with op.batch_alter_table('paid_downloads', schema=None) as batch_op:
        batch_op.add_column(sa.Column('created_by', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('is_active', sa.Boolean(), nullable=True))
        batch_op.create_foreign_key('fk_paid_downloads_created_by', 'users', ['created_by'], ['id'])

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('paid_downloads', schema=None) as batch_op:
        batch_op.drop_constraint('fk_paid_downloads_created_by', type_='foreignkey')
        batch_op.drop_column('is_active')
        batch_op.drop_column('created_by')

    with op.batch_alter_table('ota_task', schema=None) as batch_op:
        batch_op.drop_constraint('fk_ota_task_device_id', type_='foreignkey')
        batch_op.create_foreign_key('ota_task_device_FK', 'device', ['id'], ['id'])
        batch_op.alter_column('firmware_version',
               existing_type=sa.VARCHAR(),
               nullable=True)
        batch_op.alter_column('firmware_path',
               existing_type=sa.VARCHAR(),
               nullable=True)
        batch_op.alter_column('device_id',
               existing_type=sa.INTEGER(),
               nullable=True)
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=True,
               autoincrement=True)

    with op.batch_alter_table('device_locations', schema=None) as batch_op:
        batch_op.drop_constraint('fk_device_locations_device_id', type_='foreignkey')
        batch_op.create_foreign_key('device_locations_device_FK', 'device', ['id'], ['id'])

    with op.batch_alter_table('device', schema=None) as batch_op:
        batch_op.drop_constraint('uq_device_device_id', type_='unique')

    # ### end Alembic commands ###
