from datetime import datetime
from models.database import db
from sqlalchemy.dialects.postgresql import ARRAY

class Device(db.Model):
    """统一设备模型 - 包含设备基本信息、位置信息、调试脚本配置和寄存器参数"""
    __tablename__ = 'device'  # 使用标准的表名

    # 原有设备基本信息字段
    id = db.Column(db.Integer, primary_key=True)
    device_id = db.Column(db.String(80), unique=True, nullable=False)  # 设备唯一标识符
    device_remark = db.Column(db.String(200))  # 设备备注
    product_key = db.Column(db.String(80), nullable=False)
    firmware_version = db.Column(db.String(20), default="未知")
    device_type = db.Column(db.Integer, nullable=True, default=None)  # 设备类型：10=V2, 50=V5, 51=V51
    last_ota_time = db.Column(db.DateTime, default=None)
    last_ota_status = db.Column(db.String(20), default="未升级")
    ota_start_time = db.Column(db.DateTime, default=None)  # OTA开始时间
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    # 位置信息字段（来自device_locations表）
    latitude = db.Column(db.Float, default=None)  # 纬度
    longitude = db.Column(db.Float, default=None)  # 经度
    address = db.Column(db.String(200), default=None)  # 地址描述
    location_updated_at = db.Column(db.DateTime, default=None)  # 位置信息更新时间

    # 调试脚本配置字段（来自debug_script表）
    debug_enabled = db.Column(db.Boolean, default=False)  # 调试脚本是否启用
    debug_frequency = db.Column(db.Integer, default=60)  # 调试脚本执行频率（秒）
    debug_total_executions = db.Column(db.Integer, default=0)  # 总执行次数
    debug_successful_executions = db.Column(db.Integer, default=0)  # 成功执行次数
    debug_last_execution_time = db.Column(db.DateTime, default=None)  # 最后执行时间
    debug_last_execution_status = db.Column(db.String(20), default=None)  # 最后执行状态
    debug_created_at = db.Column(db.DateTime, default=None)  # 调试脚本创建时间
    debug_updated_at = db.Column(db.DateTime, default=None)  # 调试脚本更新时间

    # 寄存器参数值数组（索引对应寄存器地址）
    register_values = db.Column(ARRAY(db.String(100)), default=lambda: [])  # 寄存器值数组，索引对应寄存器地址
    register_updated_at = db.Column(db.DateTime, default=None)  # 寄存器参数最后更新时间

    @staticmethod
    def get_device_type_name(device_type_value):
        """将设备类型数值转换为对应的版本名称"""
        device_type_map = {
            10: "V2 (旧版霍尔传感器版本，黑色PCB)",
            50: "V5 (新版BL0910 10通道版本)",
            51: "V51 (新版BL0939 2通道版本)"
        }
        return device_type_map.get(device_type_value, f"未知类型 ({device_type_value})") if device_type_value else "未设置"

    @property
    def device_type_name(self):
        """获取设备类型名称"""
        return self.get_device_type_name(self.device_type)

    @property
    def has_location(self):
        """检查设备是否有位置信息"""
        return self.latitude is not None and self.longitude is not None

    @property
    def location_str(self):
        """获取位置信息字符串"""
        if self.has_location:
            return f"{self.latitude:.6f}, {self.longitude:.6f}"
        return "无位置信息"

    @property
    def register_count(self):
        """获取寄存器参数数量"""
        return len(self.register_names) if self.register_names else 0

    def get_register_parameter(self, register_name):
        """获取指定寄存器的参数信息"""
        from models.register_config import RegisterConfig

        # 查找寄存器配置
        config = RegisterConfig.get_register_by_name(register_name)
        if not config:
            return None

        # 获取对应地址的值
        value = '--'
        if (self.register_values and
            config.register_address < len(self.register_values) and
            self.register_values[config.register_address]):
            value = self.register_values[config.register_address]

        return {
            'name': config.register_name,
            'alias': config.register_alias or config.register_name,
            'data_type': config.data_type if config.data_type else 'integer',
            'value': value,
            'description': config.description or ''
        }

    def get_all_register_parameters(self):
        """获取所有寄存器参数（基于寄存器配置表）"""
        from models.register_config import RegisterConfig

        parameters = []

        # 获取所有启用的寄存器配置
        register_configs = RegisterConfig.get_all_active_registers()

        for config in register_configs:
            # 获取对应地址的值
            value = '--'
            if (self.register_values and
                config.register_address < len(self.register_values) and
                self.register_values[config.register_address]):
                value = self.register_values[config.register_address]

            param = {
                'name': config.register_name,
                'address': config.register_address,
                'alias': config.register_alias or config.register_name,
                'data_type': config.data_type if config.data_type else 'integer',
                'value': value,
                'description': config.description or '',
                'category': config.category if config.category else 'other',
                'unit': config.unit or '',
                'min_value': config.min_value,
                'max_value': config.max_value,
                'default_value': config.default_value
            }
            parameters.append(param)

        return parameters

    def set_register_value(self, register_address, value):
        """设置指定地址的寄存器值"""
        from models.register_config import RegisterConfig

        # 验证寄存器地址是否有效
        config = RegisterConfig.get_register_by_address(register_address)
        if not config:
            raise ValueError(f"寄存器地址 {register_address} 不存在")

        # 初始化数组如果为空
        if not self.register_values:
            self.register_values = []

        # 扩展数组到足够大小
        max_address = RegisterConfig.get_max_address()
        while len(self.register_values) <= max_address:
            self.register_values.append(None)

        # 设置值
        self.register_values[register_address] = str(value) if value is not None else None
        self.register_updated_at = db.func.now()

    def get_register_value(self, register_address):
        """获取指定地址的寄存器值"""
        if (not self.register_values or
            register_address >= len(self.register_values)):
            return None
        return self.register_values[register_address]

    def add_or_update_register_parameter(self, name, alias, data_type, value, description):
        """添加或更新寄存器参数（兼容性方法，建议使用set_register_value）"""
        from models.register_config import RegisterConfig

        # 查找寄存器配置
        config = RegisterConfig.get_register_by_name(name)
        if config:
            # 使用现有配置设置值
            self.set_register_value(config.register_address, value)
        else:
            # 如果配置不存在，暂时跳过（应该通过管理界面添加配置）
            pass

    def update_location(self, latitude, longitude, address=None):
        """更新设备位置信息"""
        self.latitude = latitude
        self.longitude = longitude
        if address is not None:
            self.address = address
        self.location_updated_at = datetime.now()
        self.updated_at = datetime.now()

    def update_debug_config(self, enabled=None, frequency=None):
        """更新调试脚本配置"""
        if enabled is not None:
            self.debug_enabled = enabled
        if frequency is not None:
            self.debug_frequency = frequency
        self.debug_updated_at = datetime.now()
        self.updated_at = datetime.now()

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'device_id': self.device_id,
            'device_remark': self.device_remark,
            'product_key': self.product_key,
            'firmware_version': self.firmware_version,
            'device_type': self.device_type,
            'device_type_name': self.device_type_name,
            'last_ota_time': self.last_ota_time.isoformat() if self.last_ota_time else None,
            'last_ota_status': self.last_ota_status,
            'ota_start_time': self.ota_start_time.isoformat() if self.ota_start_time else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'location': {
                'latitude': self.latitude,
                'longitude': self.longitude,
                'address': self.address,
                'has_location': self.has_location,
                'location_str': self.location_str,
                'updated_at': self.location_updated_at.isoformat() if self.location_updated_at else None
            },
            'debug': {
                'enabled': self.debug_enabled,
                'frequency': self.debug_frequency,
                'total_executions': self.debug_total_executions,
                'successful_executions': self.debug_successful_executions,
                'last_execution_time': self.debug_last_execution_time.isoformat() if self.debug_last_execution_time else None,
                'last_execution_status': self.debug_last_execution_status,
                'created_at': self.debug_created_at.isoformat() if self.debug_created_at else None,
                'updated_at': self.debug_updated_at.isoformat() if self.debug_updated_at else None
            },
            'registers': {
                'count': self.register_count,
                'parameters': self.get_all_register_parameters(),
                'updated_at': self.register_updated_at.isoformat() if self.register_updated_at else None
            }
        }