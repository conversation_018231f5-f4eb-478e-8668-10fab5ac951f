
from datetime import datetime
from models.database import db

class Firmware(db.Model):
    """固件模型"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    version = db.Column(db.String(20), nullable=False)
    file_path = db.Column(db.String(255), nullable=False)
    size = db.Column(db.Integer, nullable=False)  # 文件大小（字节）
    crc32 = db.Column(db.String(8), nullable=False)  # CRC32校验值
    device_type = db.Column(db.Integer, nullable=False, default=50)  # 设备类型：10=V2, 50=V5, 51=V51
    description = db.Column(db.Text)
    upload_time = db.Column(db.DateTime, default=datetime.now)
    download_count = db.Column(db.Integer, default=0)

    @staticmethod
    def get_device_type_name(device_type_value):
        """将设备类型数值转换为对应的版本名称"""
        device_type_map = {
            10: "V2 (旧版霍尔传感器版本，黑色PCB)",
            50: "V5 (新版BL0910 10通道版本)",
            51: "V51 (新版BL0939 2通道版本)"
        }
        return device_type_map.get(device_type_value, f"未知类型 ({device_type_value})")

    @property
    def device_type_name(self):
        """获取设备类型名称"""
        return self.get_device_type_name(self.device_type)