from datetime import datetime
from models.database import db

class LatestFirmware(db.Model):
    """各设备类型最新固件管理模型"""
    __tablename__ = 'latest_firmware'
    
    id = db.Column(db.Integer, primary_key=True)
    device_type = db.Column(db.Integer, nullable=False, unique=True)  # 设备类型：10=V2, 50=V5, 51=V51
    firmware_id = db.Column(db.Integer, db.ForeignKey('firmware.id'), nullable=False)  # 关联的固件ID
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
    updated_by = db.Column(db.String(80), nullable=True)  # 更新者
    
    # 关联关系
    firmware = db.relationship('Firmware', backref='latest_for_device_types')
    
    @staticmethod
    def get_device_type_name(device_type_value):
        """将设备类型数值转换为对应的版本名称"""
        device_type_map = {
            10: "V2 (旧版霍尔传感器版本，黑色PCB)",
            50: "V5 (新版BL0910 10通道版本)",
            51: "V51 (新版BL0939 2通道版本)"
        }
        return device_type_map.get(device_type_value, f"未知类型 ({device_type_value})")

    @property
    def device_type_name(self):
        """获取设备类型名称"""
        return self.get_device_type_name(self.device_type)
    
    @staticmethod
    def get_latest_firmware_for_device_type(device_type):
        """获取指定设备类型的最新固件"""
        latest = LatestFirmware.query.filter_by(device_type=device_type).first()
        return latest.firmware if latest else None
    
    @staticmethod
    def set_latest_firmware_for_device_type(device_type, firmware_id, updated_by=None):
        """设置指定设备类型的最新固件"""
        latest = LatestFirmware.query.filter_by(device_type=device_type).first()
        if latest:
            latest.firmware_id = firmware_id
            latest.updated_by = updated_by
            latest.updated_at = datetime.now()
        else:
            latest = LatestFirmware(
                device_type=device_type,
                firmware_id=firmware_id,
                updated_by=updated_by
            )
            db.session.add(latest)
        
        db.session.commit()
        return latest
