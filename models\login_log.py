from datetime import datetime
from models.database import db

# 新添加的LoginLog模型
class LoginLog(db.Model):
    """登录日志模型"""
    __tablename__ = 'login_logs'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.<PERSON><PERSON>, db.<PERSON>ey('users.id'), nullable=True)
    username = db.Column(db.String(80), nullable=False)
    ip_address = db.Column(db.String(50))
    login_time = db.Column(db.DateTime, default=datetime.now)
    result = db.Column(db.String(20))  # 成功/失败/登出
    
    # 关联用户
    user = db.relationship('User', backref=db.backref('login_logs', lazy=True))
    
    def __repr__(self):
        return f'<LoginLog {self.username} {self.result} {self.login_time}>'