from datetime import datetime
from models.database import db

class Merchant(db.Model):
    """商户模型"""
    __tablename__ = "merchants"
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, comment="商户名称")
    contact_person = db.Column(db.String(50), nullable=False, comment="联系人")
    contact_phone = db.Column(db.String(20), nullable=False, comment="联系电话")
    contact_email = db.Column(db.String(100), comment="联系邮箱")
    address = db.Column(db.String(200), comment="商户地址")
    business_license = db.Column(db.String(100), comment="营业执照号")
    tax_number = db.Column(db.String(100), comment="税号")
    bank_name = db.Column(db.String(100), comment="开户银行")
    bank_account = db.Column(db.String(50), comment="银行账号")
    bank_account_name = db.Column(db.String(100), comment="开户名")
    status = db.Column(db.String(20), default="正常", comment="商户状态：正常、禁用")
    remark = db.Column(db.Text, comment="备注")
    created_at = db.Column(db.DateTime, default=datetime.now, comment="创建时间")
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")
    
    def __repr__(self):
        return f"<Merchant {self.name}>" 