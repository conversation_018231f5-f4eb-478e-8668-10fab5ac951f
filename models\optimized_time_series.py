#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
优化的时序数据模型
针对大量设备并发写入和查询进行优化
"""

import os
from datetime import datetime
from typing import List, Optional, Dict, Any
from sqlalchemy import Index, text, ARRAY, SmallInteger, Integer, Float, DateTime
from sqlalchemy.dialects.postgresql import REAL
from models.database import db


# 数据类型枚举映射
DATA_TYPE_MAPPING = {
    'power': 0,
    'voltage': 1,
    'temperature': 2,
    'signal': 3,
    'error': 4,
    'relay': 5,
    'timing': 6
}

REVERSE_DATA_TYPE_MAPPING = {v: k for k, v in DATA_TYPE_MAPPING.items()}


class OptimizedTimeSeriesData(db.Model):
    """优化的时序数据模型"""
    
    __tablename__ = 'time_series_data'
    
    def __init__(self, **kwargs):
        # 动态设置表名，支持开发和生产环境分离
        if os.environ.get('FLASK_ENV') == 'development':
            self.__tablename__ = 'dev_time_series_data'
        else:
            self.__tablename__ = 'prod_time_series_data'
        super().__init__(**kwargs)
    
    # 主键（包含分区键）
    id = db.Column(db.BigInteger, primary_key=True)
    timestamp = db.Column(DateTime(timezone=True), primary_key=True, nullable=False)
    
    # 基础字段
    device_id = db.Column(db.String(50), nullable=False, index=True)
    data_type = db.Column(SmallInteger, nullable=False, index=True)
    
    # 优化的数据字段
    power_values = db.Column(ARRAY(REAL, dimensions=1), nullable=True)  # 10个功率通道
    voltage = db.Column(REAL, nullable=True)
    temperature = db.Column(REAL, nullable=True)
    total_power = db.Column(REAL, nullable=True)
    csq = db.Column(SmallInteger, nullable=True)
    ber = db.Column(SmallInteger, nullable=True)
    error_counts = db.Column(ARRAY(Integer, dimensions=1), nullable=True)  # [bl0910, short, long]
    relay_state = db.Column(SmallInteger, nullable=True)
    relay_bits = db.Column(Integer, nullable=True)
    zero_cross_time = db.Column(Integer, nullable=True)
    
    # 元数据
    created_at = db.Column(DateTime(timezone=True), default=datetime.now, nullable=False)
    
    # 优化的索引策略
    __table_args__ = (
        # 主要查询索引
        Index('idx_device_timestamp', 'device_id', 'timestamp'),
        Index('idx_timestamp_device', 'timestamp', 'device_id'),
        Index('idx_type_timestamp', 'data_type', 'timestamp'),
        Index('idx_device_type_timestamp', 'device_id', 'data_type', 'timestamp'),
        
        # 数值范围查询索引
        Index('idx_voltage_range', 'voltage', postgresql_where=text('voltage IS NOT NULL')),
        Index('idx_temperature_range', 'temperature', postgresql_where=text('temperature IS NOT NULL')),
        Index('idx_total_power_range', 'total_power', postgresql_where=text('total_power IS NOT NULL')),
        
        # 聚合查询索引
        Index('idx_hourly_agg', 'device_id', text("date_trunc('hour', timestamp)")),
        Index('idx_daily_agg', 'device_id', text("date_trunc('day', timestamp)")),
        
        # 分区表配置
        {
            'postgresql_partition_by': 'RANGE (timestamp)',
        }
    )
    
    @classmethod
    def create_from_sensor_data(cls, device_id: str, timestamp: datetime = None, **sensor_data) -> 'OptimizedTimeSeriesData':
        """
        从传感器数据创建时序记录
        
        Args:
            device_id: 设备ID
            timestamp: 时间戳，默认为当前时间
            **sensor_data: 传感器数据
            
        Returns:
            OptimizedTimeSeriesData: 时序数据记录
        """
        if timestamp is None:
            timestamp = datetime.now()
        
        # 处理功率数据
        power_values = None
        if 'channel_powers' in sensor_data and sensor_data['channel_powers']:
            power_values = sensor_data['channel_powers'][:10]  # 只取前10个通道
            # 确保数组长度为10
            while len(power_values) < 10:
                power_values.append(0.0)
        
        # 处理错误计数数据
        error_counts = None
        if any(key in sensor_data for key in ['bl0910_error_count', 'short_period_error_count', 'long_period_error_count']):
            error_counts = [
                sensor_data.get('bl0910_error_count', 0),
                sensor_data.get('short_period_error_count', 0),
                sensor_data.get('long_period_error_count', 0)
            ]
        
        # 确定数据类型
        data_type = cls._determine_data_type(sensor_data)
        
        return cls(
            device_id=device_id,
            timestamp=timestamp,
            data_type=data_type,
            power_values=power_values,
            voltage=sensor_data.get('voltage'),
            temperature=sensor_data.get('temperature'),
            total_power=sensor_data.get('total_power'),
            csq=sensor_data.get('csq'),
            ber=sensor_data.get('ber'),
            error_counts=error_counts,
            relay_state=sensor_data.get('relay_state'),
            relay_bits=sensor_data.get('relay_bits'),
            zero_cross_time=sensor_data.get('zero_cross_time')
        )
    
    @staticmethod
    def _determine_data_type(sensor_data: Dict[str, Any]) -> int:
        """根据传感器数据确定数据类型"""
        if 'channel_powers' in sensor_data or 'total_power' in sensor_data:
            return DATA_TYPE_MAPPING['power']
        elif 'voltage' in sensor_data:
            return DATA_TYPE_MAPPING['voltage']
        elif 'temperature' in sensor_data:
            return DATA_TYPE_MAPPING['temperature']
        elif 'csq' in sensor_data or 'ber' in sensor_data:
            return DATA_TYPE_MAPPING['signal']
        elif any(key in sensor_data for key in ['bl0910_error_count', 'short_period_error_count', 'long_period_error_count']):
            return DATA_TYPE_MAPPING['error']
        elif 'relay_state' in sensor_data or 'relay_bits' in sensor_data:
            return DATA_TYPE_MAPPING['relay']
        elif 'zero_cross_time' in sensor_data:
            return DATA_TYPE_MAPPING['timing']
        else:
            return DATA_TYPE_MAPPING['power']  # 默认为功率类型
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'device_id': self.device_id,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'data_type': REVERSE_DATA_TYPE_MAPPING.get(self.data_type, 'unknown'),
            'power_values': self.power_values,
            'voltage': self.voltage,
            'temperature': self.temperature,
            'total_power': self.total_power,
            'csq': self.csq,
            'ber': self.ber,
            'error_counts': self.error_counts,
            'relay_state': self.relay_state,
            'relay_bits': self.relay_bits,
            'zero_cross_time': self.zero_cross_time,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def get_power_channel_value(self, channel: int) -> Optional[float]:
        """获取指定通道的功率值"""
        if self.power_values and 0 <= channel < len(self.power_values):
            return self.power_values[channel]
        return None
    
    def get_error_count(self, error_type: str) -> Optional[int]:
        """获取指定类型的错误计数"""
        if not self.error_counts:
            return None
        
        error_index_map = {
            'bl0910': 0,
            'short_period': 1,
            'long_period': 2
        }
        
        index = error_index_map.get(error_type)
        if index is not None and index < len(self.error_counts):
            return self.error_counts[index]
        
        return None
    
    @staticmethod
    def get_table_name():
        """获取当前环境的表名"""
        if os.environ.get('FLASK_ENV') == 'development':
            return 'dev_time_series_data'
        else:
            return 'prod_time_series_data'
    
    def __repr__(self):
        data_type_name = REVERSE_DATA_TYPE_MAPPING.get(self.data_type, 'unknown')
        return f'<OptimizedTimeSeriesData {self.device_id}:{data_type_name}@{self.timestamp}>'


class TimeSeriesBuffer(db.Model):
    """时序数据缓冲表，用于批量写入优化"""
    
    __tablename__ = 'time_series_buffer'
    
    def __init__(self, **kwargs):
        # 动态设置表名
        if os.environ.get('FLASK_ENV') == 'development':
            self.__tablename__ = 'dev_time_series_buffer'
        else:
            self.__tablename__ = 'prod_time_series_buffer'
        super().__init__(**kwargs)
    
    # 缓冲表字段（简化的主键）
    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True)
    device_id = db.Column(db.String(50), nullable=False, index=True)
    timestamp = db.Column(DateTime(timezone=True), nullable=False)
    data_type = db.Column(SmallInteger, nullable=False)
    
    power_values = db.Column(ARRAY(REAL, dimensions=1), nullable=True)
    voltage = db.Column(REAL, nullable=True)
    temperature = db.Column(REAL, nullable=True)
    total_power = db.Column(REAL, nullable=True)
    csq = db.Column(SmallInteger, nullable=True)
    ber = db.Column(SmallInteger, nullable=True)
    error_counts = db.Column(ARRAY(Integer, dimensions=1), nullable=True)
    relay_state = db.Column(SmallInteger, nullable=True)
    relay_bits = db.Column(Integer, nullable=True)
    zero_cross_time = db.Column(Integer, nullable=True)
    
    created_at = db.Column(DateTime(timezone=True), default=datetime.now, nullable=False, index=True)
    
    __table_args__ = (
        Index('idx_buffer_created', 'created_at'),
        Index('idx_buffer_device', 'device_id'),
    )
    
    @classmethod
    def from_optimized_data(cls, data: OptimizedTimeSeriesData) -> 'TimeSeriesBuffer':
        """从优化时序数据创建缓冲记录"""
        return cls(
            device_id=data.device_id,
            timestamp=data.timestamp,
            data_type=data.data_type,
            power_values=data.power_values,
            voltage=data.voltage,
            temperature=data.temperature,
            total_power=data.total_power,
            csq=data.csq,
            ber=data.ber,
            error_counts=data.error_counts,
            relay_state=data.relay_state,
            relay_bits=data.relay_bits,
            zero_cross_time=data.zero_cross_time
        )
    
    def to_optimized_data(self) -> OptimizedTimeSeriesData:
        """转换为优化时序数据"""
        return OptimizedTimeSeriesData(
            device_id=self.device_id,
            timestamp=self.timestamp,
            data_type=self.data_type,
            power_values=self.power_values,
            voltage=self.voltage,
            temperature=self.temperature,
            total_power=self.total_power,
            csq=self.csq,
            ber=self.ber,
            error_counts=self.error_counts,
            relay_state=self.relay_state,
            relay_bits=self.relay_bits,
            zero_cross_time=self.zero_cross_time
        )
    
    def __repr__(self):
        return f'<TimeSeriesBuffer {self.device_id}@{self.timestamp}>'
