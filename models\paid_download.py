from models.database import db
from datetime import datetime

class PaidDownload(db.Model):
    """付费下载模型"""
    __tablename__ = 'paid_downloads'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, comment='下载名称')
    description = db.Column(db.Text, comment='下载描述')
    file_path = db.Column(db.String(255), nullable=False, comment='文件路径')
    file_size = db.Column(db.Integer, comment='文件大小(字节)')
    price = db.Column(db.Numeric(10, 2), nullable=False, comment='价格')
    download_count = db.Column(db.Integer, default=0, comment='下载次数')
    created_at = db.Column(db.DateTime, default=datetime.now, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'))  # 创建者ID
    is_active = db.Column(db.Boolean, default=True)  # 是否上架
    
    # 关联关系
    orders = db.relationship('DownloadOrder', backref='paid_download', lazy=True)
    creator = db.relationship('User', backref='created_downloads')

class DownloadOrder(db.Model):
    """下载订单模型"""
    __tablename__ = 'download_orders'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, comment='用户ID')
    download_id = db.Column(db.Integer, db.ForeignKey('paid_downloads.id'), nullable=False, comment='下载ID')
    order_no = db.Column(db.String(32), unique=True, nullable=False, comment='订单号')
    amount = db.Column(db.Numeric(10, 2), nullable=False, comment='订单金额')
    status = db.Column(db.String(20), nullable=False, default='pending', comment='订单状态')
    payment_time = db.Column(db.DateTime, comment='支付时间')
    payment_method = db.Column(db.String(20), comment='支付方式')
    created_at = db.Column(db.DateTime, default=datetime.now, comment='创建时间')
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now, comment='更新时间')
    
    # 关联关系
    user = db.relationship('User', backref='download_orders', lazy=True)