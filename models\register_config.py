#!/usr/bin/env python3
"""
寄存器配置模型
存储寄存器的名称、别名、描述等配置信息
"""

from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime
from sqlalchemy.sql import func
from models.database import db

class RegisterConfig(db.Model):
    """寄存器配置表"""
    __tablename__ = 'register_config'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # 寄存器基本信息
    register_address = Column(Integer, nullable=False, unique=True, comment='寄存器地址（数组索引）')
    register_name = Column(String(50), nullable=False, unique=True, comment='寄存器名称')
    register_alias = Column(String(100), nullable=True, comment='寄存器别名')
    description = Column(Text, nullable=True, comment='寄存器描述')
    
    # 数据类型和分类
    data_type = Column(String(20), nullable=False, default='integer', comment='数据类型')
    category = Column(String(20), nullable=False, default='other', comment='寄存器分类')
    
    # 显示配置
    unit = Column(String(20), nullable=True, comment='单位')
    min_value = Column(Integer, nullable=True, comment='最小值')
    max_value = Column(Integer, nullable=True, comment='最大值')
    default_value = Column(String(50), nullable=True, comment='默认值')
    
    # 状态和时间
    is_active = Column(Boolean, nullable=False, default=True, comment='是否启用')
    created_at = Column(DateTime, nullable=False, default=func.now(), comment='创建时间')
    updated_at = Column(DateTime, nullable=False, default=func.now(), onupdate=func.now(), comment='更新时间')
    
    def __repr__(self):
        return f'<RegisterConfig {self.register_name}({self.register_address})>'
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'register_address': self.register_address,
            'register_name': self.register_name,
            'register_alias': self.register_alias,
            'description': self.description,
            'data_type': self.data_type,
            'category': self.category,
            'unit': self.unit,
            'min_value': self.min_value,
            'max_value': self.max_value,
            'default_value': self.default_value,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def get_all_active_registers(cls):
        """获取所有启用的寄存器配置"""
        return cls.query.filter_by(is_active=True).order_by(cls.register_address).all()
    
    @classmethod
    def get_registers_by_category(cls, category):
        """根据分类获取寄存器配置"""
        return cls.query.filter_by(category=category, is_active=True).order_by(cls.register_address).all()
    
    @classmethod
    def get_register_by_name(cls, register_name):
        """根据名称获取寄存器配置"""
        return cls.query.filter_by(register_name=register_name, is_active=True).first()
    
    @classmethod
    def get_register_by_address(cls, register_address):
        """根据地址获取寄存器配置"""
        return cls.query.filter_by(register_address=register_address, is_active=True).first()
    
    @classmethod
    def get_max_address(cls):
        """获取最大寄存器地址"""
        result = db.session.query(func.max(cls.register_address)).scalar()
        return result if result is not None else -1
