#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
时序数据模型
用于存储设备的时序调试数据
"""

from datetime import datetime
from sqlalchemy import Index, text
from models.database import db


class TimeSeriesData(db.Model):
    """时序数据模型，用于存储设备的时序调试数据"""
    
    # 根据环境选择表名前缀
    __tablename__ = 'time_series_data'
    
    def __init__(self, **kwargs):
        # 动态设置表名，支持开发和生产环境分离
        import os
        if os.environ.get('FLASK_ENV') == 'development':
            self.__tablename__ = 'dev_time_series_data'
        else:
            self.__tablename__ = 'prod_time_series_data'
        super().__init__(**kwargs)
    
    id = db.Column(db.BigInteger, primary_key=True)
    
    # 基础字段
    device_id = db.Column(db.String(50), nullable=False, index=True)  # 设备ID
    timestamp = db.Column(db.DateTime, nullable=False, index=True)    # 时间戳
    data_type = db.Column(db.String(50), nullable=False, index=True)  # 数据类型
    
    # 数据字段 - 使用JSON存储灵活的数据结构
    data_value = db.Column(db.JSON, nullable=True)  # 主要数据值（JSON格式）
    
    # 常用数值字段（用于快速查询和索引）
    numeric_value = db.Column(db.Float, nullable=True, index=True)    # 数值型数据
    string_value = db.Column(db.Text, nullable=True)                  # 字符串型数据（改为Text类型避免长度限制）
    
    # 元数据
    created_at = db.Column(db.DateTime, default=datetime.now, nullable=False)
    
    # 复合索引，优化查询性能
    __table_args__ = (
        Index('idx_device_timestamp', 'device_id', 'timestamp'),
        Index('idx_device_type_timestamp', 'device_id', 'data_type', 'timestamp'),
        Index('idx_timestamp_type', 'timestamp', 'data_type'),
        # 分区键索引（按日期分区）
        Index('idx_date_device', text('DATE(timestamp)'), 'device_id'),
    )
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'device_id': self.device_id,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'data_type': self.data_type,
            'data_value': self.data_value,
            'numeric_value': self.numeric_value,
            'string_value': self.string_value,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    @staticmethod
    def get_table_name():
        """获取当前环境的表名"""
        import os
        if os.environ.get('FLASK_ENV') == 'development':
            return 'dev_time_series_data'
        else:
            return 'prod_time_series_data'
    
    def __repr__(self):
        return f'<TimeSeriesData {self.device_id}:{self.data_type}@{self.timestamp}>'


class TimeSeriesDataBatch(db.Model):
    """时序数据批次模型，用于批量操作的事务管理"""
    
    __tablename__ = 'time_series_batch'
    
    def __init__(self, **kwargs):
        # 动态设置表名
        import os
        if os.environ.get('FLASK_ENV') == 'development':
            self.__tablename__ = 'dev_time_series_batch'
        else:
            self.__tablename__ = 'prod_time_series_batch'
        super().__init__(**kwargs)
    
    id = db.Column(db.BigInteger, primary_key=True)
    device_id = db.Column(db.String(50), nullable=False, index=True)
    batch_timestamp = db.Column(db.DateTime, nullable=False, index=True)
    data_count = db.Column(db.Integer, default=0)
    status = db.Column(db.String(20), default='pending')  # pending, completed, failed
    error_message = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.now)
    completed_at = db.Column(db.DateTime, nullable=True)
    
    def __repr__(self):
        return f'<TimeSeriesDataBatch {self.device_id}@{self.batch_timestamp}>'
