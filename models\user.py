from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from models.database import db
from datetime import datetime


class User(UserMixin, db.Model):
    """用户模型"""

    __tablename__ = "users"

    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=True)
    password_hash = db.Column(db.String(256))  # 增加长度以适应新的哈希值
    is_admin = db.Column(db.<PERSON>, default=False)
    active = db.Column(db.<PERSON><PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

    def set_password(self, password):
        """设置密码"""
        # 使用pbkdf2:sha256方法进行密码哈希
        self.password_hash = generate_password_hash(password, method='pbkdf2:sha256')

    def check_password(self, password):
        """验证密码"""
        return check_password_hash(self.password_hash, password)

    def __repr__(self):
        return f"<User {self.username}>"
