# Flask核心框架
Flask==3.1.0
Werkzeug==3.1.3

# Flask扩展
Flask-SQLAlchemy==3.1.1
Flask-WTF==1.2.1
Flask-Login==0.6.3
Flask-Migrate==4.0.5
Flask-SocketIO==5.3.6
Flask-Limiter==3.12
Flask-Bootstrap==3.3.7.1

# 数据库相关
SQLAlchemy==2.0.23
psycopg2==2.9.10
alembic==1.15.2

# 表单和验证
WTForms==3.1.1

# 网络和通信
eventlet==0.39.1
stomp.py==8.2.0
pika==1.3.2
paho-mqtt==2.1.0
requests==2.32.3

# 任务调度和重试
schedule==1.2.2
tenacity==9.1.2
APScheduler==3.11.0

# 数据处理
pandas==2.2.3
numpy==2.2.5
openpyxl==3.1.5

# 系统监控
psutil==7.0.0

# 加密和安全
cryptography==44.0.2
pyOpenSSL==25.0.0

# 阿里云IoT
alibabacloud-iot20180120==5.1.0
alibabacloud_tea_openapi==0.3.13
alibabacloud_tea_console==0.0.1

# AI和机器学习
openai==1.78.0

# 支付相关
python-alipay-sdk==3.3.0

# 工具库
python-dotenv==1.0.0
crcmod==1.7
construct==2.10.70
qrcode==8.2
pillow==11.3.0

# 开发和测试工具
pytest==8.3.5
black==25.1.0
pylint==3.3.6
isort==6.0.1

# 其他依赖
click==8.1.8
Jinja2==3.1.6
MarkupSafe==3.0.2
itsdangerous==2.2.0
blinker==1.9.0
python-dateutil==2.9.0.post0
pytz==2025.2
six==1.17.0
certifi==2025.1.31
urllib3==2.4.0
charset-normalizer==3.4.1
idna==3.10