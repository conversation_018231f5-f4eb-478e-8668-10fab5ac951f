from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app
from flask_login import login_user, logout_user, login_required, current_user
from models.database import db
from models.user import User
from models.login_log import LoginLog
from utils.logger import setup_logging
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_wtf import FlaskForm
from wtforms import StringField, PasswordField, validators
from utils.captcha import generate_captcha, verify_captcha

auth_bp = Blueprint('auth', __name__)
logger = setup_logging()

# limiter = Limiter(
#     app=app,
#     key_func=get_remote_address,
#     default_limits=["200 per day", "50 per hour"]
# )

class RegistrationForm(FlaskForm):
    username = StringField('用户名', [validators.Length(min=4, max=25)])
    email = StringField('邮箱', [validators.Email()])
    password = PasswordField('密码', [validators.Length(min=6)])
    confirm_password = PasswordField('确认密码', [validators.EqualTo('password')])

@auth_bp.route('/login', methods=['GET', 'POST'])
# @limiter.limit("5 per minute")
def login():
    """登录页面"""
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))
    
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        
        if not username or not password:
            flash('请输入用户名和密码', 'danger')
            return redirect(url_for('auth.login'))
        
        user = User.query.filter_by(username=username).first()
        
        if user and user.check_password(password):
            if not user.active:
                flash('账号已被禁用，请联系管理员', 'danger')
                return redirect(url_for('auth.login'))
                
            login_user(user)
            login_log = LoginLog(user_id=user.id, username=username, ip_address=request.remote_addr, result="成功")
            db.session.add(login_log)
            db.session.commit()
            
            flash(f'欢迎回来，{user.username}！', 'success')
            return redirect(url_for('main.index'))
        else:
            login_log = LoginLog(user_id=None, username=username, ip_address=request.remote_addr, result="失败")
            db.session.add(login_log)
            db.session.commit()
            flash('用户名或密码错误，请重试', 'danger')
    
    return render_template('login.html')

@auth_bp.route('/logout')
@login_required
def logout():
    """登出"""
    login_log = LoginLog(user_id=current_user.id, username=current_user.username, ip_address=request.remote_addr, result="登出")
    db.session.add(login_log)
    db.session.commit()
    logout_user()
    flash('已登出', 'info')
    return redirect(url_for('auth.login'))

@auth_bp.route('/captcha')
def get_captcha():
    """获取验证码"""
    captcha = generate_captcha()
    return jsonify({'captcha': captcha})

@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    """注册页面"""
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))
    
    form = RegistrationForm()
    if request.method == 'POST':
        if form.validate_on_submit():
            # 验证验证码
            captcha_input = request.form.get('captcha')
            if not verify_captcha(captcha_input):
                flash('验证码错误，请重新输入', 'danger')
                return redirect(url_for('auth.register'))
            
            # 检查用户名是否已存在
            if User.query.filter_by(username=form.username.data).first():
                flash('用户名已被注册，请更换其他用户名', 'danger')
                return redirect(url_for('auth.register'))
            
            # 检查邮箱是否已存在
            if User.query.filter_by(email=form.email.data).first():
                flash('该邮箱已被注册，请使用其他邮箱地址', 'danger')
                return redirect(url_for('auth.register'))
            
            try:
                # 创建新用户
                user = User(
                    username=form.username.data,
                    email=form.email.data,
                    active=True  # 设置用户为激活状态
                )
                user.set_password(form.password.data)
                
                db.session.add(user)
                db.session.commit()
                
                # 记录注册日志
                logger.info(f'新用户注册成功: {user.username}, 邮箱: {user.email}')
                
                flash('注册成功！请使用您的账号密码登录系统', 'success')
                return redirect(url_for('auth.login'))
            except Exception as e:
                db.session.rollback()
                logger.error(f'用户注册失败: {str(e)}')
                flash('系统错误，注册失败，请稍后重试', 'danger')
                return redirect(url_for('auth.register'))
        else:
            for field, errors in form.errors.items():
                for error in errors:
                    if field == 'username':
                        flash('用户名格式错误：' + error, 'danger')
                    elif field == 'email':
                        flash('邮箱格式错误：' + error, 'danger')
                    elif field == 'password':
                        flash('密码格式错误：' + error, 'danger')
                    elif field == 'confirm_password':
                        flash('两次输入的密码不一致', 'danger')
                    else:
                        flash(f'{field}: {error}', 'danger')
    
    return render_template('register.html', form=form)