#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
自动OTA升级管理路由
提供自动OTA升级功能的配置和状态管理接口
"""

from flask import Blueprint, request, jsonify, render_template
from flask_login import login_required
from utils.logger import LoggerManager

# 获取日志记录器
logger = LoggerManager.get_logger()

# 创建蓝图
auto_ota_bp = Blueprint('auto_ota', __name__)


@auto_ota_bp.route('/auto_ota')
@login_required
def auto_ota_page():
    """自动OTA升级管理页面"""
    return render_template('auto_ota.html')


@auto_ota_bp.route('/api/auto_ota/config', methods=['GET'])
@login_required
def get_auto_ota_config():
    """获取自动OTA升级配置"""
    try:
        from services.auto_ota_service import get_auto_ota_config
        config = get_auto_ota_config()
        return jsonify({
            'success': True,
            'config': config
        })
    except Exception as e:
        logger.error(f"获取自动OTA配置失败: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500


@auto_ota_bp.route('/api/auto_ota/config', methods=['POST'])
@login_required
def update_auto_ota_config():
    """更新自动OTA升级配置"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'message': '请求数据不能为空'
            }), 400

        from services.auto_ota_service import configure_auto_ota_service

        # 提取配置参数
        enabled = data.get('enabled')
        force_update = data.get('force_update')
        timeout = data.get('timeout')
        max_retries = data.get('max_retries')
        compare_version = data.get('compare_version')
        simulation_mode = data.get('simulation_mode')

        # 验证参数
        if timeout is not None and (not isinstance(timeout, int) or timeout <= 0):
            return jsonify({
                'success': False,
                'message': '超时时间必须是正整数'
            }), 400

        if max_retries is not None and (not isinstance(max_retries, int) or max_retries < 0):
            return jsonify({
                'success': False,
                'message': '最大重试次数必须是非负整数'
            }), 400

        # 更新配置
        success = configure_auto_ota_service(
            enabled=enabled,
            force_update=force_update,
            timeout=timeout,
            max_retries=max_retries,
            compare_version=compare_version,
            simulation_mode=simulation_mode
        )

        if success:
            return jsonify({
                'success': True,
                'message': '配置更新成功'
            })
        else:
            return jsonify({
                'success': False,
                'message': '配置更新失败'
            }), 500

    except Exception as e:
        logger.error(f"更新自动OTA配置失败: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500


@auto_ota_bp.route('/api/auto_ota/status', methods=['GET'])
@login_required
def get_auto_ota_status():
    """获取自动OTA升级服务状态"""
    try:
        from services.auto_ota_service import get_auto_ota_status as get_status

        status_data = get_status()

        return jsonify({
            'success': True,
            'status': {
                'initialized': True,  # 服务总是初始化的
                'config': status_data['config']
            }
        })

    except Exception as e:
        logger.error(f"获取自动OTA状态失败: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500


@auto_ota_bp.route('/api/auto_ota/test', methods=['POST'])
@login_required
def test_auto_ota():
    """测试自动OTA升级功能"""
    try:
        data = request.get_json()
        device_id = data.get('device_id')

        if not device_id:
            return jsonify({
                'success': False,
                'message': '设备ID不能为空'
            }), 400

        # 这里可以添加测试逻辑
        # 例如：模拟发送固件升级请求消息

        return jsonify({
            'success': True,
            'message': f'设备 {device_id} 的自动OTA测试已启动'
        })

    except Exception as e:
        logger.error(f"测试自动OTA失败: {e}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500
