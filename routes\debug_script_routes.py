#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调试脚本路由
提供调试脚本管理和功率数据查询的API接口
"""

from flask import Blueprint, render_template, request, jsonify, current_app, send_file
from flask_login import login_required, current_user
from datetime import datetime, timedelta
import json
import os
import re
import io
from models.device import Device
# from models.debug_script import DebugScript  # 已合并到Device模型中
from models.database import db
from services.debug_script_manager import debug_script_manager
from services.time_series_service import time_series_service
from services.data_export_service import data_export_service
from services.auto_restart_service import auto_restart_service
from utils.logger import LoggerManager

# 获取日志记录器
logger = LoggerManager.get_logger()

# 创建蓝图
debug_script_bp = Blueprint('debug_script', __name__, url_prefix='/debug_script')

@debug_script_bp.route('/status/<int:device_id>', methods=['GET'])
@login_required
def get_script_status(device_id):
    """获取设备调试脚本状态"""
    try:
        # 检查设备是否存在
        device = Device.query.get_or_404(device_id)

        # 获取脚本状态
        status = debug_script_manager.get_script_status(device_id)
        logger.info(f"设备 {device_id} 的调试脚本状态: {status}")
        return jsonify(status)
    except Exception as e:
        logger.error(f"获取调试脚本状态异常: {e}")
        return jsonify({'error': str(e)}), 500

@debug_script_bp.route('/start/<int:device_id>', methods=['POST'])
@login_required
def start_script(device_id):
    """启动设备调试脚本"""
    try:
        # 检查设备是否存在
        device = Device.query.get_or_404(device_id)

        # 获取请求参数
        data = request.get_json()
        if not data or 'frequency' not in data:
            return jsonify({'error': '缺少frequency参数'}), 400

        frequency = int(data['frequency'])
        if frequency < 5:
            return jsonify({'error': '频率不能小于5秒'}), 400

        # 启动脚本
        success = debug_script_manager.start_script(device_id, frequency)

        if success:
            return jsonify({'success': True, 'message': f'设备 {device.device_id} 的调试脚本已启动'})
        else:
            return jsonify({'success': False, 'message': '启动调试脚本失败'}), 500
    except Exception as e:
        logger.error(f"启动调试脚本异常: {e}")
        return jsonify({'error': str(e)}), 500

@debug_script_bp.route('/stop/<int:device_id>', methods=['POST'])
@login_required
def stop_script(device_id):
    """停止设备调试脚本"""
    try:
        # 检查设备是否存在
        device = Device.query.get_or_404(device_id)

        # 停止脚本
        success = debug_script_manager.stop_script(device_id)

        if success:
            return jsonify({'success': True, 'message': f'设备 {device.device_id} 的调试脚本已停止'})
        else:
            return jsonify({'success': False, 'message': '停止调试脚本失败'}), 500
    except Exception as e:
        logger.error(f"停止调试脚本异常: {e}")
        return jsonify({'error': str(e)}), 500


@debug_script_bp.route('/api/running_devices', methods=['GET'])
@login_required
def get_running_debug_script_devices():
    """获取当前正在运行调试脚本的设备列表"""
    try:
        # 获取所有设备
        devices = Device.query.all()
        running_devices = []

        for device in devices:
            # 检查设备的调试脚本是否正在运行
            is_running = debug_script_manager.is_script_running(device.id)

            if is_running:
                # 获取调试脚本状态详情
                script_status = debug_script_manager.get_script_status(device.id)

                running_devices.append({
                    'id': device.id,
                    'device_id': device.device_id,
                    'device_remark': device.device_remark or '',
                    'product_key': device.product_key,
                    'script_status': script_status,
                    'frequency': script_status.get('frequency', 60),
                    'last_execution_time': script_status.get('last_execution_time'),
                    'last_execution_status': script_status.get('last_execution_status', '运行中'),
                    'total_executions': script_status.get('total_executions', 0),
                    'successful_executions': script_status.get('successful_executions', 0)
                })

        return jsonify({
            'success': True,
            'running_devices': running_devices,
            'total_count': len(running_devices)
        })

    except Exception as e:
        logger.error(f"获取运行中调试脚本设备列表异常: {e}")
        return jsonify({'error': str(e)}), 500

@debug_script_bp.route('/power_data/<int:device_id>', methods=['GET'])
@login_required
def get_power_data(device_id):
    """获取设备功率数据"""
    try:
        # 检查设备是否存在
        device = Device.query.get_or_404(device_id)

        # 获取查询参数
        date_str = request.args.get('date')
        if date_str:
            try:
                # 解析日期字符串
                date = datetime.strptime(date_str, '%Y-%m-%d')
                start_time = date.replace(hour=0, minute=0, second=0, microsecond=0)
                end_time = start_time + timedelta(days=1)
            except ValueError:
                return jsonify({'error': '日期格式无效，应为YYYY-MM-DD'}), 400
        else:
            # 默认查询当天数据
            now = datetime.now()
            start_time = now.replace(hour=0, minute=0, second=0, microsecond=0)
            end_time = now

        # 查询功率数据
        power_data = time_series_service.query_power_data(device.device_id, start_time, end_time)

        return jsonify({
            'device_id': device.device_id,
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'power_data': power_data
        })
    except Exception as e:
        logger.error(f"获取功率数据异常: {e}")
        return jsonify({'error': str(e)}), 500

@debug_script_bp.route('/power_history/<int:device_id>', methods=['GET'])
@login_required
def power_history_page(device_id):
    """功率历史数据页面"""
    try:
        # 检查设备是否存在
        device = Device.query.get_or_404(device_id)

        # 获取今天的日期，格式为YYYY-MM-DD
        today_date = datetime.now().strftime('%Y-%m-%d')

        return render_template('power_history.html', device=device, today_date=today_date)
    except Exception as e:
        logger.error(f"访问功率历史数据页面异常: {e}")
        return jsonify({'error': str(e)}), 500

@debug_script_bp.route('/temperature_history/<int:device_id>', methods=['GET'])
@login_required
def temperature_history_page(device_id):
    """温度历史数据页面"""
    try:
        # 检查设备是否存在
        device = Device.query.get_or_404(device_id)

        # 获取今天的日期，格式为YYYY-MM-DD
        today_date = datetime.now().strftime('%Y-%m-%d')

        return render_template('temperature_history.html', device=device, today_date=today_date)
    except Exception as e:
        logger.error(f"访问温度历史数据页面异常: {e}")
        return jsonify({'error': str(e)}), 500

@debug_script_bp.route('/voltage_history/<int:device_id>', methods=['GET'])
@login_required
def voltage_history_page(device_id):
    """电压历史数据页面"""
    try:
        # 检查设备是否存在
        device = Device.query.get_or_404(device_id)

        # 获取今天的日期，格式为YYYY-MM-DD
        today_date = datetime.now().strftime('%Y-%m-%d')

        return render_template('voltage_history.html', device=device, today_date=today_date)
    except Exception as e:
        logger.error(f"访问电压历史数据页面异常: {e}")
        return jsonify({'error': str(e)}), 500

@debug_script_bp.route('/csq_history/<int:device_id>', methods=['GET'])
@login_required
def csq_history_page(device_id):
    """信号质量历史数据页面"""
    try:
        # 检查设备是否存在
        device = Device.query.get_or_404(device_id)

        # 获取今天的日期，格式为YYYY-MM-DD
        today_date = datetime.now().strftime('%Y-%m-%d')

        return render_template('csq_history.html', device=device, today_date=today_date)
    except Exception as e:
        logger.error(f"访问信号质量历史数据页面异常: {e}")
        return jsonify({'error': str(e)}), 500

@debug_script_bp.route('/temperature_data/<int:device_id>', methods=['GET'])
@login_required
def temperature_data(device_id):
    """获取设备温度数据"""
    try:
        # 检查设备是否存在
        device = Device.query.get_or_404(device_id)

        # 获取查询参数
        date_str = request.args.get('date')
        if date_str:
            try:
                # 解析日期字符串
                date = datetime.strptime(date_str, '%Y-%m-%d')
                start_time = date.replace(hour=0, minute=0, second=0, microsecond=0)
                end_time = start_time + timedelta(days=1)
            except ValueError:
                return jsonify({'error': '日期格式无效，应为YYYY-MM-DD'}), 400
        else:
            # 默认查询当天数据
            now = datetime.now()
            start_time = now.replace(hour=0, minute=0, second=0, microsecond=0)
            end_time = now

        # 查询温度数据
        temperature_data = time_series_service.query_temperature_data(device.device_id, start_time, end_time)

        return jsonify({
            'device_id': device.device_id,
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'temperature_data': temperature_data
        })
    except Exception as e:
        logger.error(f"获取温度数据异常: {e}")
        return jsonify({'error': str(e)}), 500

@debug_script_bp.route('/voltage_data/<int:device_id>', methods=['GET'])
@login_required
def voltage_data(device_id):
    """获取设备电压数据"""
    try:
        # 检查设备是否存在
        device = Device.query.get_or_404(device_id)

        # 获取查询参数
        date_str = request.args.get('date')
        if date_str:
            try:
                # 解析日期字符串
                date = datetime.strptime(date_str, '%Y-%m-%d')
                start_time = date.replace(hour=0, minute=0, second=0, microsecond=0)
                end_time = start_time + timedelta(days=1)
            except ValueError:
                return jsonify({'error': '日期格式无效，应为YYYY-MM-DD'}), 400
        else:
            # 默认查询当天数据
            now = datetime.now()
            start_time = now.replace(hour=0, minute=0, second=0, microsecond=0)
            end_time = now

        # 查询电压数据
        voltage_data = time_series_service.query_voltage_data(device.device_id, start_time, end_time)

        return jsonify({
            'device_id': device.device_id,
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'voltage_data': voltage_data
        })
    except Exception as e:
        logger.error(f"获取电压数据异常: {e}")
        return jsonify({'error': str(e)}), 500

@debug_script_bp.route('/csq_data/<int:device_id>', methods=['GET'])
@login_required
def csq_data(device_id):
    """获取设备信号质量数据"""
    try:
        # 检查设备是否存在
        device = Device.query.get_or_404(device_id)

        # 获取查询参数
        date_str = request.args.get('date')
        if date_str:
            try:
                # 解析日期字符串
                date = datetime.strptime(date_str, '%Y-%m-%d')
                start_time = date.replace(hour=0, minute=0, second=0, microsecond=0)
                end_time = start_time + timedelta(days=1)
            except ValueError:
                return jsonify({'error': '日期格式无效，应为YYYY-MM-DD'}), 400
        else:
            # 默认查询当天数据
            now = datetime.now()
            start_time = now.replace(hour=0, minute=0, second=0, microsecond=0)
            end_time = now

        # 查询信号质量数据
        csq_data = time_series_service.query_csq_data(device.device_id, start_time, end_time)

        return jsonify({
            'device_id': device.device_id,
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'csq_data': csq_data
        })
    except Exception as e:
        logger.error(f"获取信号质量数据异常: {e}")
        return jsonify({'error': str(e)}), 500


@debug_script_bp.route('/data_summary/<int:device_id>/<data_type>', methods=['GET'])
@login_required
def data_summary(device_id, data_type):
    """获取设备数据摘要（聚合数据）"""
    try:
        # 检查设备是否存在
        device = Device.query.get_or_404(device_id)

        # 获取查询参数
        date_str = request.args.get('date')
        interval_minutes = int(request.args.get('interval', 60))  # 默认1小时间隔

        if date_str:
            try:
                # 解析日期字符串
                date = datetime.strptime(date_str, '%Y-%m-%d')
                start_time = date.replace(hour=0, minute=0, second=0, microsecond=0)
                end_time = start_time + timedelta(days=1)
            except ValueError:
                return jsonify({'error': '日期格式无效，应为YYYY-MM-DD'}), 400
        else:
            # 默认查询当天数据
            now = datetime.now()
            start_time = now.replace(hour=0, minute=0, second=0, microsecond=0)
            end_time = now

        # 查询数据摘要
        summary_data = time_series_service.query_data_summary(
            device.device_id, data_type, start_time, end_time, interval_minutes
        )

        return jsonify({
            'device_id': device.device_id,
            'data_type': data_type,
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'interval_minutes': interval_minutes,
            'summary_data': summary_data
        })
    except Exception as e:
        logger.error(f"获取数据摘要异常: {e}")
        return jsonify({'error': str(e)}), 500


@debug_script_bp.route('/data_paginated/<int:device_id>/<data_type>', methods=['GET'])
@login_required
def data_paginated(device_id, data_type):
    """获取设备分页数据"""
    try:
        # 检查设备是否存在
        device = Device.query.get_or_404(device_id)

        # 获取查询参数
        date_str = request.args.get('date')
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 1000))

        # 限制页面大小
        page_size = min(page_size, 5000)

        if date_str:
            try:
                # 解析日期字符串
                date = datetime.strptime(date_str, '%Y-%m-%d')
                start_time = date.replace(hour=0, minute=0, second=0, microsecond=0)
                end_time = start_time + timedelta(days=1)
            except ValueError:
                return jsonify({'error': '日期格式无效，应为YYYY-MM-DD'}), 400
        else:
            # 默认查询当天数据
            now = datetime.now()
            start_time = now.replace(hour=0, minute=0, second=0, microsecond=0)
            end_time = now

        # 查询分页数据
        result = time_series_service.query_data_with_pagination(
            device.device_id, data_type, start_time, end_time, page, page_size
        )

        return jsonify({
            'device_id': device.device_id,
            'data_type': data_type,
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'data': result['data'],
            'pagination': result['pagination']
        })
    except Exception as e:
        logger.error(f"获取分页数据异常: {e}")
        return jsonify({'error': str(e)}), 500


@debug_script_bp.route('/data_statistics/<int:device_id>', methods=['GET'])
@login_required
def data_statistics(device_id):
    """获取设备数据统计信息"""
    try:
        # 检查设备是否存在
        device = Device.query.get_or_404(device_id)

        # 获取数据统计
        stats = time_series_service.get_data_statistics(device.device_id)

        return jsonify({
            'device_id': device.device_id,
            'statistics': stats
        })
    except Exception as e:
        logger.error(f"获取数据统计异常: {e}")
        return jsonify({'error': str(e)}), 500


@debug_script_bp.route('/export_single/<int:device_id>/<data_type>', methods=['POST'])
@login_required
def export_single_data(device_id, data_type):
    """导出单个数据类型的数据"""
    try:
        # 检查设备是否存在
        device = Device.query.get_or_404(device_id)

        # 获取请求参数
        data = request.get_json()
        if not data:
            return jsonify({'error': '缺少请求参数'}), 400

        start_date = data.get('start_date')
        end_date = data.get('end_date')
        format_type = data.get('format', 'xlsx')

        if not start_date:
            return jsonify({'error': '缺少开始日期'}), 400

        # 验证数据类型
        valid_types = ['power', 'voltage', 'temperature', 'csq']
        if data_type not in valid_types:
            return jsonify({'error': f'不支持的数据类型: {data_type}'}), 400

        # 执行导出
        result = data_export_service.export_single_data_type(
            device.device_id, data_type, start_date, end_date, format_type
        )

        if not result['success']:
            return jsonify({'error': result['error']}), 400

        # 返回文件
        file_content = result['file_content']
        filename = result['filename']
        content_type = result['content_type']

        return send_file(
            io.BytesIO(file_content),
            mimetype=content_type,
            as_attachment=True,
            download_name=filename
        )

    except Exception as e:
        logger.error(f"导出单个数据类型异常: {e}")
        return jsonify({'error': str(e)}), 500


@debug_script_bp.route('/export_batch/<int:device_id>', methods=['POST'])
@login_required
def export_batch_data(device_id):
    """批量导出多个数据类型的数据"""
    try:
        # 检查设备是否存在
        device = Device.query.get_or_404(device_id)

        # 获取请求参数
        data = request.get_json()
        if not data:
            return jsonify({'error': '缺少请求参数'}), 400

        data_types = data.get('data_types', [])
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        format_type = data.get('format', 'xlsx')

        if not start_date:
            return jsonify({'error': '缺少开始日期'}), 400

        if not data_types:
            return jsonify({'error': '请选择要导出的数据类型'}), 400

        # 验证数据类型
        valid_types = ['power', 'voltage', 'temperature', 'csq']
        invalid_types = [dt for dt in data_types if dt not in valid_types]
        if invalid_types:
            return jsonify({'error': f'不支持的数据类型: {", ".join(invalid_types)}'}), 400

        # 执行导出
        result = data_export_service.export_batch_data(
            device.device_id, data_types, start_date, end_date, format_type
        )

        if not result['success']:
            return jsonify({'error': result['error']}), 400

        # 返回文件
        file_content = result['file_content']
        filename = result['filename']
        content_type = result['content_type']

        return send_file(
            io.BytesIO(file_content),
            mimetype=content_type,
            as_attachment=True,
            download_name=filename
        )

    except Exception as e:
        logger.error(f"批量导出数据异常: {e}")
        return jsonify({'error': str(e)}), 500


@debug_script_bp.route('/export_progress/<task_id>', methods=['GET'])
@login_required
def export_progress(task_id):
    """获取导出进度（预留接口，用于大数据量导出的进度跟踪）"""
    try:
        # 这里可以实现基于任务ID的进度跟踪
        # 目前返回简单的状态
        return jsonify({
            'task_id': task_id,
            'status': 'completed',
            'progress': 100,
            'message': '导出完成'
        })
    except Exception as e:
        logger.error(f"获取导出进度异常: {e}")
        return jsonify({'error': str(e)}), 500


@debug_script_bp.route('/auto_restart_status', methods=['GET'])
@login_required
def auto_restart_status():
    """获取自动重启服务状态"""
    try:
        status = auto_restart_service.get_restart_status()
        return jsonify({
            'success': True,
            'status': status
        })
    except Exception as e:
        logger.error(f"获取自动重启状态异常: {e}")
        return jsonify({'error': str(e)}), 500


@debug_script_bp.route('/trigger_auto_restart', methods=['POST'])
@login_required
def trigger_auto_restart():
    """手动触发自动重启（仅管理员）"""
    try:
        # 检查用户权限
        if not current_user.is_admin:
            return jsonify({'error': '权限不足，仅管理员可执行此操作'}), 403

        # 检查是否已在运行
        if auto_restart_service.is_running:
            return jsonify({'error': '自动重启服务正在运行中'}), 400

        # 启动自动重启
        from flask import current_app
        success = auto_restart_service.start_auto_restart(current_app.app_context())

        if success:
            return jsonify({
                'success': True,
                'message': '自动重启服务已启动'
            })
        else:
            return jsonify({'error': '启动自动重启服务失败'}), 500

    except Exception as e:
        logger.error(f"手动触发自动重启异常: {e}")
        return jsonify({'error': str(e)}), 500


@debug_script_bp.route('/export_device_parameters/<int:device_id>', methods=['POST'])
@login_required
def export_device_parameters(device_id):
    """导出设备参数数据"""
    try:
        # 检查设备是否存在
        device = Device.query.get_or_404(device_id)

        # 获取请求参数
        data = request.get_json() or {}
        format_type = data.get('format', 'xlsx')

        # 执行导出
        result = data_export_service.export_device_parameters(
            device.device_id, format_type
        )

        if not result['success']:
            return jsonify({'error': result['error']}), 400

        # 返回文件
        file_content = result['file_content']
        filename = result['filename']
        content_type = result['content_type']

        return send_file(
            io.BytesIO(file_content),
            mimetype=content_type,
            as_attachment=True,
            download_name=filename
        )

    except Exception as e:
        logger.error(f"导出设备参数异常: {e}")
        return jsonify({'error': str(e)}), 500

@debug_script_bp.route('/api/device/<device_id>/data_dates', methods=['GET'])
@login_required
def get_device_data_dates(device_id):
    """获取设备有数据的日期列表"""
    try:
        device = Device.query.get(device_id)
        if not device:
            return jsonify({'error': '设备不存在'}), 404

        # 获取设备数据目录
        device_dir = os.path.join(current_app.config['INFLUXDB_DATA_PATH'], current_app.config['INFLUXDB_BUCKET'], device.device_id)
        if not os.path.exists(device_dir):
            return jsonify({'dates': []})

        # 获取所有日期目录
        dates = []
        logger.info(f"设备数据目录: {device_dir}")
        for date_dir in os.listdir(device_dir):
            if os.path.isdir(os.path.join(device_dir, date_dir)):
                # 检查该日期目录下是否有数据文件
                date_path = os.path.join(device_dir, date_dir)
                has_data = any(f.endswith('.csv') for f in os.listdir(date_path))
                if has_data:
                    dates.append(date_dir)
        
        return jsonify({'dates': sorted(dates)})
    except Exception as e:
        logger.error(f"获取设备数据日期列表失败: {e}")
        return jsonify({'error': str(e)}), 500

@debug_script_bp.route('/api/devices/batch_start_debug_script', methods=['POST'])
@login_required
def batch_start_debug_script():
    """批量启动调试脚本"""
    try:
        data = request.get_json()
        if not data or 'device_ids' not in data or 'frequency' not in data:
            return jsonify({'error': '缺少必要参数'}), 400

        device_ids = data['device_ids']
        frequency = int(data['frequency'])

        if frequency < 5:
            return jsonify({'error': '频率不能小于5秒'}), 400

        results = []
        for device_id in device_ids:
            try:
                device = Device.query.get(device_id)
                if not device:
                    results.append({
                        'device_id': device_id,
                        'success': False,
                        'error': '设备不存在'
                    })
                    continue

                success = debug_script_manager.start_script(device_id, frequency)
                results.append({
                    'device_id': device_id,
                    'success': success,
                    'error': None if success else '启动失败'
                })
            except Exception as e:
                results.append({
                    'device_id': device_id,
                    'success': False,
                    'error': str(e)
                })

        return jsonify({
            'success': True,
            'results': results
        })
    except Exception as e:
        logger.error(f"批量启动调试脚本异常: {e}")
        return jsonify({'error': str(e)}), 500

@debug_script_bp.route('/api/devices/batch_stop_debug_script', methods=['POST'])
@login_required
def batch_stop_debug_script():
    """批量停止调试脚本"""
    try:
        data = request.get_json()
        if not data or 'device_ids' not in data:
            return jsonify({'error': '缺少必要参数'}), 400

        device_ids = data['device_ids']
        results = []
        for device_id in device_ids:
            try:
                device = Device.query.get(device_id)
                if not device:
                    results.append({
                        'device_id': device_id,
                        'success': False,
                        'error': '设备不存在'
                    })
                    continue

                success = debug_script_manager.stop_script(device_id)
                results.append({
                    'device_id': device_id,
                    'success': success,
                    'error': None if success else '停止失败'
                })
            except Exception as e:
                results.append({
                    'device_id': device_id,
                    'success': False,
                    'error': str(e)
                })

        return jsonify({
            'success': True,
            'results': results
        })
    except Exception as e:
        logger.error(f"批量停止调试脚本异常: {e}")
        return jsonify({'error': str(e)}), 500 