#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
设备控制台路由模块
提供设备控制台的综合管理功能
"""

from flask import Blueprint, render_template, request, jsonify, url_for, current_app
from flask_login import login_required, current_user
from models.device import Device
from models.firmware import Firmware
from models.ota_task import OtaTask
from models.database import db
from datetime import datetime
import qrcode
import io
import base64
import json
from utils.logger import LoggerManager

# 获取日志记录器
logger = LoggerManager.get_logger()

# 创建蓝图
device_console_bp = Blueprint('device_console', __name__)

@device_console_bp.route('/device/<int:device_id>/console')
@login_required
def device_console(device_id):
    """设备控制台主页面"""
    try:
        # 获取设备信息
        device = Device.query.get_or_404(device_id)
        
        # 获取设备状态信息
        device_status_cache = current_app.config.get('DEVICE_STATUS_CACHE', {})
        device_status_lock = current_app.config.get('DEVICE_STATUS_LOCK')
        
        device_status = None
        if device_status_lock:
            with device_status_lock:
                device_status = device_status_cache.get(device_id, {
                    'is_online': False,
                    'last_check': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'last_online': None
                })
        
        # 获取最新的OTA任务
        # 注意：OTA任务表中的device_id字段存储的是device表的id（字符串形式）
        latest_ota = OtaTask.query.filter_by(device_id=str(device_id)).order_by(OtaTask.created_at.desc()).first()
        
        # 获取可用固件列表
        firmwares = Firmware.query.order_by(Firmware.upload_time.desc()).all()
        
        # 获取设备参数（如果有的话）
        device_params = {}
        
        return render_template('device/console.html', 
                             device=device,
                             device_status=device_status,
                             latest_ota=latest_ota,
                             firmwares=firmwares,
                             device_params=device_params)
                             
    except Exception as e:
        logger.error(f"访问设备控制台失败: {e}")
        return render_template('error.html', error_message=f"访问设备控制台失败: {str(e)}"), 500

@device_console_bp.route('/device/<int:device_id>/console/qr')
@login_required
def generate_device_qr(device_id):
    """生成设备配置二维码"""
    try:
        device = Device.query.get_or_404(device_id)
        
        # 构建二维码数据
        qr_data = {
            'device_id': device.device_id,
            'product_key': device.product_key,
            'device_remark': device.device_remark or '',
            'timestamp': datetime.now().isoformat(),
            'user': current_user.username,
            'console_url': url_for('device_console.device_console', device_id=device_id, _external=True)
        }
        
        # 生成二维码
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(json.dumps(qr_data))
        qr.make(fit=True)
        
        # 创建二维码图片
        img = qr.make_image(fill_color="black", back_color="white")
        
        # 转换为base64
        img_buffer = io.BytesIO()
        img.save(img_buffer, format='PNG')
        img_str = base64.b64encode(img_buffer.getvalue()).decode()
        
        return jsonify({
            'success': True,
            'qr_image': f'data:image/png;base64,{img_str}',
            'qr_data': qr_data
        })
        
    except Exception as e:
        logger.error(f"生成设备二维码失败: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@device_console_bp.route('/device/<int:device_id>/console/status')
@login_required
def get_device_console_status(device_id):
    """获取设备控制台状态信息"""
    try:
        device = Device.query.get_or_404(device_id)
        
        # 获取设备状态
        device_status_cache = current_app.config.get('DEVICE_STATUS_CACHE', {})
        device_status_lock = current_app.config.get('DEVICE_STATUS_LOCK')
        
        device_status = {'is_online': False, 'last_check': 'Unknown'}
        if device_status_lock:
            with device_status_lock:
                status_info = device_status_cache.get(device_id, {})
                device_status = {
                    'is_online': status_info.get('is_online', False),
                    'last_check': datetime.fromtimestamp(status_info.get('last_check', 0)).strftime('%Y-%m-%d %H:%M:%S') if status_info.get('last_check') else 'Unknown',
                    'last_online': datetime.fromtimestamp(status_info.get('last_online', 0)).strftime('%Y-%m-%d %H:%M:%S') if status_info.get('last_online') else 'Unknown'
                }
        
        # 获取最新OTA状态
        latest_ota = OtaTask.query.filter_by(device_id=device_id).order_by(OtaTask.created_at.desc()).first()
        ota_status = None
        if latest_ota:
            ota_status = {
                'id': latest_ota.id,
                'status': latest_ota.status,
                'progress': latest_ota.progress,
                'firmware_version': latest_ota.firmware_version,
                'created_at': latest_ota.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'updated_at': latest_ota.updated_at.strftime('%Y-%m-%d %H:%M:%S') if latest_ota.updated_at else None
            }
        
        return jsonify({
            'success': True,
            'device_status': device_status,
            'ota_status': ota_status,
            'device_info': {
                'id': device.id,
                'device_id': device.device_id,
                'device_remark': device.device_remark,
                'product_key': device.product_key,
                'firmware_version': device.firmware_version,
                'created_at': device.created_at.strftime('%Y-%m-%d %H:%M:%S') if device.created_at else None
            }
        })
        
    except Exception as e:
        logger.error(f"获取设备控制台状态失败: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@device_console_bp.route('/device/<int:device_id>/console/refresh')
@login_required
def refresh_device_status(device_id):
    """刷新设备状态"""
    try:
        device = Device.query.get_or_404(device_id)
        
        # 这里可以添加主动查询设备状态的逻辑
        # 目前返回缓存中的状态
        
        return jsonify({'success': True, 'message': '状态刷新请求已发送'})
        
    except Exception as e:
        logger.error(f"刷新设备状态失败: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500
