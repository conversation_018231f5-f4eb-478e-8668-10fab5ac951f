from flask import Blueprint, render_template, jsonify, request, current_app
from flask_login import login_required
# from models.device_location import DeviceLocation  # 已合并到Device模型中
from models.device import Device
from models.database import db
from services.device_location import update_device_location_db
from utils.logger import LoggerManager
from datetime import datetime

# 获取日志记录器
logger = LoggerManager.get_logger()

bp = Blueprint('device_location', __name__)

@bp.route('/device/map')
@login_required
def device_map():
    """设备地图页面"""
    return render_template('device/map.html')

@bp.route('/api/device/locations')
@login_required
def get_device_locations():
    """获取所有设备的位置信息"""
    try:
        # 获取所有有位置信息的设备
        devices = Device.query.filter(Device.latitude.isnot(None), Device.longitude.isnot(None)).all()

        result = []
        for device in devices:
            location_data = {
                'id': device.id,  # 数据库主键ID
                'device_id': device.device_id,  # 设备标识符
                'latitude': float(device.latitude) if device.latitude else 0.0,
                'longitude': float(device.longitude) if device.longitude else 0.0,
                'address': device.address or '',
                'device_name': device.device_remark or device.device_id,
                'product_key': device.product_key or '',
                'firmware_version': device.firmware_version or '未知',
                'created_at': device.created_at.strftime('%Y-%m-%d %H:%M:%S') if device.created_at else None
            }

            # 只返回有效的位置数据
            if location_data['latitude'] != 0.0 and location_data['longitude'] != 0.0:
                result.append(location_data)

        logger.info(f"获取设备位置信息成功，共 {len(result)} 个有效位置")
        return jsonify(result)

    except Exception as e:
        logger.error(f"获取设备位置信息失败: {e}")
        return jsonify({'error': str(e)}), 500

@bp.route('/api/device/location/<device_id>', methods=['POST'])
def update_device_location(device_id):
    """更新设备位置信息"""
    data = request.json
    latitude = data.get('latitude')
    longitude = data.get('longitude')
    address = data.get('address')

    ret = update_device_location_db(device_id, latitude, longitude, address)
    if ret:
        return jsonify({'message': '位置信息更新成功'})
    else:
        return jsonify({'message': '位置信息更新失败'}), 500

@bp.route('/api/device/location/<device_id>', methods=['DELETE'])
def delete_device_location(device_id):
    """删除设备位置信息"""
    try:
        location = DeviceLocation.query.filter_by(device_id=device_id).first()
        if location:
            db.session.delete(location)
            db.session.commit()
            return jsonify({'success': True, 'message': '位置信息删除成功'})
        else:
            return jsonify({'success': False, 'message': '设备位置信息不存在'}), 404
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'删除失败: {str(e)}'}), 500
