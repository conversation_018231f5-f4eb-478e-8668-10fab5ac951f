from flask import Blueprint, render_template, jsonify, request
from flask_login import login_required

game_bp = Blueprint('game', __name__)

@game_bp.route('/games')
@login_required
def game_list():
    """游戏列表页面"""
    return render_template('game/list.html')

@game_bp.route('/games/snake')
@login_required
def snake_game():
    """贪吃蛇游戏页面"""
    return render_template('game/snake.html')

@game_bp.route('/games/snake3d')
@login_required
def snake3d_game():
    """3D贪吃蛇游戏页面"""
    return render_template('game/snake3d.html')

@game_bp.route('/api/game/snake/score', methods=['POST'])
@login_required
def update_snake_score():
    """更新贪吃蛇游戏分数"""
    data = request.get_json()
    score = data.get('score', 0)
    # TODO: 将分数保存到数据库
    return jsonify({'success': True, 'message': '分数更新成功'}) 