#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
IoT客户端管理路由模块
处理IoT客户端的启动、停止和状态查询
"""

from datetime import datetime
from flask import Blueprint, render_template, jsonify, current_app
from flask_login import login_required

from services.iot_client_manager import IoTClientManager
from utils.logger import LoggerManager

# 获取日志记录器
logger = LoggerManager.get_logger()

# 创建蓝图
iot_bp = Blueprint('iot', __name__)

@iot_bp.route('/iot/control', methods=['GET'])
@login_required
def iot_control():
    """IoT客户端管理页面"""
    now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    return render_template('iot_control.html', status=IoTClientManager.get_status(), now=now)

@iot_bp.route('/iot/start', methods=['POST'])
@login_required
def iot_start():
    """启动IoT客户端"""
    success = IoTClientManager.start()
    if success:
        # 启动设备状态查询
        device_status_thread = current_app.config.get('DEVICE_STATUS_THREAD')
        device_status_thread.start_thread()
    return jsonify({
        'success': success,
        'status': IoTClientManager.get_status(),
        'message': '已启动IoT客户端' if success else 'IoT客户端已经在运行中'
    })

@iot_bp.route('/iot/stop', methods=['POST'])
@login_required
def iot_stop():
    """停止IoT客户端"""
    success = IoTClientManager.stop()
    if success:
        # 停止设备状态查询
        device_status_thread = current_app.config.get('DEVICE_STATUS_THREAD')
        device_status_thread.stop_thread()
    return jsonify({
        'success': success,
        'status': IoTClientManager.get_status(),
        'message': '已停止IoT客户端' if success else 'IoT客户端未在运行中'
    })

@iot_bp.route('/iot/status', methods=['GET'])
@login_required
def iot_status():
    """获取IoT客户端状态"""
    return jsonify({
        'status': IoTClientManager.get_status(),
        'running': IoTClientManager.is_running()
    })
