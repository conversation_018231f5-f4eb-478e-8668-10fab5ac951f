from models.login_log import LoginLog
from flask import render_template, redirect, url_for, flash
from flask_login import login_required, current_user
from flask import Blueprint

login_logs_bp = Blueprint('login_logs', __name__)

# 新增路由：/admin/login_logs，用于展示登录日志
@login_logs_bp.route('/admin/login_logs')
@login_required
def login_logs():
    if not current_user.is_admin:
        flash("无权限查看", "danger")
        return redirect(url_for('main.index'))
    logs = LoginLog.query.order_by(LoginLog.login_time.desc()).all()
    return render_template('login_logs.html', logs=logs)
