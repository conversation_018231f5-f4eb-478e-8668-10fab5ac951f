from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models.database import db
from models.merchant import Merchant

# 创建蓝图
merchant_bp = Blueprint('merchant', __name__)

@merchant_bp.route('/merchants')
@login_required
def merchant_list():
    """商户列表页面"""
    if not current_user.is_admin:
        flash('无权限访问', 'danger')
        return redirect(url_for('main.index'))
    
    merchants = Merchant.query.all()
    return render_template('merchant/list.html', merchants=merchants)

@merchant_bp.route('/merchant/add', methods=['GET', 'POST'])
@login_required
def add_merchant():
    """添加商户"""
    if not current_user.is_admin:
        flash('无权限访问', 'danger')
        return redirect(url_for('main.index'))
    
    if request.method == 'POST':
        name = request.form.get('name')
        contact_person = request.form.get('contact_person')
        contact_phone = request.form.get('contact_phone')
        contact_email = request.form.get('contact_email')
        address = request.form.get('address')
        business_license = request.form.get('business_license')
        tax_number = request.form.get('tax_number')
        bank_name = request.form.get('bank_name')
        bank_account = request.form.get('bank_account')
        bank_account_name = request.form.get('bank_account_name')
        status = request.form.get('status', '正常')
        remark = request.form.get('remark')
        
        # 创建新商户
        merchant = Merchant(
            name=name,
            contact_person=contact_person,
            contact_phone=contact_phone,
            contact_email=contact_email,
            address=address,
            business_license=business_license,
            tax_number=tax_number,
            bank_name=bank_name,
            bank_account=bank_account,
            bank_account_name=bank_account_name,
            status=status,
            remark=remark
        )
        
        db.session.add(merchant)
        db.session.commit()
        
        flash('商户添加成功', 'success')
        return redirect(url_for('merchant.merchant_list'))
    
    return render_template('merchant/form.html')

@merchant_bp.route('/merchant/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_merchant(id):
    """编辑商户"""
    if not current_user.is_admin:
        flash('无权限访问', 'danger')
        return redirect(url_for('main.index'))
    
    merchant = Merchant.query.get_or_404(id)
    
    if request.method == 'POST':
        merchant.name = request.form.get('name')
        merchant.contact_person = request.form.get('contact_person')
        merchant.contact_phone = request.form.get('contact_phone')
        merchant.contact_email = request.form.get('contact_email')
        merchant.address = request.form.get('address')
        merchant.business_license = request.form.get('business_license')
        merchant.tax_number = request.form.get('tax_number')
        merchant.bank_name = request.form.get('bank_name')
        merchant.bank_account = request.form.get('bank_account')
        merchant.bank_account_name = request.form.get('bank_account_name')
        merchant.status = request.form.get('status', '正常')
        merchant.remark = request.form.get('remark')
        
        db.session.commit()
        
        flash('商户更新成功', 'success')
        return redirect(url_for('merchant.merchant_list'))
    
    return render_template('merchant/form.html', merchant=merchant)

@merchant_bp.route('/merchant/delete/<int:id>')
@login_required
def delete_merchant(id):
    """删除商户"""
    if not current_user.is_admin:
        flash('无权限访问', 'danger')
        return redirect(url_for('main.index'))
    
    merchant = Merchant.query.get_or_404(id)
    
    # 删除商户
    db.session.delete(merchant)
    db.session.commit()
    
    flash('商户删除成功', 'success')
    return redirect(url_for('merchant.merchant_list'))

@merchant_bp.route('/merchant/view/<int:id>')
@login_required
def view_merchant(id):
    """查看商户详情"""
    if not current_user.is_admin:
        flash('无权限访问', 'danger')
        return redirect(url_for('main.index'))
    
    merchant = Merchant.query.get_or_404(id)
    return render_template('merchant/view.html', merchant=merchant) 