from flask import Blueprint, render_template, request, jsonify, send_from_directory
from flask_login import login_required
import os
from werkzeug.utils import secure_filename
from datetime import datetime

model_viewer_bp = Blueprint('model_viewer', __name__)

# 配置文件上传目录
UPLOAD_FOLDER = 'static/uploads/models'
ALLOWED_EXTENSIONS = {'gltf', 'glb'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@model_viewer_bp.route('/model-viewer')
@login_required
def model_viewer():
    """3D模型查看器页面"""
    return render_template('model_viewer.html')

@model_viewer_bp.route('/api/upload-model', methods=['POST'])
@login_required
def upload_model():
    """上传3D模型文件"""
    if 'file' not in request.files:
        return jsonify({'error': '没有文件被上传'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': '没有选择文件'}), 400
    
    if not allowed_file(file.filename):
        return jsonify({'error': '不支持的文件类型'}), 400
    
    # 确保上传目录存在
    os.makedirs(UPLOAD_FOLDER, exist_ok=True)
    
    # 生成安全的文件名
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = secure_filename(f"{timestamp}_{file.filename}")
    filepath = os.path.join(UPLOAD_FOLDER, filename)
    
    try:
        file.save(filepath)
        return jsonify({
            'message': '文件上传成功',
            'path': f'/static/uploads/models/{filename}'
        })
    except Exception as e:
        return jsonify({'error': f'文件保存失败: {str(e)}'}), 500

@model_viewer_bp.route('/api/models')
@login_required
def list_models():
    """获取已上传的模型列表"""
    try:
        models = []
        for filename in os.listdir(UPLOAD_FOLDER):
            if allowed_file(filename):
                filepath = os.path.join(UPLOAD_FOLDER, filename)
                models.append({
                    'name': filename,
                    'size': os.path.getsize(filepath),
                    'path': f'/static/uploads/models/{filename}'
                })
        return jsonify(models)
    except Exception as e:
        return jsonify({'error': f'获取模型列表失败: {str(e)}'}), 500 