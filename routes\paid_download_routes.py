from flask import Blueprint, render_template, request, jsonify, send_file, flash, redirect, url_for
from flask_login import login_required, current_user
from models.paid_download import PaidDownload, DownloadOrder
from models.database import db
from services.payment_service import PaymentService
import os
import uuid
from datetime import datetime
from werkzeug.utils import secure_filename
from services.alipay_service import AlipayService
import logging

paid_download_bp = Blueprint('paid_download', __name__)

logger = logging.getLogger(__name__)

@paid_download_bp.route('/paid-downloads')
@login_required
def list_downloads():
    """付费下载列表页面"""
    page = request.args.get('page', 1, type=int)
    per_page = 12  # 每页显示12个项目
    
    pagination = PaidDownload.query.filter_by(is_active=True).order_by(
        PaidDownload.created_at.desc()
    ).paginate(page=page, per_page=per_page, error_out=False)
    downloads = pagination.items
    
    return render_template('paid_download/list.html', 
                         downloads=downloads,
                         pagination=pagination)

@paid_download_bp.route('/detail/<int:download_id>')
@login_required
def download_detail(download_id):
    """付费下载详情页面"""
    download = PaidDownload.query.get_or_404(download_id)
    if not download.is_active:
        flash('该下载已下架', 'error')
        return redirect(url_for('paid_download.list_downloads'))
        
    # 检查用户是否已购买
    has_purchased = DownloadOrder.query.filter_by(
        user_id=current_user.id,
        download_id=download_id,
        status='paid'
    ).first() is not None
    
    return render_template('paid_download/detail.html', 
                         download=download, 
                         has_purchased=has_purchased)

@paid_download_bp.route('/create_order', methods=['POST'])
@login_required
def create_order():
    """创建下载订单"""
    download_id = request.form.get('download_id')
    if not download_id:
        return jsonify({'success': False, 'message': '缺少下载ID'})
        
    download = PaidDownload.query.get_or_404(download_id)
    if not download.is_active:
        return jsonify({'success': False, 'message': '该下载已下架'})
    
    # 检查是否已购买
    existing_order = DownloadOrder.query.filter_by(
        user_id=current_user.id,
        download_id=download_id,
        status='paid'
    ).first()
    if existing_order:
        return jsonify({'success': False, 'message': '您已购买过此下载'})
    
    # 创建订单
    order = DownloadOrder(
        user_id=current_user.id,
        download_id=download_id,
        order_no=str(uuid.uuid4()).replace('-', ''),
        amount=download.price,
        status='pending'
    )
    db.session.add(order)
    db.session.commit()
    
    return jsonify({
        'success': True,
        'order_id': order.id,
        'order_no': order.order_no,
        'amount': float(order.amount)
    })

@paid_download_bp.route('/pay_order', methods=['POST'])
@login_required
def pay_order():
    """支付订单"""
    order_id = request.form.get('order_id')
    payment_method = request.form.get('payment_method', 'simulated')
    
    if not order_id:
        return jsonify({'success': False, 'message': '缺少订单ID'})
        
    order = DownloadOrder.query.get_or_404(order_id)
    
    # 检查订单状态
    if order.status != 'pending':
        return jsonify({'success': False, 'message': '订单状态不正确'})
    
    # 检查订单所属
    if order.user_id != current_user.id:
        return jsonify({'success': False, 'message': '无权操作此订单'})
    
    # 处理支付
    result = PaymentService.process_payment(order, payment_method)
    return jsonify(result)

@paid_download_bp.route('/download/<int:download_id>')
@login_required
def download_file(download_id):
    """下载文件"""
    download = PaidDownload.query.get_or_404(download_id)
    if not download.is_active:
        flash('该下载已下架', 'error')
        return redirect(url_for('paid_download.list_downloads'))
    
    # 检查是否已购买
    order = DownloadOrder.query.filter_by(
        user_id=current_user.id,
        download_id=download_id,
        status='paid'
    ).first()
    if not order:
        flash('请先购买此下载', 'error')
        return redirect(url_for('paid_download.download_detail', download_id=download_id))
    
    # 更新下载次数
    download.download_count += 1
    db.session.commit()
    
    # 发送文件
    return send_file(
        download.file_path,
        as_attachment=True,
        download_name=os.path.basename(download.file_path)
    )

@paid_download_bp.route('/create', methods=['GET', 'POST'])
@login_required
def create_download():
    """创建付费下载"""
    if not current_user.is_admin:
        flash('只有管理员可以创建付费下载', 'error')
        return redirect(url_for('paid_download.list_downloads'))
        
    if request.method == 'POST':
        try:
            name = request.form.get('name')
            description = request.form.get('description')
            price = float(request.form.get('price', 0))
            file = request.files.get('file')
            
            if not all([name, description, price, file]):
                flash('请填写所有必填字段', 'error')
                return redirect(url_for('paid_download.create_download'))
                
            # 保存文件
            filename = secure_filename(file.filename)
            file_path = os.path.join('uploads', 'paid_downloads', filename)
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            file.save(file_path)
            
            # 创建下载记录
            download = PaidDownload(
                name=name,
                description=description,
                price=price,
                file_path=file_path,
                file_size=os.path.getsize(file_path),
                created_by=current_user.id
            )
            
            db.session.add(download)
            db.session.commit()
            
            flash('付费下载创建成功', 'success')
            return redirect(url_for('paid_download.list_downloads'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'创建失败：{str(e)}', 'error')
            return redirect(url_for('paid_download.create_download'))
            
    return render_template('paid_download/create.html')

@paid_download_bp.route('/orders')
@login_required
def order_list():
    """订单列表页面"""
    page = request.args.get('page', 1, type=int)
    per_page = 10  # 每页显示10条记录
    
    # 查询当前用户的订单
    pagination = DownloadOrder.query.filter_by(
        user_id=current_user.id
    ).order_by(
        DownloadOrder.created_at.desc()
    ).paginate(page=page, per_page=per_page, error_out=False)
    
    return render_template('paid_download/orders.html', orders=pagination)

@paid_download_bp.route('/alipay/notify', methods=['POST'])
def alipay_notify():
    """支付宝支付回调"""
    try:
        # 获取支付宝POST过来通知信息
        data = request.form.to_dict()
        
        # 验证签名
        alipay_service = AlipayService()
        success = alipay_service.verify_payment(data)
        
        if success:
            # 获取订单号
            order_no = data.get('out_trade_no')
            if not order_no:
                return 'fail'
            
            # 查询订单
            order = DownloadOrder.query.filter_by(order_no=order_no).first()
            if not order:
                return 'fail'
            
            # 更新订单状态
            order.status = 'paid'
            order.payment_time = datetime.now()
            order.payment_method = 'alipay'
            db.session.commit()
            
            return 'success'
        else:
            return 'fail'
    except Exception as e:
        logger.error(f"支付宝回调处理失败: {str(e)}")
        return 'fail'