from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required
from models.register_config import RegisterConfig
from models.database import db
import logging

logger = logging.getLogger(__name__)

register_config_bp = Blueprint('register_config', __name__)

@register_config_bp.route('/register_config', methods=['GET'])
@login_required
def register_config_list():
    """寄存器配置管理页面"""
    try:
        # 获取所有寄存器配置
        configs = RegisterConfig.query.order_by(RegisterConfig.register_address).all()
        
        # 按分类统计
        time_count = RegisterConfig.query.filter_by(category='time').count()
        power_count = RegisterConfig.query.filter_by(category='power').count()
        other_count = RegisterConfig.query.filter_by(category='other').count()
        
        stats = {
            'total': len(configs),
            'time': time_count,
            'power': power_count,
            'other': other_count
        }
        
        return render_template('register_config.html', configs=configs, stats=stats)
        
    except Exception as e:
        logger.error(f"获取寄存器配置失败: {e}")
        flash(f'获取寄存器配置失败: {str(e)}', 'error')
        return redirect(url_for('main.index'))

@register_config_bp.route('/api/register_config', methods=['GET'])
@login_required
def get_register_configs():
    """获取寄存器配置API"""
    try:
        category = request.args.get('category')
        
        if category:
            configs = RegisterConfig.get_registers_by_category(category)
        else:
            configs = RegisterConfig.get_all_active_registers()
        
        return jsonify([config.to_dict() for config in configs])
        
    except Exception as e:
        logger.error(f"获取寄存器配置API失败: {e}")
        return jsonify({'error': str(e)}), 500

@register_config_bp.route('/api/register_config/<int:config_id>', methods=['PUT'])
@login_required
def update_register_config(config_id):
    """更新寄存器配置"""
    try:
        config = RegisterConfig.query.get_or_404(config_id)
        data = request.get_json()
        
        # 更新字段
        if 'register_alias' in data:
            config.register_alias = data['register_alias']
        if 'description' in data:
            config.description = data['description']
        if 'category' in data:
            config.category = data['category']
        if 'unit' in data:
            config.unit = data['unit']
        if 'data_type' in data:
            config.data_type = data['data_type']
        if 'min_value' in data:
            config.min_value = data['min_value']
        if 'max_value' in data:
            config.max_value = data['max_value']
        if 'default_value' in data:
            config.default_value = data['default_value']
        if 'is_active' in data:
            config.is_active = data['is_active']
        
        config.updated_at = db.func.now()
        db.session.commit()
        
        logger.info(f"更新寄存器配置: {config.register_name}")
        return jsonify({'success': True, 'message': '更新成功'})
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"更新寄存器配置失败: {e}")
        return jsonify({'error': str(e)}), 500

@register_config_bp.route('/api/register_config', methods=['POST'])
@login_required
def create_register_config():
    """创建新的寄存器配置"""
    try:
        data = request.get_json()
        
        # 验证必填字段
        required_fields = ['register_address', 'register_name']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({'error': f'缺少必填字段: {field}'}), 400
        
        # 检查地址和名称是否已存在
        existing_address = RegisterConfig.query.filter_by(register_address=data['register_address']).first()
        if existing_address:
            return jsonify({'error': f'寄存器地址 {data["register_address"]} 已存在'}), 400
            
        existing_name = RegisterConfig.query.filter_by(register_name=data['register_name']).first()
        if existing_name:
            return jsonify({'error': f'寄存器名称 {data["register_name"]} 已存在'}), 400
        
        # 创建新配置
        config = RegisterConfig(
            register_address=data['register_address'],
            register_name=data['register_name'],
            register_alias=data.get('register_alias', ''),
            description=data.get('description', ''),
            data_type=data.get('data_type', 'integer'),
            category=data.get('category', 'other'),
            unit=data.get('unit', ''),
            min_value=data.get('min_value'),
            max_value=data.get('max_value'),
            default_value=data.get('default_value'),
            is_active=data.get('is_active', True)
        )
        
        db.session.add(config)
        db.session.commit()
        
        logger.info(f"创建寄存器配置: {config.register_name}")
        return jsonify({'success': True, 'message': '创建成功', 'config': config.to_dict()})
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"创建寄存器配置失败: {e}")
        return jsonify({'error': str(e)}), 500

@register_config_bp.route('/api/register_config/<int:config_id>', methods=['DELETE'])
@login_required
def delete_register_config(config_id):
    """删除寄存器配置"""
    try:
        config = RegisterConfig.query.get_or_404(config_id)
        
        # 软删除：设置为不活跃
        config.is_active = False
        config.updated_at = db.func.now()
        db.session.commit()
        
        logger.info(f"删除寄存器配置: {config.register_name}")
        return jsonify({'success': True, 'message': '删除成功'})
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"删除寄存器配置失败: {e}")
        return jsonify({'error': str(e)}), 500

@register_config_bp.route('/api/register_config/batch_update', methods=['POST'])
@login_required
def batch_update_register_configs():
    """批量更新寄存器配置"""
    try:
        data = request.get_json()
        configs_data = data.get('configs', [])
        
        updated_count = 0
        
        for config_data in configs_data:
            config_id = config_data.get('id')
            if not config_id:
                continue
                
            config = RegisterConfig.query.get(config_id)
            if not config:
                continue
            
            # 更新字段
            if 'register_alias' in config_data:
                config.register_alias = config_data['register_alias']
            if 'description' in config_data:
                config.description = config_data['description']
            if 'category' in config_data:
                config.category = config_data['category']
            if 'unit' in config_data:
                config.unit = config_data['unit']
            
            config.updated_at = db.func.now()
            updated_count += 1
        
        db.session.commit()
        
        logger.info(f"批量更新寄存器配置: {updated_count} 个")
        return jsonify({'success': True, 'message': f'批量更新成功，共更新 {updated_count} 个配置'})
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"批量更新寄存器配置失败: {e}")
        return jsonify({'error': str(e)}), 500

@register_config_bp.route('/api/register_config/batch_update', methods=['POST'])
@login_required
def batch_update_registers():
    """批量更新寄存器配置（新版本）"""
    try:
        data = request.get_json()
        updates = data.get('updates', [])

        if not updates:
            return jsonify({'success': False, 'message': '没有要更新的数据'}), 400

        updated_count = 0

        for update in updates:
            config_id = update.get('id')
            if not config_id:
                continue

            config = RegisterConfig.query.get(config_id)
            if not config:
                continue

            # 更新字段
            for field, value in update.items():
                if field == 'id':
                    continue

                if hasattr(config, field):
                    # 处理布尔值
                    if field == 'is_active':
                        value = bool(value)
                    # 处理数值
                    elif field in ['register_address', 'scale_factor']:
                        try:
                            if field == 'scale_factor':
                                value = float(value) if value else 1.0
                            else:
                                value = int(value) if value else 0
                        except (ValueError, TypeError):
                            continue

                    setattr(config, field, value)

            updated_count += 1

        db.session.commit()

        logger.info(f"批量更新寄存器配置: {updated_count} 个")
        return jsonify({'success': True, 'message': f'批量更新成功，共更新 {updated_count} 个配置'})

    except Exception as e:
        db.session.rollback()
        logger.error(f"批量更新寄存器配置失败: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@register_config_bp.route('/api/register_config', methods=['POST'])
@login_required
def create_register():
    """创建新寄存器配置"""
    try:
        data = request.get_json()

        # 验证必填字段
        required_fields = ['register_address', 'category']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'success': False, 'message': f'缺少必填字段: {field}'}), 400

        # 检查地址是否已存在
        existing = RegisterConfig.query.filter_by(register_address=data['register_address']).first()
        if existing:
            return jsonify({'success': False, 'message': '该寄存器地址已存在'}), 400

        # 创建新配置
        config = RegisterConfig(
            register_address=int(data['register_address']),
            register_name=data.get('register_name', ''),
            category=data['category'],
            data_type=data.get('data_type', 'uint16'),
            unit=data.get('unit', ''),
            scale_factor=float(data.get('scale_factor', 1.0)),
            description=data.get('description', ''),
            is_active=bool(data.get('is_active', True))
        )

        db.session.add(config)
        db.session.commit()

        logger.info(f"创建新寄存器配置: 地址 {config.register_address}")
        return jsonify({'success': True, 'message': '寄存器配置创建成功', 'id': config.id})

    except Exception as e:
        db.session.rollback()
        logger.error(f"创建寄存器配置失败: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@register_config_bp.route('/api/register_config/<int:config_id>', methods=['DELETE'])
@login_required
def delete_register(config_id):
    """删除寄存器配置"""
    try:
        config = RegisterConfig.query.get_or_404(config_id)

        db.session.delete(config)
        db.session.commit()

        logger.info(f"删除寄存器配置: ID {config_id}, 地址 {config.register_address}")
        return jsonify({'success': True, 'message': '寄存器配置删除成功'})

    except Exception as e:
        db.session.rollback()
        logger.error(f"删除寄存器配置失败: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500
