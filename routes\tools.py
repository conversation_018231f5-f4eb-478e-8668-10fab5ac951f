from flask import Blueprint, render_template, jsonify, request
from flask_login import login_required

# 创建工具相关的蓝图
tools_bp = Blueprint('tools', __name__, url_prefix='/tools')

@tools_bp.route('/serial')
@login_required
def serial():
    """串口工具页面"""
    return render_template('tools/serial.html') 

@tools_bp.route('/toolbox')
@login_required
def toolbox():
    """工具箱页面"""
    return render_template('tools/toolbox.html') 