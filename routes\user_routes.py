from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from models.database import db
from models.user import User
from models.login_log import LoginLog
from datetime import datetime

# 创建蓝图
user_bp = Blueprint('user', __name__)

@user_bp.route('/users')
@login_required
def user_list():
    """用户列表页面"""
    if not current_user.is_admin:
        flash('无权限访问', 'danger')
        return redirect(url_for('main.index'))
    
    users = User.query.all()
    return render_template('user/list.html', users=users)

@user_bp.route('/user/add', methods=['GET', 'POST'])
@login_required
def add_user():
    """添加用户"""
    if not current_user.is_admin:
        flash('无权限访问', 'danger')
        return redirect(url_for('main.index'))
    
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        is_admin = request.form.get('is_admin') == 'on'
        
        # 检查用户名是否已存在
        if User.query.filter_by(username=username).first():
            flash('用户名已存在', 'danger')
            return redirect(url_for('user.add_user'))
        
        # 创建新用户
        user = User(username=username, is_admin=is_admin)
        user.set_password(password)
        
        db.session.add(user)
        db.session.commit()
        
        flash('用户添加成功', 'success')
        return redirect(url_for('user.user_list'))
    
    return render_template('user/form.html')

@user_bp.route('/user/edit/<int:id>', methods=['GET', 'POST'])
@login_required
def edit_user(id):
    """编辑用户"""
    if not current_user.is_admin:
        flash('无权限访问', 'danger')
        return redirect(url_for('main.index'))
    
    user = User.query.get_or_404(id)
    
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        is_admin = request.form.get('is_admin') == 'on'
        
        # 检查用户名是否已被其他用户使用
        existing_user = User.query.filter_by(username=username).first()
        if existing_user and existing_user.id != id:
            flash('用户名已存在', 'danger')
            return redirect(url_for('user.edit_user', id=id))
        
        # 更新用户信息
        user.username = username
        if password:  # 只有在提供密码时才更新密码
            user.set_password(password)
        user.is_admin = is_admin
        
        db.session.commit()
        
        flash('用户更新成功', 'success')
        return redirect(url_for('user.user_list'))
    
    return render_template('user/form.html', user=user)

@user_bp.route('/user/delete/<int:id>')
@login_required
def delete_user(id):
    """删除用户"""
    if not current_user.is_admin:
        flash('无权限访问', 'danger')
        return redirect(url_for('main.index'))
    
    user = User.query.get_or_404(id)
    
    # 不允许删除自己
    if user.id == current_user.id:
        flash('不能删除当前登录的用户', 'danger')
        return redirect(url_for('user.user_list'))
    
    # 删除用户
    db.session.delete(user)
    db.session.commit()
    
    flash('用户删除成功', 'success')
    return redirect(url_for('user.user_list'))

@user_bp.route('/user/change_password', methods=['GET', 'POST'])
@login_required
def change_password():
    """修改密码"""
    if request.method == 'POST':
        old_password = request.form.get('old_password')
        new_password = request.form.get('new_password')
        confirm_password = request.form.get('confirm_password')
        
        # 验证旧密码
        if not current_user.check_password(old_password):
            flash('旧密码错误', 'danger')
            return redirect(url_for('user.change_password'))
        
        # 验证新密码
        if new_password != confirm_password:
            flash('两次输入的新密码不一致', 'danger')
            return redirect(url_for('user.change_password'))
        
        # 更新密码
        current_user.set_password(new_password)
        db.session.commit()
        
        flash('密码修改成功', 'success')
        return redirect(url_for('main.index'))
    
    return render_template('user/change_password.html') 