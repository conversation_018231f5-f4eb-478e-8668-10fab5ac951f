#!/usr/bin/env python3
"""
初始化寄存器配置数据
从硬编码的寄存器信息中创建数据库配置
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app_factory import create_app
from models.database import db
from models.register_config import RegisterConfig

def init_register_configs():
    """初始化寄存器配置数据"""
    
    # 寄存器配置数据 - 基于原有的硬编码数据
    register_configs = [
        # 时间参数
        {'address': 0, 'name': 'REG_T1', 'description': '长时间未插入充电器检测时间(单位：s)', 'category': 'time', 'unit': 's'},
        {'address': 1, 'name': 'REG_T2', 'description': '功率大于0连续时间，判定为已连接充电器(单位：s)', 'category': 'time', 'unit': 's'},
        {'address': 2, 'name': 'REG_T3', 'description': '浮充时间，大于该时间判定为电量已满(单位：s)', 'category': 'time', 'unit': 's'},
        {'address': 3, 'name': 'REG_T4', 'description': '功率超过限制判定时间(单位：s)', 'category': 'time', 'unit': 's'},
        {'address': 4, 'name': 'REG_T5', 'description': '总功率超过限制触发时间(单位：ms)', 'category': 'time', 'unit': 'ms'},
        {'address': 5, 'name': 'REG_T6', 'description': '温度超过阈值判定时间(单位：s)', 'category': 'time', 'unit': 's'},
        {'address': 6, 'name': 'REG_T7', 'description': '初始单个口功率过大判定时间(单位：ms)', 'category': 'time', 'unit': 'ms'},
        {'address': 7, 'name': 'REG_T8', 'description': '充电过程中继电器开路状态判断为中控断电的时间(单位：ms)', 'category': 'time', 'unit': 'ms'},
        {'address': 8, 'name': 'REG_T9', 'description': '首次进入充电过程中功率突降为0时的浮充时间(单位：s)', 'category': 'time', 'unit': 's'},
        {'address': 9, 'name': 'REG_T10', 'description': '无线充电浮充时间(单位：s)', 'category': 'time', 'unit': 's'},
        {'address': 18, 'name': 'REG_T11', 'description': '拔出充电器的判定时间(单位：秒)', 'category': 'time', 'unit': 's'},
        
        # 功率参数
        {'address': 10, 'name': 'REG_P1', 'description': '浮充功率阈值(单位：W)', 'category': 'power', 'unit': 'W'},
        {'address': 11, 'name': 'REG_P2', 'description': '单口充电过程中的功率限制(单位：W)', 'category': 'power', 'unit': 'W'},
        {'address': 12, 'name': 'REG_P3', 'description': '单口充电过程中的安全功率限制(单位：W)', 'category': 'power', 'unit': 'W'},
        {'address': 13, 'name': 'REG_P4', 'description': '总功率限制(单位：W)', 'category': 'power', 'unit': 'W'},
        {'address': 14, 'name': 'REG_P5', 'description': '单口初始安全功率限制(单位：W)', 'category': 'power', 'unit': 'W'},
        {'address': 15, 'name': 'REG_P6', 'description': '启动充电后检测充电负载存在阈值(单位：W)', 'category': 'power', 'unit': 'W'},
        {'address': 16, 'name': 'REG_P7', 'description': '无线充电浮充功率阈值(单位：W)', 'category': 'power', 'unit': 'W'},
        {'address': 17, 'name': 'REG_P8', 'description': '判断是否接入用电设备的阈值，小于这个阈值判定为用电设备断开与插座的连接(单位：V5板子为BL0910的有功功率的寄存器值，V2板子为mW)', 'category': 'power', 'unit': 'W'},
        
        # 其他参数
        {'address': 19, 'name': 'REG_CTRL1', 'description': '控制寄存器，bit0: 控制SIM卡拔出功能，bit1: 控制LED闪烁模式。', 'category': 'other', 'data_type': 'hex'},
        {'address': 20, 'name': 'REG_TEMP1', 'description': '过温保护阈值(单位：℃)', 'category': 'other', 'unit': '℃'},
        {'address': 21, 'name': 'REG_BOOT_CNT', 'description': '启动计数', 'category': 'other'},
        {'address': 22, 'name': 'REG_VERSION_H', 'description': '版本号高字节', 'category': 'other', 'data_type': 'hex'},
        {'address': 23, 'name': 'REG_VERSION_L', 'description': '版本号低字节', 'category': 'other', 'data_type': 'hex'},
        {'address': 24, 'name': 'REG_PERSENTAGE', 'description': '拔出插头判定百分比(单位：%)', 'category': 'other', 'unit': '%', 'data_type': 'float'},
        {'address': 25, 'name': 'REG_CSQ', 'description': '信号强度(CSQ)和误码率(BER)', 'category': 'other', 'data_type': 'hex'},
        {'address': 26, 'name': 'REG_LOCATION_CODE', 'description': '位置编码', 'category': 'other'},
        {'address': 27, 'name': 'REG_LOCATION_LATITUDE_H', 'description': '纬度高字节', 'category': 'other'},
        {'address': 28, 'name': 'REG_LOCATION_LATITUDE_L', 'description': '纬度低字节', 'category': 'other'},
        {'address': 29, 'name': 'REG_LOCATION_LONGITUDE_H', 'description': '经度高字节', 'category': 'other'},
        {'address': 30, 'name': 'REG_LOCATION_LONGITUDE_L', 'description': '经度低字节', 'category': 'other'},
        {'address': 31, 'name': 'REG_ERROR_CNT1', 'description': '临时错误计数器1：高字节为服务器掉线次数，低字节为SIM卡被拔出的次数', 'category': 'other'},
        {'address': 32, 'name': 'REG_ERROR_CNT2', 'description': '临时错误计数器2：高字节为电压过零中断周期小于工频周期的次数，低字节为电压过零中断周期大于工频周期的次数', 'category': 'other'},
        {'address': 33, 'name': 'REG_UID_PROTECT_KEY1', 'description': 'UID保护密钥1：key1', 'category': 'other'},
        {'address': 34, 'name': 'REG_HEART_AND_BILLING_PROTO_TYPE', 'description': 'MQTT服务器的类型', 'category': 'other'},
        {'address': 35, 'name': 'REG_RESERV3', 'description': '保留3', 'category': 'other'},
        {'address': 36, 'name': 'REG_RESERV4', 'description': '保留4', 'category': 'other'},
        {'address': 37, 'name': 'REG_FACTORY_FAULT', 'description': '工厂故障记录1', 'category': 'other'},
        {'address': 38, 'name': 'REG_FACTORY_FAULT2', 'description': '工厂故障记录2', 'category': 'other'},
    ]
    
    # 插座功率阈值参数
    for plug_num in range(11):  # 插座0-10
        for param_type, param_desc in [('P2', 'P2功率阈值'), ('P3', 'P3功率阈值'), ('P5', 'P5功率阈值')]:
            address = 39 + plug_num * 3 + (['P2', 'P3', 'P5'].index(param_type))
            if address <= 77:  # 确保不超过最大地址
                register_configs.append({
                    'address': address,
                    'name': f'REG_{param_type}_PLUG{plug_num}',
                    'description': f'插座{plug_num}的{param_desc}',
                    'category': 'power',
                    'unit': 'W'
                })
    
    # 保留寄存器
    for i, addr in enumerate([72, 73, 74, 75, 76], 5):
        register_configs.append({
            'address': addr,
            'name': f'REG_RESERV{i}',
            'description': f'保留{i}',
            'category': 'other'
        })
    
    # UID保护密钥2
    register_configs.append({
        'address': 77,
        'name': 'REG_UID_PROTECT_KEY2',
        'description': 'UID保护密钥2：key2',
        'category': 'other'
    })
    
    # 创建或更新寄存器配置
    created_count = 0
    updated_count = 0
    
    for config_data in register_configs:
        # 检查是否已存在
        existing = RegisterConfig.query.filter_by(register_address=config_data['address']).first()
        
        if existing:
            # 更新现有配置
            existing.register_name = config_data['name']
            existing.description = config_data['description']
            existing.category = config_data['category']
            existing.unit = config_data.get('unit', '')
            existing.data_type = config_data.get('data_type', 'integer')
            existing.is_active = True
            updated_count += 1
        else:
            # 创建新配置
            new_config = RegisterConfig(
                register_address=config_data['address'],
                register_name=config_data['name'],
                description=config_data['description'],
                category=config_data['category'],
                unit=config_data.get('unit', ''),
                data_type=config_data.get('data_type', 'integer'),
                is_active=True
            )
            db.session.add(new_config)
            created_count += 1
    
    # 提交更改
    db.session.commit()
    
    print(f"寄存器配置初始化完成:")
    print(f"  创建: {created_count} 个")
    print(f"  更新: {updated_count} 个")
    print(f"  总计: {len(register_configs)} 个")

if __name__ == '__main__':
    app = create_app()
    with app.app_context():
        init_register_configs()
