import os
from datetime import datetime
from alipay import AliPay
from alipay.utils import AliPayConfig
from models.database import db
from models.paid_download import DownloadOrder
from utils.logger import setup_logging
import logging
from flask import current_app

logger = logging.getLogger(__name__)

"""
ALIPAY_APP_ID=你的应用ID
ALIPAY_PRIVATE_KEY_PATH=商户私钥文件路径
ALIPAY_PUBLIC_KEY_PATH=支付宝公钥文件路径
ALIPAY_NOTIFY_URL=支付回调通知地址
ALIPAY_RETURN_URL=支付完成后的跳转地址
ALIPAY_DEBUG=True/False  # 是否使用沙箱环境
"""

class AlipayService:
    """支付宝支付服务"""
    
    def __init__(self):
        """初始化支付宝配置"""
        self.app_id = current_app.config['ALIPAY_APP_ID']
        self.app_private_key_path = current_app.config['ALIPAY_PRIVATE_KEY_PATH']
        self.alipay_public_key_path = current_app.config['ALIPAY_PUBLIC_KEY_PATH']
        
        # 读取密钥文件
        with open(self.app_private_key_path) as f:
            app_private_key_string = f.read()
        with open(self.alipay_public_key_path) as f:
            alipay_public_key_string = f.read()
            
        # 初始化支付宝客户端
        self.alipay = AliPay(
            appid=self.app_id,
            app_notify_url=current_app.config['ALIPAY_NOTIFY_URL'],
            app_private_key_string=app_private_key_string,
            alipay_public_key_string=alipay_public_key_string,
            sign_type="RSA2",
            debug=current_app.config['ALIPAY_DEBUG'],
            config=AliPayConfig(timeout=15)
        )
    
    def create_payment(self, order_no, amount, subject):
        """创建支付宝支付订单
        
        Args:
            order_no: 订单号
            amount: 支付金额
            subject: 订单标题
            
        Returns:
            dict: 包含支付链接的响应
        """
        try:
            # 生成支付链接
            order_string = self.alipay.api_alipay_trade_page_pay(
                out_trade_no=order_no,
                total_amount=str(amount),
                subject=subject,
                return_url=current_app.config['ALIPAY_RETURN_URL'],
                notify_url=current_app.config['ALIPAY_NOTIFY_URL']
            )
            
            # 构建完整的支付链接
            pay_url = f"https://openapi.alipay.com/gateway.do?{order_string}"
            
            return {
                'success': True,
                'pay_url': pay_url
            }
            
        except Exception as e:
            logger.error(f"创建支付宝订单失败: {str(e)}")
            return {
                'success': False,
                'message': '创建支付订单失败'
            }
    
    def verify_payment(self, data):
        """验证支付宝支付回调
        
        Args:
            data: 支付宝回调数据
            
        Returns:
            bool: 验证是否成功
        """
        try:
            # 验证签名
            signature = data.pop("sign")
            success = self.alipay.verify(data, signature)
            
            if success:
                # 验证交易状态
                trade_status = data.get('trade_status')
                return trade_status in ['TRADE_SUCCESS', 'TRADE_FINISHED']
            
            return False
            
        except Exception as e:
            logger.error(f"验证支付宝回调失败: {str(e)}")
            return False 