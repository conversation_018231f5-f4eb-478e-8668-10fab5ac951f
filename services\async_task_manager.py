"""
异步任务管理器
用于处理设备参数查询等耗时操作，避免阻塞用户界面
"""

import threading
import time
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Callable
import logging

logger = logging.getLogger(__name__)

class AsyncTask:
    """异步任务类"""
    
    def __init__(self, task_id: str, task_type: str, device_id: str, func: Callable, *args, **kwargs):
        self.task_id = task_id
        self.task_type = task_type
        self.device_id = device_id
        self.func = func
        self.args = args
        self.kwargs = kwargs
        self.status = 'pending'  # pending, running, completed, failed
        self.result = None
        self.error = None
        self.created_at = datetime.now()
        self.started_at = None
        self.completed_at = None
        self.progress = 0
        
    def run(self):
        """执行任务"""
        try:
            self.status = 'running'
            self.started_at = datetime.now()
            logger.info(f"开始执行异步任务 {self.task_id}: {self.task_type}")
            
            # 执行任务函数
            self.result = self.func(*self.args, **self.kwargs)
            
            self.status = 'completed'
            self.progress = 100
            self.completed_at = datetime.now()
            logger.info(f"异步任务 {self.task_id} 执行完成")
            
        except Exception as e:
            self.status = 'failed'
            self.error = str(e)
            self.completed_at = datetime.now()
            logger.error(f"异步任务 {self.task_id} 执行失败: {e}")
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'task_id': self.task_id,
            'task_type': self.task_type,
            'device_id': self.device_id,
            'status': self.status,
            'result': self.result,
            'error': self.error,
            'progress': self.progress,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
        }

class AsyncTaskManager:
    """异步任务管理器"""
    
    def __init__(self):
        self.tasks: Dict[str, AsyncTask] = {}
        self.lock = threading.Lock()
        self.cleanup_thread = None
        self.running = False
        
    def start(self):
        """启动任务管理器"""
        if not self.running:
            self.running = True
            self.cleanup_thread = threading.Thread(target=self._cleanup_old_tasks, daemon=True)
            self.cleanup_thread.start()
            logger.info("异步任务管理器已启动")
    
    def stop(self):
        """停止任务管理器"""
        self.running = False
        if self.cleanup_thread:
            self.cleanup_thread.join(timeout=5)
        logger.info("异步任务管理器已停止")
    
    def submit_task(self, task_type: str, device_id: str, func: Callable, *args, **kwargs) -> str:
        """提交异步任务"""
        task_id = str(uuid.uuid4())
        task = AsyncTask(task_id, task_type, device_id, func, *args, **kwargs)
        
        with self.lock:
            self.tasks[task_id] = task
        
        # 在新线程中执行任务
        thread = threading.Thread(target=task.run, daemon=True)
        thread.start()
        
        logger.info(f"提交异步任务 {task_id}: {task_type} for device {device_id}")
        return task_id
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        with self.lock:
            task = self.tasks.get(task_id)
            if task:
                return task.to_dict()
        return None
    
    def get_device_tasks(self, device_id: str, task_type: Optional[str] = None) -> list:
        """获取设备的所有任务"""
        with self.lock:
            tasks = []
            for task in self.tasks.values():
                if task.device_id == device_id:
                    if task_type is None or task.task_type == task_type:
                        tasks.append(task.to_dict())
            return sorted(tasks, key=lambda x: x['created_at'], reverse=True)
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务（只能取消pending状态的任务）"""
        with self.lock:
            task = self.tasks.get(task_id)
            if task and task.status == 'pending':
                task.status = 'cancelled'
                task.completed_at = datetime.now()
                logger.info(f"任务 {task_id} 已取消")
                return True
        return False
    
    def _cleanup_old_tasks(self):
        """清理旧任务"""
        while self.running:
            try:
                cutoff_time = datetime.now() - timedelta(hours=1)  # 保留1小时内的任务
                
                with self.lock:
                    tasks_to_remove = []
                    for task_id, task in self.tasks.items():
                        if task.completed_at and task.completed_at < cutoff_time:
                            tasks_to_remove.append(task_id)
                    
                    for task_id in tasks_to_remove:
                        del self.tasks[task_id]
                    
                    if tasks_to_remove:
                        logger.info(f"清理了 {len(tasks_to_remove)} 个旧任务")
                
                # 每10分钟清理一次
                time.sleep(600)
                
            except Exception as e:
                logger.error(f"清理旧任务时出错: {e}")
                time.sleep(60)

# 全局任务管理器实例
_task_manager = None

def get_task_manager() -> AsyncTaskManager:
    """获取全局任务管理器实例"""
    global _task_manager
    if _task_manager is None:
        _task_manager = AsyncTaskManager()
        _task_manager.start()
    return _task_manager

def init_task_manager():
    """初始化任务管理器"""
    get_task_manager()

def shutdown_task_manager():
    """关闭任务管理器"""
    global _task_manager
    if _task_manager:
        _task_manager.stop()
        _task_manager = None
