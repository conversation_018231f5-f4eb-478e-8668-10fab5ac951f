#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据导出服务
用于导出时序数据到Excel文件
"""

import os
import io
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
from flask import current_app

from services.time_series_service import time_series_service
from models.device import Device
from utils.logger import LoggerManager

# 获取日志记录器
logger = LoggerManager.get_logger()


class DataExportService:
    """数据导出服务类"""
    
    def __init__(self):
        self.supported_formats = ['xlsx', 'csv']
        self.max_export_days = 30  # 最大导出天数限制
        self.max_records_per_export = 100000  # 单次导出最大记录数
        
    def export_single_data_type(self, device_id: str, data_type: str, 
                               start_date: str, end_date: str = None,
                               format_type: str = 'xlsx') -> Dict[str, Any]:
        """
        导出单个数据类型的数据
        
        Args:
            device_id: 设备ID
            data_type: 数据类型
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)，如果为None则使用start_date
            format_type: 导出格式 ('xlsx' 或 'csv')
            
        Returns:
            Dict: 包含文件内容和元信息的字典
        """
        try:
            # 验证参数
            if format_type not in self.supported_formats:
                raise ValueError(f"不支持的导出格式: {format_type}")
            
            # 解析日期
            start_time = datetime.strptime(start_date, '%Y-%m-%d')
            if end_date:
                end_time = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)
            else:
                end_time = start_time + timedelta(days=1)
            
            # 检查日期范围
            date_diff = (end_time - start_time).days
            if date_diff > self.max_export_days:
                raise ValueError(f"导出日期范围不能超过{self.max_export_days}天")
            
            # 获取设备信息
            device = Device.query.filter_by(device_id=device_id).first()
            if not device:
                raise ValueError(f"设备 {device_id} 不存在")
            
            # 查询数据
            if data_type == 'power':
                data = self._export_power_data(device_id, start_time, end_time)
            else:
                data = self._export_single_value_data(device_id, data_type, start_time, end_time)
            
            if not data:
                raise ValueError("没有找到数据")
            
            # 检查记录数限制
            total_records = len(data)
            if total_records > self.max_records_per_export:
                raise ValueError(f"数据量过大({total_records}条)，请缩小日期范围")
            
            # 生成文件
            if format_type == 'xlsx':
                file_content, filename = self._generate_excel_file(
                    data, device, data_type, start_date, end_date
                )
            else:
                file_content, filename = self._generate_csv_file(
                    data, device, data_type, start_date, end_date
                )
            
            return {
                'success': True,
                'file_content': file_content,
                'filename': filename,
                'content_type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' if format_type == 'xlsx' else 'text/csv',
                'total_records': total_records,
                'data_type': data_type,
                'device_id': device_id,
                'date_range': f"{start_date} 至 {end_date or start_date}"
            }
            
        except Exception as e:
            logger.error(f"导出单个数据类型失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def export_batch_data(self, device_id: str, data_types: List[str],
                         start_date: str, end_date: str = None,
                         format_type: str = 'xlsx') -> Dict[str, Any]:
        """
        批量导出多个数据类型的数据
        
        Args:
            device_id: 设备ID
            data_types: 数据类型列表
            start_date: 开始日期
            end_date: 结束日期
            format_type: 导出格式
            
        Returns:
            Dict: 包含文件内容和元信息的字典
        """
        try:
            # 验证参数
            if not data_types:
                raise ValueError("请选择要导出的数据类型")
            
            # 解析日期
            start_time = datetime.strptime(start_date, '%Y-%m-%d')
            if end_date:
                end_time = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)
            else:
                end_time = start_time + timedelta(days=1)
            
            # 检查日期范围
            date_diff = (end_time - start_time).days
            if date_diff > self.max_export_days:
                raise ValueError(f"导出日期范围不能超过{self.max_export_days}天")
            
            # 获取设备信息
            device = Device.query.filter_by(device_id=device_id).first()
            if not device:
                raise ValueError(f"设备 {device_id} 不存在")
            
            # 收集所有数据
            all_data = {}
            total_records = 0
            
            for data_type in data_types:
                try:
                    if data_type == 'power':
                        data = self._export_power_data(device_id, start_time, end_time)
                    else:
                        data = self._export_single_value_data(device_id, data_type, start_time, end_time)
                    
                    if data:
                        all_data[data_type] = data
                        total_records += len(data)
                        
                except Exception as e:
                    logger.warning(f"导出{data_type}数据失败: {e}")
                    continue
            
            if not all_data:
                raise ValueError("没有找到任何数据")
            
            # 检查记录数限制
            if total_records > self.max_records_per_export:
                raise ValueError(f"数据量过大({total_records}条)，请缩小日期范围或减少数据类型")
            
            # 生成文件
            if format_type == 'xlsx':
                file_content, filename = self._generate_batch_excel_file(
                    all_data, device, start_date, end_date
                )
            else:
                file_content, filename = self._generate_batch_csv_file(
                    all_data, device, start_date, end_date
                )
            
            return {
                'success': True,
                'file_content': file_content,
                'filename': filename,
                'content_type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' if format_type == 'xlsx' else 'text/csv',
                'total_records': total_records,
                'data_types': list(all_data.keys()),
                'device_id': device_id,
                'date_range': f"{start_date} 至 {end_date or start_date}"
            }
            
        except Exception as e:
            logger.error(f"批量导出数据失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _export_power_data(self, device_id: str, start_time: datetime, end_time: datetime) -> List[Dict]:
        """导出功率数据"""
        power_data = time_series_service.query_power_data(device_id, start_time, end_time)
        
        # 转换功率数据格式
        result = []
        
        # 合并所有通道的数据
        all_timestamps = set()
        for channel_data in power_data.values():
            for point in channel_data:
                all_timestamps.add(point['time'])
        
        # 按时间戳组织数据
        for timestamp in sorted(all_timestamps):
            row = {
                'timestamp': timestamp,
                'device_id': device_id,
                'data_type': 'power'
            }
            
            # 添加各通道功率值
            for i in range(1, 11):
                channel_key = f'channel_{i}'
                channel_data = power_data.get(channel_key, [])
                
                # 查找对应时间戳的数据
                value = None
                for point in channel_data:
                    if point['time'] == timestamp:
                        value = point['value']
                        break
                
                row[f'channel_{i}_power'] = value
            
            result.append(row)
        
        return result
    
    def _export_single_value_data(self, device_id: str, data_type: str, 
                                 start_time: datetime, end_time: datetime) -> List[Dict]:
        """导出单值数据"""
        if data_type == 'csq':
            data = time_series_service.query_csq_data(device_id, start_time, end_time)
            # CSQ数据包含BER信息
            result = []
            for point in data:
                result.append({
                    'timestamp': point['time'],
                    'device_id': device_id,
                    'data_type': 'csq',
                    'value': point['value'],
                    'ber': point.get('ber')
                })
            return result
        else:
            data = time_series_service.query_single_value_data(device_id, data_type, start_time, end_time)
            return [
                {
                    'timestamp': point['time'],
                    'device_id': device_id,
                    'data_type': data_type,
                    'value': point['value']
                }
                for point in data
            ]

    def _generate_excel_file(self, data: List[Dict], device: Device, data_type: str,
                           start_date: str, end_date: str = None) -> tuple:
        """生成Excel文件"""
        try:
            # 创建工作簿
            wb = Workbook()
            ws = wb.active
            ws.title = f"{data_type.upper()}数据"

            # 设置样式
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            header_alignment = Alignment(horizontal="center", vertical="center")
            border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            # 写入标题信息
            ws['A1'] = f"设备 {device.device_id} - {data_type.upper()}数据导出"
            ws['A1'].font = Font(bold=True, size=14)
            ws.merge_cells('A1:E1')

            ws['A2'] = f"导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            ws['A3'] = f"数据日期: {start_date}" + (f" 至 {end_date}" if end_date and end_date != start_date else "")
            ws['A4'] = f"记录总数: {len(data)}"

            # 确定列标题
            if data_type == 'power':
                headers = ['时间戳', '设备ID', '数据类型'] + [f'通道{i}功率(W)' for i in range(1, 11)]
            elif data_type == 'csq':
                headers = ['时间戳', '设备ID', '数据类型', 'CSQ值', 'BER值']
            else:
                headers = ['时间戳', '设备ID', '数据类型', '数值']

            # 写入表头
            header_row = 6
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=header_row, column=col, value=header)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment
                cell.border = border

            # 写入数据
            for row_idx, record in enumerate(data, header_row + 1):
                if data_type == 'power':
                    row_data = [
                        record['timestamp'],
                        record['device_id'],
                        record['data_type']
                    ] + [record.get(f'channel_{i}_power') for i in range(1, 11)]
                elif data_type == 'csq':
                    row_data = [
                        record['timestamp'],
                        record['device_id'],
                        record['data_type'],
                        record['value'],
                        record.get('ber')
                    ]
                else:
                    row_data = [
                        record['timestamp'],
                        record['device_id'],
                        record['data_type'],
                        record['value']
                    ]

                for col, value in enumerate(row_data, 1):
                    cell = ws.cell(row=row_idx, column=col, value=value)
                    cell.border = border

                    # 格式化时间戳
                    if col == 1 and isinstance(value, str):
                        try:
                            dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
                            cell.value = dt.strftime('%Y-%m-%d %H:%M:%S')
                        except:
                            pass

            # 调整列宽
            for col in range(1, len(headers) + 1):
                ws.column_dimensions[chr(64 + col)].width = 15

            # 生成文件名
            date_suffix = start_date if not end_date or end_date == start_date else f"{start_date}_to_{end_date}"
            filename = f"{device.device_id}_{data_type}_data_{date_suffix}.xlsx"

            # 保存到内存
            output = io.BytesIO()
            wb.save(output)
            output.seek(0)

            return output.getvalue(), filename

        except Exception as e:
            logger.error(f"生成Excel文件失败: {e}")
            raise

    def _generate_csv_file(self, data: List[Dict], device: Device, data_type: str,
                          start_date: str, end_date: str = None) -> tuple:
        """生成CSV文件"""
        try:
            # 转换为DataFrame
            df = pd.DataFrame(data)

            # 格式化时间戳
            if 'timestamp' in df.columns:
                df['timestamp'] = pd.to_datetime(df['timestamp']).dt.strftime('%Y-%m-%d %H:%M:%S')

            # 生成文件名
            date_suffix = start_date if not end_date or end_date == start_date else f"{start_date}_to_{end_date}"
            filename = f"{device.device_id}_{data_type}_data_{date_suffix}.csv"

            # 保存到内存
            output = io.StringIO()
            df.to_csv(output, index=False, encoding='utf-8-sig')

            return output.getvalue().encode('utf-8-sig'), filename

        except Exception as e:
            logger.error(f"生成CSV文件失败: {e}")
            raise

    def _generate_batch_excel_file(self, all_data: Dict[str, List[Dict]], device: Device,
                                  start_date: str, end_date: str = None) -> tuple:
        """生成批量导出的Excel文件"""
        try:
            # 创建工作簿
            wb = Workbook()

            # 删除默认工作表
            wb.remove(wb.active)

            # 为每种数据类型创建工作表
            for data_type, data in all_data.items():
                ws = wb.create_sheet(title=f"{data_type.upper()}数据")

                # 设置样式
                header_font = Font(bold=True, color="FFFFFF")
                header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                header_alignment = Alignment(horizontal="center", vertical="center")
                border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )

                # 写入标题信息
                ws['A1'] = f"{data_type.upper()}数据"
                ws['A1'].font = Font(bold=True, size=12)

                # 确定列标题
                if data_type == 'power':
                    headers = ['时间戳', '设备ID', '数据类型'] + [f'通道{i}功率(W)' for i in range(1, 11)]
                elif data_type == 'csq':
                    headers = ['时间戳', '设备ID', '数据类型', 'CSQ值', 'BER值']
                else:
                    headers = ['时间戳', '设备ID', '数据类型', '数值']

                # 写入表头
                header_row = 3
                for col, header in enumerate(headers, 1):
                    cell = ws.cell(row=header_row, column=col, value=header)
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = header_alignment
                    cell.border = border

                # 写入数据
                for row_idx, record in enumerate(data, header_row + 1):
                    if data_type == 'power':
                        row_data = [
                            record['timestamp'],
                            record['device_id'],
                            record['data_type']
                        ] + [record.get(f'channel_{i}_power') for i in range(1, 11)]
                    elif data_type == 'csq':
                        row_data = [
                            record['timestamp'],
                            record['device_id'],
                            record['data_type'],
                            record['value'],
                            record.get('ber')
                        ]
                    else:
                        row_data = [
                            record['timestamp'],
                            record['device_id'],
                            record['data_type'],
                            record['value']
                        ]

                    for col, value in enumerate(row_data, 1):
                        cell = ws.cell(row=row_idx, column=col, value=value)
                        cell.border = border

                        # 格式化时间戳
                        if col == 1 and isinstance(value, str):
                            try:
                                dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
                                cell.value = dt.strftime('%Y-%m-%d %H:%M:%S')
                            except:
                                pass

                # 调整列宽
                for col in range(1, len(headers) + 1):
                    ws.column_dimensions[chr(64 + col)].width = 15

            # 创建汇总工作表
            summary_ws = wb.create_sheet(title="导出汇总", index=0)
            summary_ws['A1'] = f"设备 {device.device_id} - 批量数据导出"
            summary_ws['A1'].font = Font(bold=True, size=14)

            summary_ws['A3'] = f"导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            summary_ws['A4'] = f"数据日期: {start_date}" + (f" 至 {end_date}" if end_date and end_date != start_date else "")
            summary_ws['A5'] = f"数据类型: {', '.join(all_data.keys())}"
            summary_ws['A6'] = f"总记录数: {sum(len(data) for data in all_data.values())}"

            # 生成文件名
            date_suffix = start_date if not end_date or end_date == start_date else f"{start_date}_to_{end_date}"
            filename = f"{device.device_id}_batch_data_{date_suffix}.xlsx"

            # 保存到内存
            output = io.BytesIO()
            wb.save(output)
            output.seek(0)

            return output.getvalue(), filename

        except Exception as e:
            logger.error(f"生成批量Excel文件失败: {e}")
            raise

    def _generate_batch_csv_file(self, all_data: Dict[str, List[Dict]], device: Device,
                                start_date: str, end_date: str = None) -> tuple:
        """生成批量导出的CSV文件（合并所有数据）"""
        try:
            # 合并所有数据
            combined_data = []
            for data_type, data in all_data.items():
                combined_data.extend(data)

            # 按时间戳排序
            combined_data.sort(key=lambda x: x['timestamp'])

            # 转换为DataFrame
            df = pd.DataFrame(combined_data)

            # 格式化时间戳
            if 'timestamp' in df.columns:
                df['timestamp'] = pd.to_datetime(df['timestamp']).dt.strftime('%Y-%m-%d %H:%M:%S')

            # 生成文件名
            date_suffix = start_date if not end_date or end_date == start_date else f"{start_date}_to_{end_date}"
            filename = f"{device.device_id}_batch_data_{date_suffix}.csv"

            # 保存到内存
            output = io.StringIO()
            df.to_csv(output, index=False, encoding='utf-8-sig')

            return output.getvalue().encode('utf-8-sig'), filename

        except Exception as e:
            logger.error(f"生成批量CSV文件失败: {e}")
            raise


    def export_device_parameters(self, device_id: str, format_type: str = 'xlsx') -> Dict[str, Any]:
        """
        导出设备参数数据

        Args:
            device_id: 设备ID
            format_type: 导出格式 ('xlsx' 或 'csv')

        Returns:
            Dict: 包含文件内容和元信息的字典
        """
        try:
            # 验证参数
            if format_type not in self.supported_formats:
                raise ValueError(f"不支持的导出格式: {format_type}")

            # 获取设备信息
            device = Device.query.filter_by(device_id=device_id).first()
            if not device:
                raise ValueError(f"设备 {device_id} 不存在")

            # 获取设备参数数据
            parameters_data = self._get_device_parameters(device)

            if not parameters_data:
                raise ValueError("没有找到设备参数数据")

            # 生成文件
            if format_type == 'xlsx':
                file_content, filename = self._generate_parameters_excel_file(parameters_data, device)
            else:
                file_content, filename = self._generate_parameters_csv_file(parameters_data, device)

            return {
                'success': True,
                'file_content': file_content,
                'filename': filename,
                'content_type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' if format_type == 'xlsx' else 'text/csv',
                'total_records': len(parameters_data),
                'device_id': device_id,
                'export_type': 'device_parameters'
            }

        except Exception as e:
            logger.error(f"导出设备参数失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def _get_device_parameters(self, device: Device) -> List[Dict]:
        """获取设备参数数据"""
        try:
            # 这里需要根据实际的设备参数存储方式来获取数据
            # 假设设备参数存储在device对象的某个字段中，或者有专门的参数表

            parameters_data = []

            # 基本设备信息
            basic_info = [
                {'category': '基本信息', 'parameter': '设备ID', 'value': device.device_id, 'unit': '', 'description': '设备唯一标识符'},
                {'category': '基本信息', 'parameter': '设备名称', 'value': getattr(device, 'name', ''), 'unit': '', 'description': '设备名称'},
                {'category': '基本信息', 'parameter': '设备类型', 'value': getattr(device, 'device_type', ''), 'unit': '', 'description': '设备类型'},
                {'category': '基本信息', 'parameter': '固件版本', 'value': getattr(device, 'firmware_version', ''), 'unit': '', 'description': '设备固件版本'},
                {'category': '基本信息', 'parameter': '硬件版本', 'value': getattr(device, 'hardware_version', ''), 'unit': '', 'description': '设备硬件版本'},
                {'category': '基本信息', 'parameter': '创建时间', 'value': device.created_at.strftime('%Y-%m-%d %H:%M:%S') if device.created_at else '', 'unit': '', 'description': '设备创建时间'},
                {'category': '基本信息', 'parameter': '更新时间', 'value': device.updated_at.strftime('%Y-%m-%d %H:%M:%S') if device.updated_at else '', 'unit': '', 'description': '设备最后更新时间'},
            ]

            parameters_data.extend(basic_info)

            # 如果设备有额外的参数字段，可以在这里添加
            # 例如：如果有device.parameters字段存储JSON格式的参数
            if hasattr(device, 'parameters') and device.parameters:
                try:
                    import json
                    params = json.loads(device.parameters) if isinstance(device.parameters, str) else device.parameters

                    for category, params_dict in params.items():
                        if isinstance(params_dict, dict):
                            for param_name, param_info in params_dict.items():
                                if isinstance(param_info, dict):
                                    parameters_data.append({
                                        'category': category,
                                        'parameter': param_name,
                                        'value': param_info.get('value', ''),
                                        'unit': param_info.get('unit', ''),
                                        'description': param_info.get('description', '')
                                    })
                                else:
                                    parameters_data.append({
                                        'category': category,
                                        'parameter': param_name,
                                        'value': str(param_info),
                                        'unit': '',
                                        'description': ''
                                    })
                except Exception as e:
                    logger.warning(f"解析设备参数失败: {e}")

            # 添加一些模拟的参数数据（实际使用时应该从真实数据源获取）
            sample_params = [
                {'category': '电气参数', 'parameter': '额定电压', 'value': '220', 'unit': 'V', 'description': '设备额定工作电压'},
                {'category': '电气参数', 'parameter': '额定电流', 'value': '10', 'unit': 'A', 'description': '设备额定工作电流'},
                {'category': '电气参数', 'parameter': '额定功率', 'value': '2200', 'unit': 'W', 'description': '设备额定功率'},
                {'category': '通信参数', 'parameter': 'IP地址', 'value': getattr(device, 'ip_address', ''), 'unit': '', 'description': '设备IP地址'},
                {'category': '通信参数', 'parameter': '端口号', 'value': getattr(device, 'port', ''), 'unit': '', 'description': '设备通信端口'},
                {'category': '环境参数', 'parameter': '工作温度范围', 'value': '-20~60', 'unit': '°C', 'description': '设备正常工作温度范围'},
                {'category': '环境参数', 'parameter': '存储温度范围', 'value': '-40~85', 'unit': '°C', 'description': '设备存储温度范围'},
                {'category': '环境参数', 'parameter': '相对湿度', 'value': '5~95', 'unit': '%RH', 'description': '设备工作相对湿度范围'},
            ]

            parameters_data.extend(sample_params)

            return parameters_data

        except Exception as e:
            logger.error(f"获取设备参数数据失败: {e}")
            return []

    def _generate_parameters_excel_file(self, parameters_data: List[Dict], device: Device) -> tuple:
        """生成设备参数Excel文件"""
        try:
            # 创建工作簿
            wb = Workbook()
            ws = wb.active
            ws.title = "设备参数"

            # 设置样式
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            header_alignment = Alignment(horizontal="center", vertical="center")
            border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            # 写入标题信息
            ws['A1'] = f"设备 {device.device_id} - 参数导出"
            ws['A1'].font = Font(bold=True, size=14)
            ws.merge_cells('A1:E1')

            ws['A2'] = f"导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            ws['A3'] = f"参数总数: {len(parameters_data)}"

            # 表头
            headers = ['参数分类', '参数名称', '参数值', '单位', '描述']
            header_row = 5

            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=header_row, column=col, value=header)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment
                cell.border = border

            # 写入数据
            for row_idx, param in enumerate(parameters_data, header_row + 1):
                row_data = [
                    param.get('category', ''),
                    param.get('parameter', ''),
                    param.get('value', ''),
                    param.get('unit', ''),
                    param.get('description', '')
                ]

                for col, value in enumerate(row_data, 1):
                    cell = ws.cell(row=row_idx, column=col, value=value)
                    cell.border = border

            # 调整列宽
            column_widths = [15, 20, 15, 10, 30]
            for col, width in enumerate(column_widths, 1):
                ws.column_dimensions[chr(64 + col)].width = width

            # 生成文件名
            filename = f"{device.device_id}_parameters_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

            # 保存到内存
            output = io.BytesIO()
            wb.save(output)
            output.seek(0)

            return output.getvalue(), filename

        except Exception as e:
            logger.error(f"生成设备参数Excel文件失败: {e}")
            raise

    def _generate_parameters_csv_file(self, parameters_data: List[Dict], device: Device) -> tuple:
        """生成设备参数CSV文件"""
        try:
            # 转换为DataFrame
            df = pd.DataFrame(parameters_data)

            # 重新排列列顺序
            columns_order = ['category', 'parameter', 'value', 'unit', 'description']
            df = df.reindex(columns=columns_order)

            # 重命名列
            df.columns = ['参数分类', '参数名称', '参数值', '单位', '描述']

            # 生成文件名
            filename = f"{device.device_id}_parameters_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"

            # 保存到内存
            output = io.StringIO()
            df.to_csv(output, index=False, encoding='utf-8-sig')

            return output.getvalue().encode('utf-8-sig'), filename

        except Exception as e:
            logger.error(f"生成设备参数CSV文件失败: {e}")
            raise


# 创建全局实例
data_export_service = DataExportService()
