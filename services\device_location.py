from models.device import Device
from models.database import db
from utils.logger import setup_logging

logger = setup_logging()

def update_device_location_db(device_id: str, latitude: float, longitude: float, address: str):
    """更新设备位置信息"""
    # 通过device_id字符串查找设备
    device = Device.query.filter_by(device_id=device_id).first()

    if not device:
        logger.error(f"设备 {device_id} 不存在")
        return False

    # 更新设备位置信息
    device.update_location(latitude, longitude, address)

    db.session.commit()
    logger.info(f"设备 {device_id} 的位置信息已更新到数据库")
    return True

