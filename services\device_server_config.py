#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
设备服务器配置模块
集成IoT客户端配置方法，提供设备服务器信息更新功能
"""

import logging
import time
from typing import Dict, Optional, Tuple
from flask import current_app

# 导入IoT客户端相关模块
try:
    from iot_client.platform.ali_mqtt_client import AmqpConfig
    from iot_client.platform.emqx_mqtt_client import EMQXConfig
    from iot_client.platform.platform_type import PlatformType
    from iot_client.iot_client import IoTClient
    from services.iot_client_manager import IoTClientManager
    from iot_client.bin_block.bin_block import BinBlock
    from iot_client.functions.register_manager import RegisterManager
    from iot_client.bin_block.bin_block import BinBlockMQTTBrokerInfo_t
    from iot_client.bin_block.protocol_constants import MqttBrokerType

    IOT_CLIENT_AVAILABLE = True
except ImportError as e:
    IOT_CLIENT_AVAILABLE = False
    logging.warning(f"IoT客户端模块不可用: {e}")

from .server_config_service import server_config_service
from .device_database_service import device_database_service


class DeviceServerConfigManager:
    """设备服务器配置管理器"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._iot_client: IoTClient = None
        self._client_initialized = False

    def _initialize_iot_client(self) -> bool:
        """初始化IoT客户端"""
        if not IOT_CLIENT_AVAILABLE:
            self.logger.error("IoT客户端模块不可用，无法进行设备配置")
            return False

        if self._client_initialized:
            return True

        try:
            # 创建IoT客户端
            # 使用全局IoT客户端
            self._iot_client = IoTClientManager.get_instance()

            self._client_initialized = True
            self.logger.info("IoT客户端初始化成功")
            return True

        except Exception as e:
            self.logger.error(f"IoT客户端初始化失败: {e}")
            return False

    def _get_broker_type(self, server_type: str):
        """获取broker类型枚举"""
        if server_type == "alicloud":
            return MqttBrokerType.ALIBABA_CLOUD
        elif server_type == "emqx":
            return MqttBrokerType.EMQX
        else:
            raise ValueError(f"不支持的服务器类型: {server_type}")

    def config_alicloud_to_emqx(
        self, device_id: int, source_product_key: str, target_product_key: str, new_device_id: Optional[int] = None
    ) -> Tuple[bool, str]:
        """阿里云迁移到EMQX"""
        if not self._initialize_iot_client():
            return False, "IoT客户端初始化失败"

        try:
            if new_device_id is None:
                new_device_id = device_id

            # 构建topic
            topic_full_name = f"/{source_product_key}/{device_id}/user/ota"

            # 获取EMQX服务器配置
            emqx_config = server_config_service.get_server_config("emqx")
            default_config = emqx_config.get("default_config", {})

            # 创建broker信息
            broker_info = BinBlockMQTTBrokerInfo_t(
                new_devid=new_device_id,
                broker_type=MqttBrokerType.EMQX,
                port=default_config.get("port", 1883),
                keep_alive=0,  # 不更新
                broker_host=default_config.get("broker_host", "mqtt01.yunpusher.com"),
                username=default_config.get("username", "kafang@"),
                password=default_config.get("password", "kafang@_2025"),
                product_key=target_product_key,
                device_secret="",
            )

            # 创建寄存器管理器并执行配置
            reg_manager = RegisterManager(self._iot_client, topic_full_name, self.logger)
            result = reg_manager.cmd_mqtt_broker_info_update(device_id, broker_info, restart=True, timeout=10)

            if result.get("parsed_data", {}).get("result", None) == 0:
                # 设备配置成功，更新数据库
                db_success, db_message = device_database_service.update_device_server_info(
                    device_id, target_product_key, new_device_id
                )

                if db_success:
                    return True, f"配置更新成功，{db_message}"
                else:
                    self.logger.warning(f"设备配置成功但数据库更新失败: {db_message}")
                    return True, f"设备配置成功，但数据库更新失败: {db_message}"
            else:
                return False, f"配置更新失败: {result}"

        except Exception as e:
            self.logger.error(f"阿里云到EMQX配置失败: {e}")
            return False, f"配置失败: {str(e)}"

    def config_emqx_to_alicloud(
        self,
        device_id: int,
        source_product_key: str,
        target_product_key: str,
        device_secret: str,
        new_device_id: Optional[int] = None,
    ) -> Tuple[bool, str]:
        """EMQX迁移到阿里云"""
        if not self._initialize_iot_client():
            return False, "IoT客户端初始化失败"

        try:
            if new_device_id is None:
                new_device_id = device_id

            # 构建topic
            topic_full_name = f"/{source_product_key}/{device_id}/user/ota"

            # 创建broker信息
            broker_info = BinBlockMQTTBrokerInfo_t(
                new_devid=9999,  # 阿里云使用固定值
                broker_type=MqttBrokerType.ALIBABA_CLOUD,
                port=1883,
                keep_alive=0,  # 不更新，使用原来的
                broker_host="",  # 不更新,阿里云有product_key即可
                username="",  # 不更新，阿里云不需要
                password="",  # 不更新，阿里云不需要
                product_key=target_product_key,
                device_secret=device_secret,
            )

            # 创建寄存器管理器并执行配置
            reg_manager = RegisterManager(self._iot_client, topic_full_name, self.logger)
            result = reg_manager.cmd_mqtt_broker_info_update(device_id, broker_info, restart=True, timeout=10)

            if result.get("parsed_data", {}).get("result", None) == 0:
                # 设备配置成功，更新数据库
                db_success, db_message = device_database_service.update_device_server_info(
                    device_id, target_product_key, new_device_id
                )

                if db_success:
                    return True, f"配置更新成功，{db_message}"
                else:
                    self.logger.warning(f"设备配置成功但数据库更新失败: {db_message}")
                    return True, f"设备配置成功，但数据库更新失败: {db_message}"
            else:
                return False, f"配置更新失败: {result}"

        except Exception as e:
            self.logger.error(f"EMQX到阿里云配置失败: {e}")
            return False, f"配置失败: {str(e)}"

    def config_emqx_product_key(
        self,
        device_id: int,
        source_product_key: str,
        target_product_key: str,
        new_device_id: Optional[int] = None,
    ) -> Tuple[bool, str]:
        """EMQX内部product_key变更"""
        if not self._initialize_iot_client():
            return False, "IoT客户端初始化失败"

        try:
            if new_device_id is None:
                new_device_id = device_id

            # 构建topic
            topic_full_name = f"/{source_product_key}/{device_id}/user/ota"

            # 获取EMQX服务器配置
            emqx_config = server_config_service.get_server_config("emqx")
            default_config = emqx_config.get("default_config", {})

            # 创建broker信息
            broker_info = BinBlockMQTTBrokerInfo_t(
                new_devid=new_device_id,
                broker_type=MqttBrokerType.EMQX,
                port=default_config.get("port", 1883),
                keep_alive=0,  # 不更新
                broker_host=default_config.get("broker_host", "mqtt01.yunpusher.com"),
                username=default_config.get("username", "kafang@"),
                password=default_config.get("password", "kafang@_2025"),
                product_key=target_product_key,
                device_secret="",
            )

            # 创建寄存器管理器并执行配置
            reg_manager = RegisterManager(self._iot_client, topic_full_name, self.logger)
            result = reg_manager.cmd_mqtt_broker_info_update(device_id, broker_info, restart=True, timeout=10)

            if result.get("parsed_data", {}).get("result", None) == 0:
                # 设备配置成功，更新数据库
                db_success, db_message = device_database_service.update_device_server_info(
                    device_id, target_product_key
                )

                if db_success:
                    return True, f"配置更新成功，{db_message}"
                else:
                    self.logger.warning(f"设备配置成功但数据库更新失败: {db_message}")
                    return True, f"设备配置成功，但数据库更新失败: {db_message}"
            else:
                return False, f"配置更新失败: {result}"

        except Exception as e:
            self.logger.error(f"EMQX产品密钥配置失败: {e}")
            return False, f"配置失败: {str(e)}"

    def update_device_server_config(
        self,
        device_id: int,
        source_product_key: str,
        target_product_key: str,
        new_device_id: Optional[int] = None,
        new_device_secret: Optional[str] = None,
    ) -> Tuple[bool, str]:
        """统一的设备服务器配置更新接口"""
        source_server_type = server_config_service.detect_server_type(source_product_key)
        target_server_type = server_config_service.detect_server_type(target_product_key)

        # 确保类型正确
        if isinstance(new_device_id, str):
            new_device_id = int(new_device_id.strip()) if new_device_id.strip() else None
        if isinstance(device_id, str):
            device_id = int(device_id.strip()) if device_id.strip() else None

        # 验证配置
        is_valid, message = server_config_service.validate_migration(source_product_key, target_product_key)
        if not is_valid:
            return False, message

        try:
            if source_server_type == "alicloud" and target_server_type == "emqx":
                return self.config_alicloud_to_emqx(device_id, source_product_key, target_product_key, new_device_id)
            elif source_server_type == "emqx" and target_server_type == "alicloud":
                # 获取阿里云产品的device_secret
                if not new_device_secret:
                    return False, "阿里云产品缺少device_secret配置"
                return self.config_emqx_to_alicloud(
                    device_id, source_product_key, target_product_key, new_device_secret, new_device_id
                )
            elif source_server_type == "emqx" and target_server_type == "emqx":
                return self.config_emqx_product_key(device_id, source_product_key, target_product_key, new_device_id)
            else:
                return False, f"不支持的配置类型: {source_server_type} -> {target_server_type}"

        except Exception as e:
            self.logger.error(f"设备服务器配置更新失败: {e}")
            return False, f"配置更新失败: {str(e)}"

    def batch_update_device_server_config(
        self, device_ids: list, source_product_key: str, target_product_key: str
    ) -> Tuple[bool, str, list]:
        """
        批量更新设备服务器配置

        Args:
            device_ids: 设备数据库ID列表
            source_product_key: 源产品密钥
            target_product_key: 目标产品密钥

        Returns:
            Tuple[bool, str, list]: (是否有成功, 总体消息, 详细结果列表)
        """
        try:
            # 获取符合条件的设备
            from models.device import Device

            devices = Device.query.filter(Device.id.in_(device_ids), Device.product_key == source_product_key).all()

            if not devices:
                return False, f"没有找到产品密钥为 {source_product_key} 的设备", []

            results = []
            success_count = 0

            for device in devices:
                try:
                    # 执行设备配置
                    ori_devid: int = int(device.device_id)
                    config_success, config_message = self.update_device_server_config(
                        ori_devid, source_product_key, target_product_key
                    )

                    if config_success:
                        success_count += 1

                    results.append(
                        {
                            "device_id": device.device_id,
                            "device_name": device.device_remark or device.device_id,
                            "success": config_success,
                            "message": config_message,
                        }
                    )

                except Exception as e:
                    results.append(
                        {
                            "device_id": device.device_id,
                            "device_name": device.device_remark or device.device_id,
                            "success": False,
                            "message": f"配置失败: {str(e)}",
                        }
                    )
                    self.logger.error(f"批量配置设备 {device.device_id} 失败: {e}")

            # 返回结果
            if success_count > 0:
                message = f"批量配置完成，成功 {success_count}/{len(devices)} 个设备"
                return True, message, results
            else:
                return False, "没有设备配置成功", results

        except Exception as e:
            error_msg = f"批量配置失败: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, []

    def cleanup(self):
        """清理资源"""
        if self._iot_client and self._client_initialized:
            try:
                # 这里可以添加客户端清理逻辑
                pass
            except Exception as e:
                self.logger.error(f"清理IoT客户端失败: {e}")


# 创建全局配置管理器实例
device_server_config_manager = DeviceServerConfigManager()
