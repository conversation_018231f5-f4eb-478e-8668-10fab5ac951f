import threading
from iot_client.iot_client import IoTClient
from iot_client.platform.ali_mqtt_client import AmqpConfig
from iot_client.platform.emqx_mqtt_client import EMQXConfig
from utils.logger import LoggerManager
import logging

# 获取日志记录器
logger = LoggerManager.get_logger()

# 添加IoT客户端管理类
class IoTClientManager:
    """IoT客户端管理类 - 单例模式"""

    _instance = None
    _running = False
    _initialized = False
    _lock = threading.Lock()

    @classmethod
    def initialize(cls):
        """初始化IoT客户端管理器"""
        if not cls._initialized:
            with cls._lock:
                if not cls._initialized:
                    # 创建配置
                    config = AmqpConfig()
                    exqx_config = EMQXConfig()
                    topic_filters = ["^/[^/]+/[^/]+/user/ota_ack$"]
                    cls._instance = IoTClient(topic_filters, logger, config, exqx_config)

                    # 设置请求处理器
                    cls._setup_request_handler()

                    # 关闭 stomp.py 的 DEBUG 日志
                    logging.getLogger("stomp.py").setLevel(logging.INFO)
                    cls._initialized = True
                    logger.info("IoT客户端管理器已初始化")

    @classmethod
    def _setup_request_handler(cls):
        """设置请求处理器"""
        try:
            from services.request_handler_manager import request_handler_manager
            cls._instance.set_on_request_cb(request_handler_manager.handle_request)
            logger.info("请求处理器已设置")
        except Exception as e:
            logger.error(f"设置请求处理器失败: {e}")

    @classmethod
    def start(cls):
        """启动IoT客户端"""
        # 确保已初始化
        if not cls._initialized:
            cls.initialize()

        if not cls._running:
            cls._instance.start()
            cls._running = True
            logger.info("IoT客户端已启动")

            # 启动MQTT消息转发服务
            try:
                from services.mqtt_message_forwarder import mqtt_forwarder
                mqtt_forwarder.set_iot_client(cls._instance)
                if mqtt_forwarder.start():
                    logger.info("MQTT消息转发服务已启动")
                else:
                    logger.warning("MQTT消息转发服务启动失败或已禁用")
            except Exception as e:
                logger.error(f"启动MQTT消息转发服务时发生错误: {e}")

            return True
        return False

    @classmethod
    def stop(cls):
        """停止IoT客户端"""
        if cls._running and cls._instance:
            # 停止MQTT消息转发服务
            try:
                from services.mqtt_message_forwarder import mqtt_forwarder
                if mqtt_forwarder.stop():
                    logger.info("MQTT消息转发服务已停止")
            except Exception as e:
                logger.error(f"停止MQTT消息转发服务时发生错误: {e}")

            cls._instance.stop()
            cls._running = False
            logger.info("IoT客户端已停止")
            return True
        return False

    @classmethod
    def is_running(cls):
        """检查IoT客户端是否运行中"""
        return cls._running

    @classmethod
    def get_instance(cls):
        """获取IoT客户端实例"""
        if not cls._running:
            raise RuntimeError("IoT客户端未启动")
        return cls._instance

    @classmethod
    def get_instance_directly(cls):
        """直接获取IoT客户端实例（不检查运行状态）"""
        if not cls._initialized:
            cls.initialize()
        return cls._instance

    @classmethod
    def get_status(cls):
        """获取IoT客户端状态"""
        if not cls._initialized:
            return "未初始化"
        elif cls._running:
            return "运行中"
        else:
            return "已停止"
