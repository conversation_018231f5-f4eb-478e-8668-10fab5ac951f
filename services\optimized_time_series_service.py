#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
优化的时序数据库服务
针对大量设备并发写入和查询进行性能优化
"""

import os
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from collections import defaultdict, deque
from sqlalchemy import and_, or_, func, text, select
from sqlalchemy.exc import SQLAlchemyError
from flask import current_app

from models.database import db
from models.optimized_time_series import OptimizedTimeSeriesData, TimeSeriesBuffer, DATA_TYPE_MAPPING
from utils.logger import LoggerManager

# 获取日志记录器
logger = LoggerManager.get_logger()


class OptimizedTimeSeriesService:
    """优化的时序数据库服务类"""
    
    def __init__(self):
        # 批量写入配置
        self.batch_size = 1000
        self.batch_timeout = 30  # 秒
        self.use_buffer = True   # 是否使用缓冲表
        
        # 内存缓存配置（优化内存使用）
        self.cache_max_size = 100  # 最大缓存条目数
        self.cache_ttl = 300       # 缓存TTL（秒）
        self._cache = {}
        self._cache_access_times = {}
        self._cache_lock = threading.RLock()
        
        # 批量写入缓冲区
        self._write_buffer = deque()
        self._buffer_lock = threading.Lock()
        self._last_flush_time = time.time()
        
        # 启动后台刷新线程
        self._start_background_flush()
    
    def write_sensor_data(self, device_id: str, **sensor_data) -> bool:
        """
        写入传感器数据到时序数据库（优化版本）
        
        Args:
            device_id: 设备ID
            **sensor_data: 传感器数据
            
        Returns:
            bool: 写入是否成功
        """
        try:
            # 创建优化的时序数据记录
            ts_data = OptimizedTimeSeriesData.create_from_sensor_data(
                device_id=device_id,
                **sensor_data
            )
            
            if self.use_buffer:
                # 使用缓冲区批量写入
                return self._write_to_buffer(ts_data)
            else:
                # 直接写入主表
                return self._write_directly(ts_data)
                
        except Exception as e:
            logger.error(f"写入传感器数据异常: {e}")
            return False
    
    def _write_to_buffer(self, ts_data: OptimizedTimeSeriesData) -> bool:
        """写入到缓冲区"""
        try:
            with self._buffer_lock:
                self._write_buffer.append(ts_data)
                
                # 如果缓冲区满了，立即刷新
                if len(self._write_buffer) >= self.batch_size:
                    self._flush_buffer()
            
            return True
            
        except Exception as e:
            logger.error(f"写入缓冲区异常: {e}")
            return False
    
    def _write_directly(self, ts_data: OptimizedTimeSeriesData) -> bool:
        """直接写入主表"""
        try:
            db.session.add(ts_data)
            db.session.commit()
            return True
            
        except SQLAlchemyError as e:
            db.session.rollback()
            logger.error(f"直接写入时序数据异常: {e}")
            return False
    
    def _flush_buffer(self):
        """刷新缓冲区到数据库"""
        if not self._write_buffer:
            return
        
        try:
            # 获取要刷新的数据
            buffer_data = list(self._write_buffer)
            self._write_buffer.clear()
            self._last_flush_time = time.time()
            
            # 批量插入
            if self.use_buffer:
                # 先写入缓冲表
                buffer_records = [TimeSeriesBuffer.from_optimized_data(data) for data in buffer_data]
                db.session.add_all(buffer_records)
                db.session.commit()
                
                # 异步转移到主表
                self._schedule_buffer_flush()
            else:
                # 直接写入主表
                db.session.add_all(buffer_data)
                db.session.commit()
            
            logger.info(f"成功刷新 {len(buffer_data)} 条时序数据到数据库")
            
        except SQLAlchemyError as e:
            db.session.rollback()
            logger.error(f"刷新缓冲区异常: {e}")
            
            # 将数据重新放回缓冲区
            with self._buffer_lock:
                self._write_buffer.extendleft(reversed(buffer_data))
    
    def _schedule_buffer_flush(self):
        """调度缓冲区刷新到主表"""
        try:
            # 执行存储过程将缓冲区数据转移到主表
            table_prefix = 'dev_' if os.environ.get('FLASK_ENV') == 'development' else 'prod_'
            
            result = db.session.execute(
                text("SELECT flush_buffer_to_main(:table_prefix)"),
                {'table_prefix': table_prefix}
            )
            
            moved_count = result.scalar()
            if moved_count > 0:
                logger.info(f"从缓冲区转移了 {moved_count} 条记录到主表")
            
            db.session.commit()
            
        except Exception as e:
            logger.error(f"缓冲区刷新到主表异常: {e}")
            db.session.rollback()
    
    def _start_background_flush(self):
        """启动后台刷新线程"""
        def background_flush():
            while True:
                try:
                    time.sleep(5)  # 每5秒检查一次
                    
                    current_time = time.time()
                    with self._buffer_lock:
                        # 如果超时或缓冲区有数据，则刷新
                        if (self._write_buffer and 
                            current_time - self._last_flush_time > self.batch_timeout):
                            self._flush_buffer()
                    
                    # 定期清理缓存
                    if current_time % 60 < 5:  # 大约每分钟清理一次
                        self._cleanup_cache()
                        
                except Exception as e:
                    logger.error(f"后台刷新线程异常: {e}")
        
        flush_thread = threading.Thread(target=background_flush, daemon=True)
        flush_thread.start()
    
    def query_power_data(self, device_id: str, start_time: datetime,
                        end_time: Optional[datetime] = None) -> Dict[str, List[Dict[str, Any]]]:
        """
        查询设备功率数据（优化版本）
        """
        try:
            if end_time is None:
                end_time = datetime.now()
            
            # 检查缓存
            cache_key = f"power_{device_id}_{start_time.date()}_{end_time.date()}"
            cached_data = self._get_from_cache(cache_key)
            if cached_data is not None:
                return cached_data
            
            # 优化查询：使用数组字段直接查询
            power_records = db.session.query(
                OptimizedTimeSeriesData.timestamp,
                OptimizedTimeSeriesData.power_values
            ).filter(
                and_(
                    OptimizedTimeSeriesData.device_id == device_id,
                    OptimizedTimeSeriesData.data_type == DATA_TYPE_MAPPING['power'],
                    OptimizedTimeSeriesData.timestamp >= start_time,
                    OptimizedTimeSeriesData.timestamp <= end_time,
                    OptimizedTimeSeriesData.power_values.isnot(None)
                )
            ).order_by(OptimizedTimeSeriesData.timestamp).all()
            
            # 组织数据格式
            result_data = {f"channel_{i+1}": [] for i in range(10)}
            
            for record in power_records:
                timestamp_iso = record.timestamp.isoformat()
                power_values = record.power_values or []
                
                for i, power_value in enumerate(power_values[:10]):
                    channel_key = f"channel_{i+1}"
                    result_data[channel_key].append({
                        "time": timestamp_iso,
                        "value": power_value
                    })
            
            # 缓存结果
            self._set_cache(cache_key, result_data)
            
            return result_data
            
        except Exception as e:
            logger.error(f"查询功率数据异常: {e}")
            return {}
    
    def query_single_value_data(self, device_id: str, data_type: str,
                               start_time: datetime, end_time: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """
        查询单值数据（优化版本）
        """
        try:
            if end_time is None:
                end_time = datetime.now()
            
            # 检查缓存
            cache_key = f"{data_type}_{device_id}_{start_time.date()}_{end_time.date()}"
            cached_data = self._get_from_cache(cache_key)
            if cached_data is not None:
                return cached_data
            
            # 获取数据类型编码
            data_type_code = DATA_TYPE_MAPPING.get(data_type)
            if data_type_code is None:
                logger.warning(f"未知的数据类型: {data_type}")
                return []
            
            # 根据数据类型选择查询字段
            field_mapping = {
                'voltage': OptimizedTimeSeriesData.voltage,
                'temperature': OptimizedTimeSeriesData.temperature,
                'total_power': OptimizedTimeSeriesData.total_power,
            }
            
            value_field = field_mapping.get(data_type)
            if value_field is None:
                logger.warning(f"不支持的单值数据类型: {data_type}")
                return []
            
            # 优化查询：只选择需要的字段
            records = db.session.query(
                OptimizedTimeSeriesData.timestamp,
                value_field
            ).filter(
                and_(
                    OptimizedTimeSeriesData.device_id == device_id,
                    OptimizedTimeSeriesData.data_type == data_type_code,
                    OptimizedTimeSeriesData.timestamp >= start_time,
                    OptimizedTimeSeriesData.timestamp <= end_time,
                    value_field.isnot(None)
                )
            ).order_by(OptimizedTimeSeriesData.timestamp).all()
            
            # 组织数据格式
            result_data = []
            for record in records:
                result_data.append({
                    "time": record.timestamp.isoformat(),
                    "value": getattr(record, value_field.key)
                })
            
            # 缓存结果
            self._set_cache(cache_key, result_data)
            
            return result_data
            
        except Exception as e:
            logger.error(f"查询{data_type}数据异常: {e}")
            return []
    
    def query_csq_data(self, device_id: str, start_time: datetime,
                      end_time: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """
        查询设备信号质量数据（优化版本）
        """
        try:
            if end_time is None:
                end_time = datetime.now()
            
            # 检查缓存
            cache_key = f"csq_{device_id}_{start_time.date()}_{end_time.date()}"
            cached_data = self._get_from_cache(cache_key)
            if cached_data is not None:
                return cached_data
            
            # 查询CSQ和BER数据
            records = db.session.query(
                OptimizedTimeSeriesData.timestamp,
                OptimizedTimeSeriesData.csq,
                OptimizedTimeSeriesData.ber
            ).filter(
                and_(
                    OptimizedTimeSeriesData.device_id == device_id,
                    OptimizedTimeSeriesData.data_type == DATA_TYPE_MAPPING['signal'],
                    OptimizedTimeSeriesData.timestamp >= start_time,
                    OptimizedTimeSeriesData.timestamp <= end_time,
                    OptimizedTimeSeriesData.csq.isnot(None)
                )
            ).order_by(OptimizedTimeSeriesData.timestamp).all()
            
            # 组织数据格式
            result_data = []
            for record in records:
                result_data.append({
                    "time": record.timestamp.isoformat(),
                    "value": record.csq,
                    "ber": record.ber or 99  # BER默认值99表示未知
                })
            
            # 缓存结果
            self._set_cache(cache_key, result_data)
            
            return result_data
            
        except Exception as e:
            logger.error(f"查询信号质量数据异常: {e}")
            return []

    def query_data_summary(self, device_id: str, data_type: str,
                          start_time: datetime, end_time: Optional[datetime] = None,
                          interval_minutes: int = 60) -> List[Dict[str, Any]]:
        """查询数据摘要（优化版本，使用分区和聚合）"""
        try:
            if end_time is None:
                end_time = datetime.now()

            # 获取数据类型编码
            data_type_code = DATA_TYPE_MAPPING.get(data_type)
            if data_type_code is None:
                return []

            # 根据数据类型选择聚合字段
            field_mapping = {
                'voltage': OptimizedTimeSeriesData.voltage,
                'temperature': OptimizedTimeSeriesData.temperature,
                'total_power': OptimizedTimeSeriesData.total_power,
            }

            value_field = field_mapping.get(data_type)
            if value_field is None:
                return []

            # 使用PostgreSQL的时间聚合函数
            interval_expr = text(f"date_trunc('hour', timestamp) + interval '{interval_minutes} minutes' * floor(extract(minute from timestamp) / {interval_minutes})")

            # 优化的聚合查询
            aggregated_data = db.session.query(
                interval_expr.label('time_bucket'),
                func.avg(value_field).label('avg_value'),
                func.min(value_field).label('min_value'),
                func.max(value_field).label('max_value'),
                func.count(OptimizedTimeSeriesData.id).label('count')
            ).filter(
                and_(
                    OptimizedTimeSeriesData.device_id == device_id,
                    OptimizedTimeSeriesData.data_type == data_type_code,
                    OptimizedTimeSeriesData.timestamp >= start_time,
                    OptimizedTimeSeriesData.timestamp <= end_time,
                    value_field.isnot(None)
                )
            ).group_by(interval_expr).order_by(interval_expr).all()

            # 组织数据格式
            result_data = []
            for record in aggregated_data:
                result_data.append({
                    "time": record.time_bucket.isoformat(),
                    "avg_value": float(record.avg_value) if record.avg_value else None,
                    "min_value": float(record.min_value) if record.min_value else None,
                    "max_value": float(record.max_value) if record.max_value else None,
                    "count": record.count
                })

            return result_data

        except Exception as e:
            logger.error(f"查询{data_type}数据摘要异常: {e}")
            return []

    def _get_from_cache(self, key: str) -> Any:
        """从缓存获取数据（优化版本）"""
        with self._cache_lock:
            if key in self._cache:
                cache_entry = self._cache[key]
                if datetime.now() - cache_entry['timestamp'] < timedelta(seconds=self.cache_ttl):
                    # 更新访问时间
                    self._cache_access_times[key] = time.time()
                    return cache_entry['data']
                else:
                    # 缓存过期，删除
                    del self._cache[key]
                    if key in self._cache_access_times:
                        del self._cache_access_times[key]
        return None

    def _set_cache(self, key: str, data: Any) -> None:
        """设置缓存数据（优化版本）"""
        with self._cache_lock:
            # 如果缓存已满，删除最久未访问的条目
            if len(self._cache) >= self.cache_max_size:
                self._evict_oldest_cache_entry()

            self._cache[key] = {
                'data': data,
                'timestamp': datetime.now()
            }
            self._cache_access_times[key] = time.time()

    def _evict_oldest_cache_entry(self):
        """删除最久未访问的缓存条目"""
        if not self._cache_access_times:
            return

        oldest_key = min(self._cache_access_times.keys(),
                        key=lambda k: self._cache_access_times[k])

        if oldest_key in self._cache:
            del self._cache[oldest_key]
        del self._cache_access_times[oldest_key]


# 创建全局实例
optimized_time_series_service = OptimizedTimeSeriesService()
