import os
import time
import threading
from threading import Thread, Event
from datetime import datetime
from werkzeug.utils import secure_filename
from typing import Optional, Callable, Any
from queue import Queue, Empty
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, Future

from models.ota_task import OtaTask
from models.device import Device
from models.firmware import Firmware
from models.database import db
from utils.logger import LoggerManager
from utils.socket_manager import emit_task_update
from utils.ota_common import VersionUtils, FirmwareValidator, DeviceValidator
from services.iot_client_manager import IoTClientManager
from iot_client.functions.ota_client import OtaClient

# 获取日志记录器
logger = LoggerManager.get_logger()


@dataclass
class OtaTaskInfo:
    """OTA任务信息类"""

    task_id: int
    task_func: Callable
    args: tuple
    created_at: datetime


class OtaTaskThreadManager:
    """OTA任务线程管理器

    使用线程池管理OTA任务：
    - 维护一个任务队列
    - 使用线程池并行处理任务
    - 支持配置最大并发数
    - 内存中管理设备任务状态，防止重复任务
    """

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
                cls._instance._initialize()
            return cls._instance

    def _initialize(self):
        """初始化管理器"""
        self.max_workers = 5  # 默认最大并发数
        self.task_queue: Queue[OtaTaskInfo] = Queue()
        self.running_tasks: dict[int, Future] = {}  # 任务ID -> Future映射
        self.task_events: dict[int, Event] = {}
        self.device_tasks: dict[int, int] = {}  # 设备ID -> 任务ID映射
        self.executor: Optional[ThreadPoolExecutor] = None
        self.monitor_thread: Optional[Thread] = None
        self.stop_event = Event()
        self.is_running = False
        self._start_manager()

    def _start_manager(self):
        """启动管理器"""
        if self.is_running:
            return

        self.executor = ThreadPoolExecutor(max_workers=self.max_workers, thread_name_prefix="OTA-Worker")

        self.monitor_thread = Thread(target=self._monitor_tasks, daemon=True)
        self.monitor_thread.start()
        self.is_running = True
        logger.info(f"OTA任务管理器已启动，最大并发数: {self.max_workers}")

    def configure(self, max_workers: int = 5):
        """配置管理器参数"""
        old_max_workers = self.max_workers
        self.max_workers = max_workers

        if self.is_running and old_max_workers != max_workers:
            logger.info(f"管理器运行中，配置将在下次重启时生效: max_workers={max_workers}")
        else:
            logger.info(f"OTA任务管理器配置更新: max_workers={max_workers}")

    def _monitor_tasks(self):
        """监控任务执行"""
        while not self.stop_event.is_set():
            try:
                # 从队列中获取任务
                task_info = None
                try:
                    task_info = self.task_queue.get(timeout=1)
                except Empty:
                    continue

                if task_info:
                    # 提交任务到线程池
                    future = self.executor.submit(self._execute_task_wrapper, task_info)
                    self.running_tasks[task_info.task_id] = future

                # 清理已完成的任务
                self._cleanup_completed_tasks()

            except Exception as e:
                logger.error(f"任务监控异常: {e}")

    def _execute_task_wrapper(self, task_info: OtaTaskInfo):
        """任务执行包装器"""
        try:
            logger.info(f"开始执行任务: {task_info.task_id}")
            task_info.task_func(task_info.task_id, *task_info.args)
            logger.info(f"任务执行完成: {task_info.task_id}")
        except Exception as e:
            logger.error(f"任务执行失败: {task_info.task_id}, 错误: {e}")
        finally:
            # 清理设备任务映射
            self._remove_device_task(task_info.task_id)

    def _cleanup_completed_tasks(self):
        """清理已完成的任务"""
        completed_tasks = []
        for task_id, future in self.running_tasks.items():
            if future.done():
                completed_tasks.append(task_id)

        for task_id in completed_tasks:
            self.running_tasks.pop(task_id, None)
            self.task_events.pop(task_id, None)

    def add_task(self, task_func: Callable, task_id: int, device_id: int, *args: Any) -> bool:
        """添加新的OTA任务

        Args:
            task_func: 要执行的任务函数
            task_id: 任务ID
            device_id: 设备ID
            *args: 传递给任务函数的参数

        Returns:
            是否成功添加任务
        """
        if not self.is_running:
            logger.error("任务管理器未运行，无法添加任务")
            return False

        # 检查设备是否已有任务在执行
        if device_id in self.device_tasks:
            existing_task_id = self.device_tasks[device_id]
            logger.warning(f"设备 {device_id} 已有任务 {existing_task_id} 在执行，跳过新任务 {task_id}")
            return False

        # 记录设备任务映射
        self.device_tasks[device_id] = task_id

        task_info = OtaTaskInfo(task_id=task_id, task_func=task_func, args=args, created_at=datetime.now())

        # 将任务加入队列
        self.task_queue.put(task_info)
        logger.info(f"添加任务到队列: {task_id}, 设备: {device_id}")
        return True

    def _remove_device_task(self, task_id: int):
        """移除设备任务映射"""
        device_id = None
        for dev_id, tid in self.device_tasks.items():
            if tid == task_id:
                device_id = dev_id
                break

        if device_id is not None:
            self.device_tasks.pop(device_id, None)
            logger.info(f"移除设备 {device_id} 的任务映射: {task_id}")

    def is_device_busy(self, device_id: int) -> bool:
        """检查设备是否有任务在执行"""
        return device_id in self.device_tasks

    def get_stats(self) -> dict:
        """获取统计信息"""
        return {
            "max_workers": self.max_workers,
            "active_tasks": len(self.running_tasks),
            "queued_tasks": self.task_queue.qsize(),
            "device_tasks": len(self.device_tasks),
            "is_running": self.is_running,
        }

    def pause_task(self, task_id: int) -> bool:
        """暂停任务（预留接口）"""
        logger.info(f"收到暂停任务请求: {task_id}")
        logger.info("暂停功能正在开发中，当前仅记录请求")
        return True

    def cancel_task(self, task_id: int) -> bool:
        """取消任务"""
        if task_id in self.running_tasks:
            future = self.running_tasks[task_id]
            cancelled = future.cancel()
            if cancelled:
                self.running_tasks.pop(task_id, None)
                self._remove_device_task(task_id)
                logger.info(f"任务 {task_id} 已取消")
            return cancelled
        return False

    def shutdown(self):
        """关闭任务管理器"""
        if not self.is_running:
            return

        self.stop_event.set()
        self.is_running = False

        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)

        if self.executor:
            self.executor.shutdown(wait=True)

        logger.info("OTA任务管理器已关闭")


# 创建OTA任务管理器实例
ota_task_manager = OtaTaskThreadManager()


def shutdown_ota_service():
    """关闭OTA服务（兼容parallel_ota_service）"""
    try:
        ota_task_manager.shutdown()
        logger.info("OTA服务已关闭")
    except Exception as e:
        logger.error(f"关闭OTA服务失败: {e}")

def get_ota_service_stats() -> dict:
    """获取OTA服务统计信息（兼容parallel_ota_service）"""
    return ota_task_manager.get_stats()


def pause_ota_task(task_id: int) -> tuple[bool, str]:
    """暂停OTA任务（兼容parallel_ota_service）"""
    try:
        success = ota_task_manager.pause_task(task_id)
        if success:
            return True, f"任务 {task_id} 暂停请求已发送"
        else:
            return False, f"任务 {task_id} 暂停失败"
    except Exception as e:
        logger.error(f"暂停任务失败: {e}")
        return False, str(e)


def cancel_ota_task(task_id: int) -> tuple[bool, str]:
    """取消OTA任务（兼容parallel_ota_service）"""
    try:
        success = ota_task_manager.cancel_task(task_id)
        if success:
            return True, f"任务 {task_id} 已取消"
        else:
            return False, f"任务 {task_id} 取消失败"
    except Exception as e:
        logger.error(f"取消任务失败: {e}")
        return False, str(e)


def get_ota_task_status(task_id: int) -> Optional[dict]:
    """获取OTA任务详细状态（兼容parallel_ota_service）"""
    # 简单实现，返回基本状态
    try:
        task = OtaTask.query.get(task_id)
        if task:
            return {
                "task_id": task.id,
                "status": task.status,
                "progress": task.progress,
                "error_message": task.error_message,
            }
    except Exception as e:
        logger.error(f"获取任务状态失败: {e}")
    return None


def run_ota_task(task_id, app=None):
    """运行OTA任务"""
    # 使用传入的app创建应用上下文
    if not app:
        logger.error(f"OTA任务 {task_id} 未提供应用实例，无法执行")
        return

    with app.app_context():
        try:
            # 获取任务和设备信息
            task = OtaTask.query.get(task_id)
            if not task:
                logger.error(f"OTA任务 {task_id} 不存在")
                return

            device = Device.query.get(task.device_id)
            if not device:
                logger.error(f"设备 {task.device_id} 不存在")
                return

            # 更新任务状态
            task.status = "进行中"
            task.progress = 0
            db.session.commit()

            # 检查IoT客户端是否已启动
            if not IoTClientManager.is_running():
                task.status = "失败"
                task.error_message = "IoT客户端未启动，请先启动客户端"
                device.last_ota_status = "失败"
                db.session.commit()

                # 发送WebSocket消息
                emit_task_update(task.id, task.status, task.progress, task.error_message)
                return

            # 定义进度回调函数，用于更新OTA任务进度
            last_emit_time = 0  # 上次发送WebSocket消息的时间

            def progress_callback(current, total, description):
                nonlocal last_emit_time
                # 计算百分比进度
                if total > 0:
                    progress = int((current / total) * 100)
                else:
                    progress = 0

                # 更新任务状态
                task.progress = progress
                task.error_message = description
                emit_task_update(task.id, task.status, task.progress, task.error_message)

                # 检查是否需要发送WebSocket消息（限制为每秒最多一次）
                current_time = time.time()
                if current_time - last_emit_time >= 2.5:  # 至少间隔n秒
                    # 发送WebSocket消息通知前端
                    last_emit_time = current_time
                    db.session.commit()
                    # 记录日志
                    logger.info(f"OTA进度更新: {progress}%, {description}")
                else:
                    # 仍然记录日志，但不发送WebSocket消息
                    logger.debug(f"OTA进度更新(未发送): {progress}%, {description}")

                return True  # 返回True表示继续操作

            # 使用全局IoT客户端
            iot_client = IoTClientManager.get_instance()

            # 创建OTA客户端
            ota_client = OtaClient(iot_client, device.product_key, device.device_id, logger, progress_callback)

            # 开始OTA升级
            success = ota_client.start_ota(task.firmware_path, force_update=True)

            # 更新任务状态
            if success:
                task.status = "成功"
                task.progress = 100
                device.last_ota_time = datetime.now()
                device.last_ota_status = "成功"
                device.firmware_version = task.firmware_version
                message = "升级成功"
            else:
                task.status = "失败"
                task.error_message = "OTA升级失败"
                device.last_ota_status = "失败"
                message = "升级失败"

            # 清除OTA开始时间标记
            device.ota_start_time = None

            db.session.commit()

            # 发送WebSocket消息
            emit_task_update(task.id, task.status, task.progress, message)

        except Exception as e:
            logger.error(f"执行OTA任务失败: {e}")
            task.status = "失败"
            task.error_message = str(e)
            device.last_ota_status = "失败"
            db.session.commit()

            # 发送WebSocket消息
            emit_task_update(task.id, task.status, task.progress, task.error_message)

        finally:
            # 确保在多线程环境下正确清理数据库会话
            try:
                db.session.remove()
            except Exception as cleanup_error:
                logger.warning(f"清理数据库会话时出错: {cleanup_error}")


def start_ota_task(device_ids: list[int], firmware_id=None, firmware_file=None):
    """创建并启动OTA任务

    Args:
        device_ids: 设备ID列表
        firmware_id: 固件ID（与firmware_file二选一）
        firmware_file: 固件文件对象（与firmware_id二选一）

    Returns:
        (success, message): 成功状态和消息
    """
    from flask import current_app

    try:
        # 验证当前服务器状态
        iot_client = IoTClientManager.get_instance()
        if not iot_client.is_connected():
            return False, "IoT服务未启动，无法创建OTA任务。"

        # 获取固件信息
        firmware_path = None
        firmware_version = None

        if firmware_id:
            # 通过固件ID获取固件信息
            firmware = Firmware.query.get(firmware_id)
            if not firmware:
                return False, "固件不存在"

            firmware_path = firmware.file_path
            firmware_version = firmware.version

            # 验证固件文件
            is_valid, error_msg = FirmwareValidator.validate_firmware_file(firmware_path)
            if not is_valid:
                return False, f"固件文件验证失败: {error_msg}"

        elif firmware_file:
            # 保存固件文件
            firmware_path = os.path.join(current_app.config["UPLOAD_FOLDER"], secure_filename(firmware_file.filename))
            firmware_file.save(firmware_path)

            # 验证固件文件
            is_valid, error_msg = FirmwareValidator.validate_firmware_file(firmware_path)
            if not is_valid:
                return False, f"固件文件验证失败: {error_msg}"

            # 从文件名提取版本信息
            firmware_version = VersionUtils.extract_version_from_filename(firmware_file.filename)
        else:
            return False, "未提供固件信息"

        # 检查设备冲突和兼容性
        busy_devices = []
        incompatible_devices = []
        valid_devices = []

        for device_id in device_ids:
            # 检查设备是否正在执行任务
            if ota_task_manager.is_device_busy(device_id):
                busy_devices.append(device_id)
                continue

            # 检查设备兼容性
            is_compatible, message = DeviceValidator.check_device_compatibility(device_id, firmware_version)
            if is_compatible:
                valid_devices.append(device_id)
            else:
                incompatible_devices.append(device_id)
                logger.info(f"设备 {device_id} 不兼容: {message}")

        # 构建反馈消息
        message_parts = [f"固件版本: {firmware_version}"]
        if busy_devices:
            message_parts.append(f"正在执行任务的设备: {busy_devices}")
        if incompatible_devices:
            message_parts.append(f"不兼容的设备: {incompatible_devices}")
        if valid_devices:
            message_parts.append(f"将要升级的设备: {valid_devices}")

        message = ", ".join(message_parts)

        # 如果没有有效设备，返回错误
        if not valid_devices:
            if busy_devices and incompatible_devices:
                return False, f"{busy_devices}设备都无法升级: {message}"
            elif busy_devices:
                return False, f"{busy_devices}设备正在执行任务: {message}"
            elif incompatible_devices:
                return False, f"{busy_devices}设备都不兼容: {message}"
            else:
                return False, "没有有效的设备可以升级"

        # 创建OTA任务
        created_tasks = []
        failed_tasks = []

        for device_id in valid_devices:
            task = OtaTask(
                device_id=device_id,
                firmware_path=firmware_path,
                firmware_version=firmware_version,
                status="等待中",
                progress=0,
            )
            db.session.add(task)
            db.session.commit()

            # 尝试添加任务到队列
            if ota_task_manager.add_task(run_ota_task, task.id, device_id, current_app._get_current_object()):
                created_tasks.append(device_id)
            else:
                failed_tasks.append(device_id)
                logger.error(f"设备 {device_id} 创建任务失败")

        # 更新消息
        if created_tasks:
            message += f", 成功创建任务的设备: {created_tasks}"
        if failed_tasks:
            message += f", 创建任务失败的设备: {failed_tasks}"

        logger.info(message)
        return len(created_tasks) > 0, message

    except Exception as e:
        db.session.rollback()
        logger.error(f"创建OTA任务失败: {e}")
        return False, str(e)


def retry_ota_task(task_id):
    """重试OTA任务

    Args:
        task_id: 任务ID

    Returns:
        (success, message): 成功状态和消息
    """
    from flask import current_app

    try:
        # 获取任务信息
        task = OtaTask.query.get_or_404(task_id)

        # 检查设备是否正在执行其他任务
        if ota_task_manager.is_device_busy(task.device_id):
            return False, f"设备 {task.device_id} 正在执行其他任务，无法重试"

        # 重置任务状态
        task.status = "等待中"
        task.progress = 0
        task.error_message = ""
        task.updated_at = datetime.now()
        db.session.commit()

        # 尝试添加任务到队列
        if ota_task_manager.add_task(run_ota_task, task.id, task.device_id, current_app._get_current_object()):
            return True, "任务已重新开始"
        else:
            task.status = "失败"
            task.error_message = "设备正在执行其他任务"
            db.session.commit()
            return False, "设备正在执行其他任务，无法重试"

    except Exception as e:
        db.session.rollback()
        logger.error(f"重试任务失败: {e}")
        return False, str(e)
