#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
OTA任务执行器
负责执行单个OTA任务的完整流程
"""

import os
from datetime import datetime
from typing import Optional, Callable
from utils.logger import LoggerManager
from utils.socket_manager import emit_task_update
from services.iot_client_manager import IoTClientManager
from services.database_session_manager import thread_safe_db_ops
from services.ota_task_state import (
    OtaTaskStateManager, OtaTaskDetailedStatus,
    OtaErrorType, OtaTaskProgress
)
from services.ota_error_handler import ota_error_handler
from iot_client.functions.ota_client import OtaClient

# 获取日志记录器
logger = LoggerManager.get_logger()


class OtaTaskExecutor:
    """OTA任务执行器"""
    
    def __init__(self, task_id: int, state_manager: OtaTaskStateManager):
        self.task_id = task_id
        self.state_manager = state_manager
        self.task_info: Optional[dict] = None
        self.ota_client: Optional[OtaClient] = None
    
    def execute(self) -> bool:
        """执行OTA任务"""
        try:
            logger.info(f"开始执行OTA任务: {self.task_id}")
            
            # 1. 初始化任务
            if not self._initialize_task():
                return False
            
            # 2. 验证前置条件
            if not self._validate_prerequisites():
                return False
            
            # 3. 连接设备
            if not self._connect_device():
                return False
            
            # 4. 加载固件
            if not self._load_firmware():
                return False
            
            # 5. 执行OTA升级
            if not self._perform_ota_upgrade():
                return False
            
            # 6. 完成任务
            self._complete_task_success()
            return True
            
        except Exception as e:
            logger.error(f"OTA任务 {self.task_id} 执行异常: {e}")
            self._handle_task_error(OtaErrorType.SYSTEM_ERROR, "EXEC_ERROR", str(e))
            return False
    
    def _initialize_task(self) -> bool:
        """初始化任务"""
        try:
            self.state_manager.set_status(
                OtaTaskDetailedStatus.INITIALIZING, "正在初始化任务..."
            )
            self._emit_progress_update()
            
            # 获取任务信息
            self.task_info = thread_safe_db_ops.get_task_info(self.task_id)
            if not self.task_info:
                self._handle_task_error(
                    OtaErrorType.VALIDATION_ERROR, 
                    "TASK_NOT_FOUND", 
                    f"任务 {self.task_id} 不存在"
                )
                return False
            
            logger.info(f"任务 {self.task_id} 初始化完成: 设备={self.task_info['device_name']}")
            return True
            
        except Exception as e:
            self._handle_task_error(
                OtaErrorType.SYSTEM_ERROR, "INIT_ERROR", f"初始化失败: {e}"
            )
            return False
    
    def _validate_prerequisites(self) -> bool:
        """验证前置条件"""
        try:
            self.state_manager.set_status(
                OtaTaskDetailedStatus.CONNECTING, "正在验证前置条件..."
            )
            self._emit_progress_update()
            
            # 检查IoT客户端状态
            if not IoTClientManager.is_running():
                self._handle_task_error(
                    OtaErrorType.SYSTEM_ERROR,
                    "IOT_CLIENT_NOT_RUNNING",
                    "IoT客户端未启动，请先启动客户端",
                    is_retryable=False
                )
                return False
            
            # 检查固件文件是否存在
            firmware_path = self.task_info['firmware_path']
            if not os.path.exists(firmware_path):
                self._handle_task_error(
                    OtaErrorType.FIRMWARE_ERROR,
                    "FIRMWARE_NOT_FOUND",
                    f"固件文件不存在: {firmware_path}",
                    is_retryable=False
                )
                return False
            
            logger.info(f"任务 {self.task_id} 前置条件验证通过")
            return True
            
        except Exception as e:
            self._handle_task_error(
                OtaErrorType.SYSTEM_ERROR, "VALIDATION_ERROR", f"验证失败: {e}"
            )
            return False
    
    def _connect_device(self) -> bool:
        """连接设备"""
        try:
            self.state_manager.set_status(
                OtaTaskDetailedStatus.CONNECTING, "正在连接设备..."
            )
            self._emit_progress_update()
            
            # 获取IoT客户端实例
            iot_client = IoTClientManager.get_instance()
            
            # 创建OTA客户端
            self.ota_client = OtaClient(
                iot_client=iot_client,
                target_product_key=self.task_info['product_key'],
                target_device_name=self.task_info['device_name'],
                logger=logger,
                progress_callback=self._progress_callback
            )
            
            logger.info(f"任务 {self.task_id} 设备连接成功")
            return True
            
        except Exception as e:
            self._handle_task_error(
                OtaErrorType.NETWORK_ERROR, "CONNECT_ERROR", f"连接设备失败: {e}"
            )
            return False
    
    def _load_firmware(self) -> bool:
        """加载固件"""
        try:
            self.state_manager.set_status(
                OtaTaskDetailedStatus.LOADING_FIRMWARE, "正在加载固件..."
            )
            self._emit_progress_update()
            
            # 加载固件文件
            firmware_path = self.task_info['firmware_path']
            if not self.ota_client.load_firmware(firmware_path):
                self._handle_task_error(
                    OtaErrorType.FIRMWARE_ERROR,
                    "FIRMWARE_LOAD_ERROR",
                    "固件加载失败",
                    is_retryable=False
                )
                return False
            
            logger.info(f"任务 {self.task_id} 固件加载成功")
            return True
            
        except Exception as e:
            self._handle_task_error(
                OtaErrorType.FIRMWARE_ERROR, "FIRMWARE_ERROR", f"固件加载异常: {e}"
            )
            return False
    
    def _perform_ota_upgrade(self) -> bool:
        """执行OTA升级"""
        try:
            self.state_manager.set_status(
                OtaTaskDetailedStatus.STARTING_OTA, "正在启动OTA升级..."
            )
            self._emit_progress_update()
            
            # 开始OTA升级
            firmware_path = self.task_info['firmware_path']
            success = self.ota_client.start_ota(firmware_path, force_update=True)
            
            if not success:
                self._handle_task_error(
                    OtaErrorType.DEVICE_ERROR,
                    "OTA_UPGRADE_FAILED",
                    "OTA升级失败"
                )
                return False
            
            logger.info(f"任务 {self.task_id} OTA升级成功")
            return True
            
        except Exception as e:
            self._handle_task_error(
                OtaErrorType.DEVICE_ERROR, "OTA_ERROR", f"OTA升级异常: {e}"
            )
            return False
    
    def _complete_task_success(self):
        """完成任务（成功）"""
        self.state_manager.complete_task(True, "OTA升级成功")

        # 更新任务状态到数据库
        thread_safe_db_ops.update_task_status(
            task_id=self.task_id,
            status=self.state_manager.get_legacy_status(),
            progress=self.state_manager.progress.percentage,
            error_message=None,
            detailed_status=self.state_manager.status.value,
            stage_info=self.state_manager.progress.message
        )

        # 更新设备状态
        thread_safe_db_ops.update_device_ota_status(
            device_id=self.task_info['device_id'],
            status="成功",
            firmware_version=self.task_info['firmware_version']
        )

        # 发送WebSocket消息
        self._emit_progress_update("升级成功")

        logger.info(f"任务 {self.task_id} 执行成功")
    
    def _handle_task_error(self, error_type: OtaErrorType = None, error_code: str = None,
                          error_message: str = "", is_retryable: bool = None):
        """处理任务错误（增强版）"""
        # 如果没有提供错误类型和代码，则自动分类
        if error_type is None or error_code is None:
            error_type, error_code = ota_error_handler.classify_error(
                error_message, self.state_manager.status
            )

        # 如果没有指定是否可重试，则根据错误类型判断
        if is_retryable is None:
            is_retryable = ota_error_handler.should_retry(
                error_type, self.state_manager.retry_count
            )

        # 获取用户友好的错误消息
        user_friendly_message = ota_error_handler.get_user_friendly_message(
            error_type, error_code
        )

        # 设置错误信息
        self.state_manager.set_error(error_type, error_code, user_friendly_message, is_retryable)

        # 更新任务状态到数据库
        thread_safe_db_ops.update_task_status(
            task_id=self.task_id,
            status=self.state_manager.get_legacy_status(),
            progress=self.state_manager.progress.percentage,
            error_message=user_friendly_message,
            detailed_status=self.state_manager.status.value,
            stage_info=self.state_manager.progress.message
        )

        # 更新设备状态
        if self.task_info:
            thread_safe_db_ops.update_device_ota_status(
                device_id=self.task_info['device_id'],
                status="失败"
            )

        # 发送WebSocket消息
        self._emit_progress_update(user_friendly_message)

        # 记录详细的错误日志
        logger.error(f"任务 {self.task_id} 失败: [{error_type.value}] {error_code} - {error_message}")
        logger.info(f"用户友好消息: {user_friendly_message}")

        if is_retryable:
            retry_delay = ota_error_handler.calculate_retry_delay(
                self.task_id, error_type, self.state_manager.retry_count
            )
            logger.info(f"任务 {self.task_id} 可重试，建议延迟 {retry_delay:.1f} 秒")
    
    def _progress_callback(self, current: int, total: int, message: str):
        """进度回调函数"""
        self.state_manager.progress.update(current, total, message)

        # 根据进度更新详细状态
        if "连接" in message:
            self.state_manager.set_status(OtaTaskDetailedStatus.CONNECTING, message)
        elif "加载固件" in message:
            self.state_manager.set_status(OtaTaskDetailedStatus.LOADING_FIRMWARE, message)
        elif "启动OTA" in message:
            self.state_manager.set_status(OtaTaskDetailedStatus.STARTING_OTA, message)
        elif "差分" in message:
            self.state_manager.set_status(OtaTaskDetailedStatus.CHECKING_DIFF, message)
        elif "发送" in message or "分片" in message:
            self.state_manager.set_status(OtaTaskDetailedStatus.SENDING_SLICES, message)
        elif "查询" in message:
            self.state_manager.set_status(OtaTaskDetailedStatus.QUERYING_RESULT, message)
        elif "重启" in message:
            self.state_manager.set_status(OtaTaskDetailedStatus.REBOOTING, message)

        # 更新数据库
        thread_safe_db_ops.update_task_status(
            task_id=self.task_id,
            status=self.state_manager.get_legacy_status(),
            progress=self.state_manager.progress.percentage,
            detailed_status=self.state_manager.status.value,
            stage_info=message
        )

        self._emit_progress_update()
    
    def _emit_progress_update(self, custom_message: str = None):
        """发送进度更新消息"""
        try:
            message = custom_message or self.state_manager.progress.message
            emit_task_update(
                task_id=self.task_id,
                status=self.state_manager.get_legacy_status(),
                progress=self.state_manager.progress.percentage,
                message=message
            )
        except Exception as e:
            logger.warning(f"发送WebSocket消息失败: {e}")


def create_ota_task_executor(task_id: int, state_manager: OtaTaskStateManager) -> OtaTaskExecutor:
    """创建OTA任务执行器"""
    return OtaTaskExecutor(task_id, state_manager)
