#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
OTA工具类
提供OTA相关的通用工具函数
"""

import os
import hashlib
from typing import List, Tuple, Optional, Dict
from werkzeug.utils import secure_filename
from models.device import Device
from models.firmware import Firmware
from utils.logger import LoggerManager

# 获取日志记录器
logger = LoggerManager.get_logger()


# 固件验证器已移至 utils.ota_common.FirmwareValidator

class FirmwareUtils:
    """固件工具类"""

    @staticmethod
    def calculate_file_checksum(file_path: str, algorithm: str = "md5") -> Optional[str]:
        """
        计算文件校验和

        Args:
            file_path: 文件路径
            algorithm: 算法类型 (md5, sha1, sha256)

        Returns:
            校验和字符串或None
        """
        try:
            hash_obj = hashlib.new(algorithm)
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_obj.update(chunk)
            return hash_obj.hexdigest()
        except Exception as e:
            logger.error(f"计算文件校验和失败: {e}")
            return None

    @staticmethod
    def extract_version_from_filename(filename: str) -> str:
        """从文件名提取版本信息 - 使用统一工具"""
        from utils.ota_common import VersionUtils
        return VersionUtils.extract_version_from_filename(filename)


# 设备验证器已移至 utils.ota_common.DeviceValidator


# OTA任务助手已移至 utils.ota_common.OtaTaskHelper

    @staticmethod
    def calculate_task_priority(device_id: int, firmware_version: str) -> int:
        """
        计算任务优先级

        Args:
            device_id: 设备ID
            firmware_version: 固件版本

        Returns:
            优先级数字（越小优先级越高）
        """
        # 默认优先级
        priority = 5

        try:
            device = Device.query.get(device_id)
            if device:
                # 可以根据设备类型、重要性等调整优先级
                # 这里是示例逻辑
                if hasattr(device, "priority") and device.priority:
                    priority = device.priority
                elif hasattr(device, "device_type"):
                    # 重要设备优先级更高
                    if device.device_type in ["critical", "重要"]:
                        priority = 1
                    elif device.device_type in ["normal", "普通"]:
                        priority = 5
                    else:
                        priority = 9

        except Exception as e:
            logger.warning(f"计算任务优先级失败: {e}")

        return priority

    @staticmethod
    def format_task_summary(total_tasks: int, successful_tasks: int, failed_tasks: int) -> str:
        """
        格式化任务摘要信息

        Args:
            total_tasks: 总任务数
            successful_tasks: 成功任务数
            failed_tasks: 失败任务数

        Returns:
            格式化的摘要字符串
        """
        pending_tasks = total_tasks - successful_tasks - failed_tasks
        success_rate = (successful_tasks / total_tasks * 100) if total_tasks > 0 else 0

        summary = (
            f"任务总数: {total_tasks}, "
            f"成功: {successful_tasks}, "
            f"失败: {failed_tasks}, "
            f"待处理: {pending_tasks}, "
            f"成功率: {success_rate:.1f}%"
        )

        return summary


class FileManager:
    """文件管理器"""

    @staticmethod
    def save_uploaded_file(file, upload_folder: str, allowed_extensions: List[str] = None) -> Tuple[bool, str, str]:
        """
        保存上传的文件

        Args:
            file: 上传的文件对象
            upload_folder: 上传目录
            allowed_extensions: 允许的扩展名列表

        Returns:
            (是否成功, 文件路径, 错误消息)
        """
        try:
            if not file or not file.filename:
                return False, "", "没有选择文件"

            # 安全的文件名
            filename = secure_filename(file.filename)
            if not filename:
                return False, "", "无效的文件名"

            # 检查扩展名
            if allowed_extensions:
                file_ext = os.path.splitext(filename)[1].lower()
                if file_ext not in allowed_extensions:
                    return False, "", f"不支持的文件格式: {file_ext}"

            # 确保上传目录存在
            os.makedirs(upload_folder, exist_ok=True)

            # 生成唯一文件名（避免冲突）
            base_name, ext = os.path.splitext(filename)
            counter = 1
            final_filename = filename
            file_path = os.path.join(upload_folder, final_filename)

            while os.path.exists(file_path):
                final_filename = f"{base_name}_{counter}{ext}"
                file_path = os.path.join(upload_folder, final_filename)
                counter += 1

            # 保存文件
            file.save(file_path)

            return True, file_path, "文件保存成功"

        except Exception as e:
            return False, "", f"保存文件失败: {e}"

    @staticmethod
    def cleanup_old_files(directory: str, max_age_days: int = 30) -> int:
        """
        清理旧文件

        Args:
            directory: 目录路径
            max_age_days: 最大保留天数

        Returns:
            删除的文件数量
        """
        import time

        deleted_count = 0
        max_age_seconds = max_age_days * 24 * 3600
        current_time = time.time()

        try:
            for filename in os.listdir(directory):
                file_path = os.path.join(directory, filename)
                if os.path.isfile(file_path):
                    file_age = current_time - os.path.getmtime(file_path)
                    if file_age > max_age_seconds:
                        os.remove(file_path)
                        deleted_count += 1
                        logger.info(f"删除旧文件: {file_path}")

        except Exception as e:
            logger.error(f"清理旧文件失败: {e}")

        return deleted_count
