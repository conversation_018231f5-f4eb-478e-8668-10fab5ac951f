#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
设备服务器配置服务
提供设备服务器信息配置和管理功能
"""

import json
import os
import logging
from typing import Dict, List, Optional, Tuple
from flask import current_app

class ServerConfigService:
    """设备服务器配置服务"""
    
    def __init__(self):
        self.config_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', 'server_products.json')
        self._config_data = None
        self.logger = logging.getLogger(__name__)
    
    def _load_config(self) -> Dict:
        """加载配置文件"""
        if self._config_data is None:
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self._config_data = json.load(f)
            except Exception as e:
                self.logger.error(f"加载服务器配置文件失败: {e}")
                self._config_data = {}
        return self._config_data
    
    def get_server_types(self) -> Dict:
        """获取所有服务器类型"""
        config = self._load_config()
        return config.get('server_types', {})
    
    def get_products_by_server(self, server_type: str) -> List[Dict]:
        """根据服务器类型获取产品列表"""
        config = self._load_config()
        return config.get('products', {}).get(server_type, [])
    
    def get_all_products(self) -> Dict[str, List[Dict]]:
        """获取所有产品列表"""
        config = self._load_config()
        return config.get('products', {})
    
    def get_product_by_key(self, product_key: str) -> Optional[Dict]:
        """根据product_key获取产品信息"""
        all_products = self.get_all_products()
        for server_type, products in all_products.items():
            for product in products:
                if product['product_key'] == product_key:
                    return product
        return None
    
    def detect_server_type(self, product_key: str) -> str:
        """根据product_key检测服务器类型"""
        if product_key == "hs7eigK8Xvl":
            return "alicloud"
        elif product_key.startswith("wx"):
            return "emqx"
        else:
            return "unknown"
    
    def get_migration_rules(self) -> Dict:
        """获取迁移规则"""
        config = self._load_config()
        return config.get('migration_rules', {})
    
    def get_available_targets(self, current_product_key: str) -> List[Dict]:
        """获取当前设备可迁移的目标产品列表"""
        current_server_type = self.detect_server_type(current_product_key)
        all_products = self.get_all_products()
        available_targets = []
        
        # 添加同类型服务器的其他产品
        if current_server_type in all_products:
            for product in all_products[current_server_type]:
                if product['product_key'] != current_product_key:
                    available_targets.append({
                        **product,
                        'migration_type': f'{current_server_type}_product_change'
                    })
        
        # 添加其他类型服务器的产品
        for server_type, products in all_products.items():
            if server_type != current_server_type:
                for product in products:
                    available_targets.append({
                        **product,
                        'migration_type': f'{current_server_type}_to_{server_type}'
                    })
        
        return available_targets
    
    def validate_migration(self, source_product_key: str, target_product_key: str) -> Tuple[bool, str]:
        """验证迁移配置的有效性"""
        if source_product_key == target_product_key:
            return False, "源产品和目标产品不能相同"
        
        source_product = self.get_product_by_key(source_product_key)
        target_product = self.get_product_by_key(target_product_key)
        
        if not source_product:
            return False, f"未找到源产品配置: {source_product_key}"
        
        if not target_product:
            return False, f"未找到目标产品配置: {target_product_key}"
        
        return True, "配置验证通过"
    
    def get_server_config(self, server_type: str) -> Dict:
        """获取服务器配置信息"""
        server_types = self.get_server_types()
        return server_types.get(server_type, {})
    
    def format_product_display_name(self, product: Dict) -> str:
        """格式化产品显示名称"""
        return f"{product['name']} ({product['product_key']})"
    
    def get_migration_description(self, source_product_key: str, target_product_key: str) -> str:
        """获取迁移操作描述"""
        source_type = self.detect_server_type(source_product_key)
        target_type = self.detect_server_type(target_product_key)
        
        if source_type == target_type:
            return f"在{self.get_server_types().get(source_type, {}).get('name', source_type)}内更换产品"
        else:
            source_name = self.get_server_types().get(source_type, {}).get('name', source_type)
            target_name = self.get_server_types().get(target_type, {}).get('name', target_type)
            return f"从{source_name}迁移到{target_name}"

# 创建全局服务实例
server_config_service = ServerConfigService()
