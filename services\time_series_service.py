#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
时序数据库服务
用于高效存储和查询设备时序数据
"""

import os
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from sqlalchemy import and_, or_, func, text
from sqlalchemy.exc import SQLAlchemyError
from flask import current_app

from models.database import db
from models.time_series_data import TimeSeriesData, TimeSeriesDataBatch
from utils.logger import LoggerManager

# 获取日志记录器
logger = LoggerManager.get_logger()


class TimeSeriesService:
    """时序数据库服务类"""
    
    def __init__(self):
        self.batch_size = 1000  # 批量插入大小
        self.cache_ttl = 300    # 缓存TTL（秒）
        self._cache = {}        # 简单内存缓存
        
    def write_sensor_data(self, device_id: str, channel_powers: List[float] = None,
                         voltage: float = None, temperature: float = None,
                         total_power: float = None, csq: int = None, ber: int = None,
                         bl0910_error_count: int = None, relay_state: int = None,
                         relay_bits: int = None, short_period_error_count: int = None,
                         long_period_error_count: int = None, zero_cross_time: int = None) -> bool:
        """
        写入传感器数据到时序数据库
        
        Args:
            device_id: 设备ID
            channel_powers: 10个通道的功率值列表
            voltage: 电压值
            temperature: 温度值
            total_power: 总功率
            csq: 信号质量
            ber: 误码率
            bl0910_error_count: BL0910错误计数
            relay_state: 继电器状态
            relay_bits: 继电器位
            short_period_error_count: 短周期错误计数
            long_period_error_count: 长周期错误计数
            zero_cross_time: 零交叉时间
            
        Returns:
            bool: 写入是否成功
        """
        try:
            current_time = datetime.now()
            data_records = []
            
            # 创建批次记录
            batch = TimeSeriesDataBatch(
                device_id=device_id,
                batch_timestamp=current_time,
                status='pending'
            )
            
            # 写入功率数据
            if channel_powers and len(channel_powers) >= 10:
                # 单独存储每个通道的功率
                for i, power in enumerate(channel_powers[:10]):
                    data_records.append(TimeSeriesData(
                        device_id=device_id,
                        timestamp=current_time,
                        data_type=f'power_channel_{i+1}',
                        data_value={'channel': i+1, 'power': power},
                        numeric_value=power
                    ))
                
                # 存储所有通道功率的汇总
                data_records.append(TimeSeriesData(
                    device_id=device_id,
                    timestamp=current_time,
                    data_type='power_channels',
                    data_value={'channels': channel_powers[:10]},
                    numeric_value=sum(channel_powers[:10])
                ))
            
            # 写入其他单值数据
            single_value_data = {
                'voltage': voltage,
                'temperature': temperature,
                'total_power': total_power,
                'csq': csq,
                'ber': ber,
                'bl0910_error_count': bl0910_error_count,
                'relay_state': relay_state,
                'relay_bits': relay_bits,
                'short_period_error_count': short_period_error_count,
                'long_period_error_count': long_period_error_count,
                'zero_cross_time': zero_cross_time
            }
            
            for data_type, value in single_value_data.items():
                if value is not None:
                    # 安全处理字符串值，避免过长
                    string_val = None
                    numeric_val = None

                    if isinstance(value, (int, float)):
                        numeric_val = float(value)
                    else:
                        string_val = str(value)
                        # 如果字符串过长，截断并记录警告
                        if len(string_val) > 1000:  # 设置合理的长度限制
                            logger.warning(f"数据类型 {data_type} 的字符串值过长，已截断: {len(string_val)} 字符")
                            string_val = string_val[:1000] + "...[截断]"

                    data_records.append(TimeSeriesData(
                        device_id=device_id,
                        timestamp=current_time,
                        data_type=data_type,
                        data_value={'value': value},
                        numeric_value=numeric_val,
                        string_value=string_val
                    ))
            
            # 批量插入数据
            if data_records:
                db.session.add(batch)
                db.session.add_all(data_records)
                
                batch.data_count = len(data_records)
                batch.status = 'completed'
                batch.completed_at = datetime.now()
                
                db.session.commit()
                
                logger.info(f"成功写入设备 {device_id} 的 {len(data_records)} 条时序数据")
                return True
            else:
                logger.warning(f"设备 {device_id} 没有有效数据需要写入")
                return False
                
        except SQLAlchemyError as e:
            db.session.rollback()
            logger.error(f"写入时序数据异常: {e}")
            
            # 更新批次状态为失败
            if 'batch' in locals():
                try:
                    batch.status = 'failed'
                    batch.error_message = str(e)
                    batch.completed_at = datetime.now()
                    db.session.commit()
                except:
                    pass
            
            return False
        except Exception as e:
            db.session.rollback()
            logger.error(f"写入时序数据异常: {e}")
            return False
    
    def query_power_data(self, device_id: str, start_time: datetime,
                        end_time: Optional[datetime] = None) -> Dict[str, List[Dict[str, Any]]]:
        """
        查询设备功率数据
        
        Args:
            device_id: 设备ID
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            Dict: 包含各通道功率数据的字典
        """
        try:
            if end_time is None:
                end_time = datetime.now()
            
            # 生成缓存键
            cache_key = f"power_{device_id}_{start_time.date()}_{end_time.date()}"
            
            # 检查缓存
            cached_data = self._get_from_cache(cache_key)
            if cached_data is not None:
                return cached_data
            
            # 查询各通道功率数据
            power_data = db.session.query(TimeSeriesData).filter(
                and_(
                    TimeSeriesData.device_id == device_id,
                    TimeSeriesData.data_type.like('power_channel_%'),
                    TimeSeriesData.timestamp >= start_time,
                    TimeSeriesData.timestamp <= end_time
                )
            ).order_by(TimeSeriesData.timestamp).all()
            
            # 组织数据格式
            result_data = {f"channel_{i+1}": [] for i in range(10)}
            
            for record in power_data:
                if record.data_value and 'channel' in record.data_value:
                    channel_num = record.data_value['channel']
                    channel_key = f"channel_{channel_num}"
                    
                    if channel_key in result_data:
                        result_data[channel_key].append({
                            "time": record.timestamp.isoformat(),
                            "value": record.numeric_value or record.data_value.get('power', 0)
                        })
            
            # 缓存结果
            self._set_cache(cache_key, result_data)
            
            return result_data
            
        except Exception as e:
            logger.error(f"查询功率数据异常: {e}")
            return {}
    
    def query_single_value_data(self, device_id: str, data_type: str,
                               start_time: datetime, end_time: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """
        查询单值数据（温度、电压等）
        
        Args:
            device_id: 设备ID
            data_type: 数据类型
            start_time: 开始时间
            end_time: 结束时间
            
        Returns:
            List: 包含时序数据的列表
        """
        try:
            if end_time is None:
                end_time = datetime.now()
            
            # 生成缓存键
            cache_key = f"{data_type}_{device_id}_{start_time.date()}_{end_time.date()}"
            
            # 检查缓存
            cached_data = self._get_from_cache(cache_key)
            if cached_data is not None:
                return cached_data
            
            # 查询数据
            data_records = db.session.query(TimeSeriesData).filter(
                and_(
                    TimeSeriesData.device_id == device_id,
                    TimeSeriesData.data_type == data_type,
                    TimeSeriesData.timestamp >= start_time,
                    TimeSeriesData.timestamp <= end_time
                )
            ).order_by(TimeSeriesData.timestamp).all()
            
            # 组织数据格式
            result_data = []
            for record in data_records:
                value = record.numeric_value
                if value is None and record.data_value:
                    value = record.data_value.get('value')
                
                result_data.append({
                    "time": record.timestamp.isoformat(),
                    "value": value
                })
            
            # 缓存结果
            self._set_cache(cache_key, result_data)
            
            return result_data

        except Exception as e:
            logger.error(f"查询{data_type}数据异常: {e}")
            return []

    def query_temperature_data(self, device_id: str, start_time: datetime,
                              end_time: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """查询设备温度数据"""
        return self.query_single_value_data(device_id, 'temperature', start_time, end_time)

    def query_voltage_data(self, device_id: str, start_time: datetime,
                          end_time: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """查询设备电压数据"""
        return self.query_single_value_data(device_id, 'voltage', start_time, end_time)

    def query_total_power_data(self, device_id: str, start_time: datetime,
                              end_time: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """查询设备总功率数据"""
        return self.query_single_value_data(device_id, 'total_power', start_time, end_time)

    def query_csq_data(self, device_id: str, start_time: datetime,
                      end_time: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """
        查询设备信号质量数据

        Returns:
            List: 包含CSQ和BER数据的列表
        """
        try:
            if end_time is None:
                end_time = datetime.now()

            # 生成缓存键
            cache_key = f"csq_{device_id}_{start_time.date()}_{end_time.date()}"

            # 检查缓存
            cached_data = self._get_from_cache(cache_key)
            if cached_data is not None:
                return cached_data

            # 查询CSQ和BER数据
            csq_records = db.session.query(TimeSeriesData).filter(
                and_(
                    TimeSeriesData.device_id == device_id,
                    or_(TimeSeriesData.data_type == 'csq', TimeSeriesData.data_type == 'ber'),
                    TimeSeriesData.timestamp >= start_time,
                    TimeSeriesData.timestamp <= end_time
                )
            ).order_by(TimeSeriesData.timestamp).all()

            # 组织数据格式，将CSQ和BER按时间戳合并
            data_map = {}
            for record in csq_records:
                timestamp_key = record.timestamp.isoformat()
                if timestamp_key not in data_map:
                    data_map[timestamp_key] = {
                        "time": timestamp_key,
                        "csq": None,
                        "ber": None
                    }

                if record.data_type == 'csq':
                    data_map[timestamp_key]["csq"] = record.numeric_value
                elif record.data_type == 'ber':
                    data_map[timestamp_key]["ber"] = record.numeric_value

            # 转换为列表格式，只保留有CSQ数据的记录
            result_data = []
            for timestamp_key in sorted(data_map.keys()):
                data_point = data_map[timestamp_key]
                if data_point["csq"] is not None:
                    result_data.append({
                        "time": data_point["time"],
                        "value": data_point["csq"],
                        "ber": data_point["ber"] or 99  # BER默认值99表示未知
                    })

            # 缓存结果
            self._set_cache(cache_key, result_data)

            return result_data

        except Exception as e:
            logger.error(f"查询信号质量数据异常: {e}")
            return []

    def delete_old_data(self, days_to_keep: int = 30) -> bool:
        """
        删除旧数据，保留指定天数的数据

        Args:
            days_to_keep: 保留的天数

        Returns:
            bool: 删除是否成功
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)

            # 删除旧的时序数据
            deleted_count = db.session.query(TimeSeriesData).filter(
                TimeSeriesData.timestamp < cutoff_date
            ).delete()

            # 删除旧的批次记录
            batch_deleted_count = db.session.query(TimeSeriesDataBatch).filter(
                TimeSeriesDataBatch.batch_timestamp < cutoff_date
            ).delete()

            db.session.commit()

            logger.info(f"删除了 {deleted_count} 条时序数据和 {batch_deleted_count} 条批次记录")
            return True

        except Exception as e:
            db.session.rollback()
            logger.error(f"删除旧数据异常: {e}")
            return False

    def get_data_statistics(self, device_id: str = None) -> Dict[str, Any]:
        """
        获取数据统计信息

        Args:
            device_id: 设备ID，如果为None则统计所有设备

        Returns:
            Dict: 统计信息
        """
        try:
            query = db.session.query(
                TimeSeriesData.data_type,
                func.count(TimeSeriesData.id).label('count'),
                func.min(TimeSeriesData.timestamp).label('earliest'),
                func.max(TimeSeriesData.timestamp).label('latest')
            )

            if device_id:
                query = query.filter(TimeSeriesData.device_id == device_id)

            stats = query.group_by(TimeSeriesData.data_type).all()

            result = {
                'total_records': sum(stat.count for stat in stats),
                'data_types': {},
                'earliest_record': None,
                'latest_record': None
            }

            for stat in stats:
                result['data_types'][stat.data_type] = {
                    'count': stat.count,
                    'earliest': stat.earliest.isoformat() if stat.earliest else None,
                    'latest': stat.latest.isoformat() if stat.latest else None
                }

                # 更新全局最早和最晚记录
                if result['earliest_record'] is None or (stat.earliest and stat.earliest < datetime.fromisoformat(result['earliest_record'])):
                    result['earliest_record'] = stat.earliest.isoformat() if stat.earliest else None
                if result['latest_record'] is None or (stat.latest and stat.latest > datetime.fromisoformat(result['latest_record'])):
                    result['latest_record'] = stat.latest.isoformat() if stat.latest else None

            return result

        except Exception as e:
            logger.error(f"获取数据统计异常: {e}")
            return {}

    def _get_from_cache(self, key: str) -> Any:
        """从缓存获取数据"""
        if key in self._cache:
            cache_entry = self._cache[key]
            if datetime.now() - cache_entry['timestamp'] < timedelta(seconds=self.cache_ttl):
                return cache_entry['data']
            else:
                del self._cache[key]
        return None

    def _set_cache(self, key: str, data: Any) -> None:
        """设置缓存数据"""
        self._cache[key] = {
            'data': data,
            'timestamp': datetime.now()
        }

        # 简单的缓存清理，防止内存泄漏
        if len(self._cache) > 100:
            # 删除最旧的缓存项
            oldest_key = min(self._cache.keys(), key=lambda k: self._cache[k]['timestamp'])
            del self._cache[oldest_key]


    def query_data_with_pagination(self, device_id: str, data_type: str,
                                  start_time: datetime, end_time: Optional[datetime] = None,
                                  page: int = 1, page_size: int = 1000) -> Dict[str, Any]:
        """
        分页查询时序数据

        Args:
            device_id: 设备ID
            data_type: 数据类型
            start_time: 开始时间
            end_time: 结束时间
            page: 页码（从1开始）
            page_size: 每页大小

        Returns:
            Dict: 包含数据和分页信息的字典
        """
        try:
            if end_time is None:
                end_time = datetime.now()

            # 计算偏移量
            offset = (page - 1) * page_size

            # 查询总数
            total_count = db.session.query(func.count(TimeSeriesData.id)).filter(
                and_(
                    TimeSeriesData.device_id == device_id,
                    TimeSeriesData.data_type == data_type,
                    TimeSeriesData.timestamp >= start_time,
                    TimeSeriesData.timestamp <= end_time
                )
            ).scalar()

            # 查询数据
            data_records = db.session.query(TimeSeriesData).filter(
                and_(
                    TimeSeriesData.device_id == device_id,
                    TimeSeriesData.data_type == data_type,
                    TimeSeriesData.timestamp >= start_time,
                    TimeSeriesData.timestamp <= end_time
                )
            ).order_by(TimeSeriesData.timestamp).offset(offset).limit(page_size).all()

            # 组织数据格式
            result_data = []
            for record in data_records:
                value = record.numeric_value
                if value is None and record.data_value:
                    value = record.data_value.get('value')

                result_data.append({
                    "time": record.timestamp.isoformat(),
                    "value": value
                })

            # 计算分页信息
            total_pages = (total_count + page_size - 1) // page_size

            return {
                'data': result_data,
                'pagination': {
                    'page': page,
                    'page_size': page_size,
                    'total_count': total_count,
                    'total_pages': total_pages,
                    'has_next': page < total_pages,
                    'has_prev': page > 1
                }
            }

        except Exception as e:
            logger.error(f"分页查询{data_type}数据异常: {e}")
            return {
                'data': [],
                'pagination': {
                    'page': page,
                    'page_size': page_size,
                    'total_count': 0,
                    'total_pages': 0,
                    'has_next': False,
                    'has_prev': False
                }
            }

    def query_data_summary(self, device_id: str, data_type: str,
                          start_time: datetime, end_time: Optional[datetime] = None,
                          interval_minutes: int = 60) -> List[Dict[str, Any]]:
        """
        查询数据摘要（按时间间隔聚合）

        Args:
            device_id: 设备ID
            data_type: 数据类型
            start_time: 开始时间
            end_time: 结束时间
            interval_minutes: 聚合间隔（分钟）

        Returns:
            List: 聚合后的数据列表
        """
        try:
            if end_time is None:
                end_time = datetime.now()

            # 使用PostgreSQL的date_trunc函数进行时间聚合
            interval_expr = text(f"date_trunc('hour', timestamp) + interval '{interval_minutes} minutes' * floor(extract(minute from timestamp) / {interval_minutes})")

            # 查询聚合数据
            aggregated_data = db.session.query(
                interval_expr.label('time_bucket'),
                func.avg(TimeSeriesData.numeric_value).label('avg_value'),
                func.min(TimeSeriesData.numeric_value).label('min_value'),
                func.max(TimeSeriesData.numeric_value).label('max_value'),
                func.count(TimeSeriesData.id).label('count')
            ).filter(
                and_(
                    TimeSeriesData.device_id == device_id,
                    TimeSeriesData.data_type == data_type,
                    TimeSeriesData.timestamp >= start_time,
                    TimeSeriesData.timestamp <= end_time,
                    TimeSeriesData.numeric_value.isnot(None)
                )
            ).group_by(interval_expr).order_by(interval_expr).all()

            # 组织数据格式
            result_data = []
            for record in aggregated_data:
                result_data.append({
                    "time": record.time_bucket.isoformat(),
                    "avg_value": float(record.avg_value) if record.avg_value else None,
                    "min_value": float(record.min_value) if record.min_value else None,
                    "max_value": float(record.max_value) if record.max_value else None,
                    "count": record.count
                })

            return result_data

        except Exception as e:
            logger.error(f"查询{data_type}数据摘要异常: {e}")
            return []


# 创建全局实例
time_series_service = TimeSeriesService()
