-- 充电桩管理系统性能优化SQL脚本
-- 用于添加必要的数据库索引以提升查询性能

-- 1. 设备表性能优化索引
-- 为设备表的常用筛选字段添加索引

-- 产品密钥索引（用于产品筛选）
CREATE INDEX IF NOT EXISTS idx_device_product_key ON device(product_key);

-- 固件版本索引（用于固件筛选）
CREATE INDEX IF NOT EXISTS idx_device_firmware_version ON device(firmware_version);

-- 设备备注索引（用于备注搜索）
CREATE INDEX IF NOT EXISTS idx_device_remark ON device(device_remark);

-- OTA状态索引（用于OTA状态筛选）
CREATE INDEX IF NOT EXISTS idx_device_ota_status ON device(last_ota_status);

-- 创建时间索引（用于时间范围筛选）
CREATE INDEX IF NOT EXISTS idx_device_created_at ON device(created_at);

-- OTA时间索引（用于OTA时间筛选）
CREATE INDEX IF NOT EXISTS idx_device_ota_time ON device(last_ota_time);

-- 复合索引：产品密钥+固件版本（常用组合查询）
CREATE INDEX IF NOT EXISTS idx_device_product_firmware ON device(product_key, firmware_version);

-- 复合索引：OTA状态+时间（OTA相关查询）
CREATE INDEX IF NOT EXISTS idx_device_ota_status_time ON device(last_ota_status, last_ota_time);

-- 2. OTA任务表性能优化索引
-- 为OTA任务表添加必要的索引

-- 设备ID索引（已存在，确保存在）
CREATE INDEX IF NOT EXISTS idx_ota_task_device_id ON ota_task(device_id);

-- 状态索引（已存在，确保存在）
CREATE INDEX IF NOT EXISTS idx_ota_task_status ON ota_task(status);

-- 固件版本索引（用于固件筛选）
CREATE INDEX IF NOT EXISTS idx_ota_task_firmware_version ON ota_task(firmware_version);

-- 创建时间索引（用于时间排序和筛选）
CREATE INDEX IF NOT EXISTS idx_ota_task_created_at ON ota_task(created_at);

-- 更新时间索引（用于最近更新排序）
CREATE INDEX IF NOT EXISTS idx_ota_task_updated_at ON ota_task(updated_at);

-- 复合索引：状态+创建时间（常用组合查询）
CREATE INDEX IF NOT EXISTS idx_ota_task_status_created ON ota_task(status, created_at);

-- 复合索引：设备ID+状态（设备相关任务查询）
CREATE INDEX IF NOT EXISTS idx_ota_task_device_status ON ota_task(device_id, status);

-- 3. 设备参数表性能优化索引
-- 为设备参数表添加必要的索引

-- 参数名索引（用于参数类型筛选）
CREATE INDEX IF NOT EXISTS idx_device_parameter_name ON device_parameter(param_name);

-- 复合索引：设备ID+参数名（常用组合查询）
CREATE INDEX IF NOT EXISTS idx_device_parameter_device_name ON device_parameter(device_id, param_name);

-- 更新时间索引（用于最新参数查询）
CREATE INDEX IF NOT EXISTS idx_device_parameter_updated_at ON device_parameter(updated_at);

-- 4. 设备位置表性能优化索引
-- 为设备位置表添加必要的索引

-- 创建时间索引
CREATE INDEX IF NOT EXISTS idx_device_location_created_at ON device_locations(created_at);

-- 更新时间索引
CREATE INDEX IF NOT EXISTS idx_device_location_updated_at ON device_locations(updated_at);

-- 地址索引（用于地址搜索）
CREATE INDEX IF NOT EXISTS idx_device_location_address ON device_locations(address);

-- 5. 用户相关表性能优化索引
-- 登录日志表索引优化

-- 登录时间索引（用于时间范围查询）
CREATE INDEX IF NOT EXISTS idx_login_logs_login_time ON login_logs(login_time);

-- 用户名索引（用于用户筛选）
CREATE INDEX IF NOT EXISTS idx_login_logs_username ON login_logs(username);

-- IP地址索引（用于IP筛选）
CREATE INDEX IF NOT EXISTS idx_login_logs_ip ON login_logs(ip_address);

-- 复合索引：用户ID+登录时间（用户登录历史查询）
CREATE INDEX IF NOT EXISTS idx_login_logs_user_time ON login_logs(user_id, login_time);

-- 6. 固件表性能优化索引
-- 为固件表添加必要的索引

-- 版本索引（用于版本筛选）
CREATE INDEX IF NOT EXISTS idx_firmware_version ON firmware(version);

-- 上传时间索引（用于时间排序）
CREATE INDEX IF NOT EXISTS idx_firmware_upload_time ON firmware(upload_time);

-- 名称索引（用于名称搜索）
CREATE INDEX IF NOT EXISTS idx_firmware_name ON firmware(name);

-- 下载次数索引（用于热门固件查询）
CREATE INDEX IF NOT EXISTS idx_firmware_download_count ON firmware(download_count);

-- 7. 调试脚本表性能优化索引
-- 为调试脚本表添加必要的索引

-- 启用状态索引（用于启用状态筛选）
CREATE INDEX IF NOT EXISTS idx_debug_script_enabled ON debug_script(enabled);

-- 最后执行时间索引（用于执行历史查询）
CREATE INDEX IF NOT EXISTS idx_debug_script_last_execution ON debug_script(last_execution_time);

-- 复合索引：设备ID+启用状态（设备调试脚本查询）
CREATE INDEX IF NOT EXISTS idx_debug_script_device_enabled ON debug_script(device_id, enabled);

-- 8. 商户表性能优化索引
-- 为商户表添加必要的索引

-- 商户名称索引（用于名称搜索）
CREATE INDEX IF NOT EXISTS idx_merchant_name ON merchants(name);

-- 联系人索引（用于联系人搜索）
CREATE INDEX IF NOT EXISTS idx_merchant_contact_person ON merchants(contact_person);

-- 联系电话索引（用于电话搜索）
CREATE INDEX IF NOT EXISTS idx_merchant_contact_phone ON merchants(contact_phone);

-- 9. 付费下载相关表性能优化索引
-- 付费下载表索引

-- 用户ID索引（用于用户下载历史）
CREATE INDEX IF NOT EXISTS idx_paid_downloads_user_id ON paid_downloads(user_id);

-- 创建时间索引（用于时间排序）
CREATE INDEX IF NOT EXISTS idx_paid_downloads_created_at ON paid_downloads(created_at);

-- 下载订单表索引
-- 用户ID索引（用于用户订单历史）
CREATE INDEX IF NOT EXISTS idx_download_orders_user_id ON download_orders(user_id);

-- 订单状态索引（用于状态筛选）
CREATE INDEX IF NOT EXISTS idx_download_orders_status ON download_orders(status);

-- 创建时间索引（用于时间排序）
CREATE INDEX IF NOT EXISTS idx_download_orders_created_at ON download_orders(created_at);

-- 复合索引：用户ID+状态（用户订单状态查询）
CREATE INDEX IF NOT EXISTS idx_download_orders_user_status ON download_orders(user_id, status);

-- 10. 分析查询计划（PostgreSQL特有）
-- 更新表统计信息以优化查询计划
ANALYZE device;
ANALYZE ota_task;
ANALYZE device_parameter;
ANALYZE device_locations;
ANALYZE login_logs;
ANALYZE firmware;
ANALYZE debug_script;
ANALYZE merchants;
ANALYZE paid_downloads;
ANALYZE download_orders;

-- 脚本执行完成提示
SELECT 'Performance optimization indexes created successfully!' as result;
