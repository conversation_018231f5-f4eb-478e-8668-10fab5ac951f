# 数据库管理工具集

本文件夹包含了充电桩管理系统数据库迁移和管理的相关工具脚本。

## 📁 文件说明

### 🔧 核心迁移工具

#### `database_migration.py`
**主要数据库迁移脚本**
- **功能**: 将SQLite数据库完整迁移到PostgreSQL
- **特性**: 
  - 自动创建表结构和索引
  - 智能数据类型转换
  - 处理外键约束和数据完整性
  - 支持批量数据迁移
  - 跳过孤立记录保证数据一致性
- **使用方法**: `python database_migration.py`
- **输出**: 详细的迁移日志和结果报告

#### `update_config_for_postgresql.py`
**应用配置更新脚本**
- **功能**: 更新Flask应用配置以使用PostgreSQL
- **特性**:
  - 备份原始配置文件
  - 生成PostgreSQL连接配置
  - 创建环境切换脚本
  - 更新依赖文件
- **使用方法**: `python update_config_for_postgresql.py`
- **输出**: 
  - 更新的`config.py`
  - 环境启动脚本(`start_dev.bat`, `start_prod.bat`)
  - 迁移总结文档

### 🔍 测试和验证工具

#### `test_postgresql_connection.py`
**PostgreSQL连接测试**
- **功能**: 测试PostgreSQL数据库连接和权限
- **使用方法**: `python test_postgresql_connection.py`
- **验证项目**:
  - 数据库连接状态
  - 用户权限
  - 表创建权限

#### `verify_migration_success.py`
**迁移结果验证**
- **功能**: 验证数据迁移的完整性和正确性
- **使用方法**: `python verify_migration_success.py`
- **验证项目**:
  - 数据行数对比
  - 表结构一致性
  - 数据完整性检查

#### `test_postgresql_app.py`
**应用功能测试**
- **功能**: 测试Flask应用在PostgreSQL环境下的功能
- **使用方法**: `python test_postgresql_app.py`
- **测试项目**:
  - 数据库连接
  - 基本CRUD操作
  - Web路由响应
  - 查询性能

### 🔧 辅助工具

#### `check_data_integrity.py`
**数据完整性检查**
- **功能**: 检查SQLite数据库中的数据完整性问题
- **使用方法**: `python check_data_integrity.py`
- **检查项目**:
  - 外键约束违反
  - 孤立记录
  - 数据一致性

#### `check_db_structure.py`
**数据库结构分析**
- **功能**: 分析SQLite数据库的表结构和数据统计
- **使用方法**: `python check_db_structure.py`
- **输出信息**:
  - 表列表和结构
  - 数据行数统计
  - 外键关系

#### `check_postgresql_permissions.py`
**PostgreSQL权限检查**
- **功能**: 检查PostgreSQL用户权限和schema访问
- **使用方法**: `python check_postgresql_permissions.py`
- **检查项目**:
  - 用户权限
  - Schema创建权限
  - 表操作权限

#### `check_debug_db.py`
**调试数据库检查**
- **功能**: 专门检查调试环境数据库的连接和状态
- **使用方法**: `python check_debug_db.py`

## 🚀 使用流程

### 完整迁移流程
```bash
# 1. 检查源数据库结构
python check_db_structure.py

# 2. 检查数据完整性
python check_data_integrity.py

# 3. 测试PostgreSQL连接
python test_postgresql_connection.py

# 4. 检查PostgreSQL权限
python check_postgresql_permissions.py

# 5. 执行数据迁移
python database_migration.py

# 6. 验证迁移结果
python verify_migration_success.py

# 7. 更新应用配置
python update_config_for_postgresql.py

# 8. 测试应用功能
python test_postgresql_app.py
```

### 快速验证
```bash
# 验证迁移是否成功
python verify_migration_success.py

# 测试应用是否正常
python test_postgresql_app.py
```

## 📋 数据库配置信息

### 生产环境
- **主机**: 14.103.149.252:5432
- **数据库**: kafangcharging
- **Schema**: kafanglinlin_schema
- **用户**: kafanglinlin

### 调试环境
- **主机**: 14.103.149.252:5432
- **数据库**: kfchargingdbg
- **Schema**: kfchargingdbgc_schema
- **用户**: KfChargingDbgC

## ⚠️ 注意事项

1. **备份重要性**: 运行迁移前确保已备份原始数据
2. **权限要求**: 确保PostgreSQL用户有足够的权限
3. **网络连接**: 确保能访问PostgreSQL服务器
4. **依赖安装**: 确保已安装`psycopg2-binary`
5. **环境变量**: 根据需要设置`FLASK_ENV`环境变量

## 🐛 故障排除

### 常见问题
1. **连接失败**: 检查网络和防火墙设置
2. **权限不足**: 联系数据库管理员分配权限
3. **数据类型错误**: 检查SQLite到PostgreSQL的类型映射
4. **外键约束**: 检查数据完整性，清理孤立记录

### 日志文件
- 迁移过程会生成详细的日志文件
- 日志文件名格式: `migration_YYYYMMDD_HHMMSS.log`
- 查看日志获取详细的错误信息

## 📞 支持

如有问题，请查看：
1. 日志文件中的详细错误信息
2. `MIGRATION_SUMMARY.md`中的迁移总结
3. 各脚本的输出信息

---
*最后更新: 2025-07-30*
