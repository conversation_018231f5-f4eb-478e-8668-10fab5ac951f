-- =====================================================
-- 清理旧表和约束
-- 删除不再需要的device_parameter、device_locations、debug_script表
-- =====================================================

-- 设置schema
SET search_path TO kfchargingdbgc_schema;

-- 显示清理前的表信息
SELECT 'device_parameter表信息:' as info;
SELECT 
    COUNT(*) as total_records,
    COUNT(DISTINCT device_id) as unique_devices,
    MIN(updated_at) as earliest_update,
    MAX(updated_at) as latest_update
FROM device_parameter;

SELECT 'device_locations表信息:' as info;
SELECT 
    COUNT(*) as total_records,
    COUNT(DISTINCT device_id) as unique_devices,
    MIN(created_at) as earliest_created,
    MAX(updated_at) as latest_update
FROM device_locations;

SELECT 'debug_script表信息:' as info;
SELECT 
    COUNT(*) as total_records,
    COUNT(DISTINCT device_id) as unique_devices,
    MIN(created_at) as earliest_created,
    MAX(updated_at) as latest_update
FROM debug_script;

-- 检查是否有其他表引用这些表
SELECT '检查外键依赖:' as info;
SELECT 
    tc.table_name,
    tc.constraint_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_schema = 'kfchargingdbgc_schema'
    AND (ccu.table_name IN ('device_parameter', 'device_locations', 'debug_script')
         OR tc.table_name IN ('device_parameter', 'device_locations', 'debug_script'));

-- 备份旧表（重命名为_backup）
SELECT 'Creating backup tables...' as info;

-- 备份device_parameter表
ALTER TABLE device_parameter RENAME TO device_parameter_backup;

-- 备份device_locations表  
ALTER TABLE device_locations RENAME TO device_locations_backup;

-- 备份debug_script表
ALTER TABLE debug_script RENAME TO debug_script_backup;

-- 验证备份
SELECT 'Backup verification:' as info;
SELECT 
    'device_parameter_backup' as table_name,
    COUNT(*) as record_count
FROM device_parameter_backup
UNION ALL
SELECT 
    'device_locations_backup' as table_name,
    COUNT(*) as record_count
FROM device_locations_backup
UNION ALL
SELECT 
    'debug_script_backup' as table_name,
    COUNT(*) as record_count
FROM debug_script_backup;

-- 显示当前schema中的表
SELECT 'Current tables in schema:' as info;
SELECT 
    table_name,
    table_type
FROM information_schema.tables 
WHERE table_schema = 'kfchargingdbgc_schema'
    AND table_type = 'BASE TABLE'
ORDER BY table_name;

-- 验证新的device_new表数据完整性
SELECT 'device_new表数据验证:' as info;
SELECT 
    COUNT(*) as total_devices,
    COUNT(CASE WHEN latitude IS NOT NULL THEN 1 END) as devices_with_location,
    COUNT(CASE WHEN debug_created_at IS NOT NULL THEN 1 END) as devices_with_debug,
    COUNT(CASE WHEN array_length(register_names, 1) > 0 THEN 1 END) as devices_with_parameters,
    AVG(array_length(register_names, 1)) as avg_parameters_per_device
FROM device_new;

-- 显示一些示例数据验证迁移正确性
SELECT 'Sample data verification:' as info;
SELECT 
    device_id,
    device_remark,
    CASE WHEN latitude IS NOT NULL THEN 'Yes' ELSE 'No' END as has_location,
    CASE WHEN debug_created_at IS NOT NULL THEN 'Yes' ELSE 'No' END as has_debug,
    array_length(register_names, 1) as param_count
FROM device_new 
WHERE array_length(register_names, 1) > 0 OR latitude IS NOT NULL OR debug_created_at IS NOT NULL
LIMIT 5;

SELECT 'Cleanup completed successfully!' as result;
