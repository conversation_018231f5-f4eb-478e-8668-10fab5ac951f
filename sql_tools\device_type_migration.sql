-- 设备类型字段迁移脚本
-- 为firmware表和device表添加设备类型字段，并创建latest_firmware表
-- 执行前请确保已备份数据库

-- =====================================================
-- 测试环境数据库迁移 (kfchargingdbg)
-- Schema: kfchargingdbgc_schema
-- =====================================================

-- 设置搜索路径到测试环境schema
SET search_path TO kfchargingdbgc_schema, public;

-- 检查当前表结构
SELECT 'firmware表当前结构:' as info;
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_schema = 'kfchargingdbgc_schema' 
  AND table_name = 'firmware' 
ORDER BY ordinal_position;

SELECT 'device表当前结构:' as info;
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_schema = 'kfchargingdbgc_schema' 
  AND table_name = 'device' 
ORDER BY ordinal_position;

-- =====================================================
-- 1. 为firmware表添加设备类型字段
-- =====================================================

-- 添加设备类型字段到firmware表
ALTER TABLE kfchargingdbgc_schema.firmware 
ADD COLUMN IF NOT EXISTS device_type INTEGER NOT NULL DEFAULT 50;

-- 为设备类型字段添加注释
COMMENT ON COLUMN kfchargingdbgc_schema.firmware.device_type IS '设备类型：10=V2(旧版霍尔传感器版本，黑色PCB), 50=V5(新版BL0910 10通道版本), 51=V51(新版BL0939 2通道版本)';

-- 创建设备类型检查约束
ALTER TABLE kfchargingdbgc_schema.firmware 
ADD CONSTRAINT chk_firmware_device_type 
CHECK (device_type IN (10, 50, 51));

-- =====================================================
-- 2. 为device表添加设备类型字段
-- =====================================================

-- 添加设备类型字段到device表
ALTER TABLE kfchargingdbgc_schema.device 
ADD COLUMN IF NOT EXISTS device_type INTEGER DEFAULT NULL;

-- 为设备类型字段添加注释
COMMENT ON COLUMN kfchargingdbgc_schema.device.device_type IS '设备类型：10=V2(旧版霍尔传感器版本，黑色PCB), 50=V5(新版BL0910 10通道版本), 51=V51(新版BL0939 2通道版本)';

-- 创建设备类型检查约束
ALTER TABLE kfchargingdbgc_schema.device 
ADD CONSTRAINT chk_device_device_type 
CHECK (device_type IS NULL OR device_type IN (10, 50, 51));

-- =====================================================
-- 3. 创建latest_firmware表
-- =====================================================

-- 创建latest_firmware表
CREATE TABLE IF NOT EXISTS kfchargingdbgc_schema.latest_firmware (
    id SERIAL PRIMARY KEY,
    device_type INTEGER NOT NULL UNIQUE,
    firmware_id INTEGER NOT NULL REFERENCES kfchargingdbgc_schema.firmware(id) ON DELETE CASCADE,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(80) DEFAULT NULL,
    CONSTRAINT chk_latest_firmware_device_type CHECK (device_type IN (10, 50, 51))
);

-- 为latest_firmware表添加注释
COMMENT ON TABLE kfchargingdbgc_schema.latest_firmware IS '各设备类型最新固件管理表';
COMMENT ON COLUMN kfchargingdbgc_schema.latest_firmware.device_type IS '设备类型：10=V2, 50=V5, 51=V51';
COMMENT ON COLUMN kfchargingdbgc_schema.latest_firmware.firmware_id IS '关联的固件ID';
COMMENT ON COLUMN kfchargingdbgc_schema.latest_firmware.updated_at IS '更新时间';
COMMENT ON COLUMN kfchargingdbgc_schema.latest_firmware.updated_by IS '更新者';

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_latest_firmware_device_type 
ON kfchargingdbgc_schema.latest_firmware(device_type);

CREATE INDEX IF NOT EXISTS idx_latest_firmware_firmware_id 
ON kfchargingdbgc_schema.latest_firmware(firmware_id);

-- =====================================================
-- 4. 创建更新触发器
-- =====================================================

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION kfchargingdbgc_schema.update_latest_firmware_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器
DROP TRIGGER IF EXISTS trigger_update_latest_firmware_timestamp 
ON kfchargingdbgc_schema.latest_firmware;

CREATE TRIGGER trigger_update_latest_firmware_timestamp
    BEFORE UPDATE ON kfchargingdbgc_schema.latest_firmware
    FOR EACH ROW
    EXECUTE FUNCTION kfchargingdbgc_schema.update_latest_firmware_timestamp();

-- =====================================================
-- 5. 验证迁移结果
-- =====================================================

-- 检查firmware表结构
SELECT 'firmware表迁移后结构:' as info;
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_schema = 'kfchargingdbgc_schema' 
  AND table_name = 'firmware' 
ORDER BY ordinal_position;

-- 检查device表结构
SELECT 'device表迁移后结构:' as info;
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_schema = 'kfchargingdbgc_schema' 
  AND table_name = 'device' 
ORDER BY ordinal_position;

-- 检查latest_firmware表结构
SELECT 'latest_firmware表结构:' as info;
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_schema = 'kfchargingdbgc_schema' 
  AND table_name = 'latest_firmware' 
ORDER BY ordinal_position;

-- 检查约束
SELECT 'firmware表约束:' as info;
SELECT constraint_name, constraint_type 
FROM information_schema.table_constraints 
WHERE table_schema = 'kfchargingdbgc_schema' 
  AND table_name = 'firmware'
  AND constraint_name LIKE '%device_type%';

SELECT 'device表约束:' as info;
SELECT constraint_name, constraint_type 
FROM information_schema.table_constraints 
WHERE table_schema = 'kfchargingdbgc_schema' 
  AND table_name = 'device'
  AND constraint_name LIKE '%device_type%';

SELECT 'latest_firmware表约束:' as info;
SELECT constraint_name, constraint_type 
FROM information_schema.table_constraints 
WHERE table_schema = 'kfchargingdbgc_schema' 
  AND table_name = 'latest_firmware';

-- 显示迁移完成信息
SELECT '测试环境数据库迁移完成！' as result;
SELECT '请手动执行生产环境迁移脚本' as note;

-- =====================================================
-- 生产环境数据库迁移 (kafangcharging)
-- Schema: kafanglinlin_schema
-- =====================================================

/*
-- 生产环境迁移脚本（请手动执行）
-- 设置搜索路径到生产环境schema
SET search_path TO kafanglinlin_schema, public;

-- 检查当前表结构
SELECT 'firmware表当前结构:' as info;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns
WHERE table_schema = 'kafanglinlin_schema'
  AND table_name = 'firmware'
ORDER BY ordinal_position;

SELECT 'device表当前结构:' as info;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns
WHERE table_schema = 'kafanglinlin_schema'
  AND table_name = 'device'
ORDER BY ordinal_position;

-- 1. 为firmware表添加设备类型字段
ALTER TABLE kafanglinlin_schema.firmware
ADD COLUMN IF NOT EXISTS device_type INTEGER NOT NULL DEFAULT 50;

COMMENT ON COLUMN kafanglinlin_schema.firmware.device_type IS '设备类型：10=V2(旧版霍尔传感器版本，黑色PCB), 50=V5(新版BL0910 10通道版本), 51=V51(新版BL0939 2通道版本)';

ALTER TABLE kafanglinlin_schema.firmware
ADD CONSTRAINT chk_firmware_device_type
CHECK (device_type IN (10, 50, 51));

-- 2. 为device表添加设备类型字段
ALTER TABLE kafanglinlin_schema.device
ADD COLUMN IF NOT EXISTS device_type INTEGER DEFAULT NULL;

COMMENT ON COLUMN kafanglinlin_schema.device.device_type IS '设备类型：10=V2(旧版霍尔传感器版本，黑色PCB), 50=V5(新版BL0910 10通道版本), 51=V51(新版BL0939 2通道版本)';

ALTER TABLE kafanglinlin_schema.device
ADD CONSTRAINT chk_device_device_type
CHECK (device_type IS NULL OR device_type IN (10, 50, 51));

-- 3. 创建latest_firmware表
CREATE TABLE IF NOT EXISTS kafanglinlin_schema.latest_firmware (
    id SERIAL PRIMARY KEY,
    device_type INTEGER NOT NULL UNIQUE,
    firmware_id INTEGER NOT NULL REFERENCES kafanglinlin_schema.firmware(id) ON DELETE CASCADE,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(80) DEFAULT NULL,
    CONSTRAINT chk_latest_firmware_device_type CHECK (device_type IN (10, 50, 51))
);

COMMENT ON TABLE kafanglinlin_schema.latest_firmware IS '各设备类型最新固件管理表';
COMMENT ON COLUMN kafanglinlin_schema.latest_firmware.device_type IS '设备类型：10=V2, 50=V5, 51=V51';
COMMENT ON COLUMN kafanglinlin_schema.latest_firmware.firmware_id IS '关联的固件ID';
COMMENT ON COLUMN kafanglinlin_schema.latest_firmware.updated_at IS '更新时间';
COMMENT ON COLUMN kafanglinlin_schema.latest_firmware.updated_by IS '更新者';

CREATE INDEX IF NOT EXISTS idx_latest_firmware_device_type
ON kafanglinlin_schema.latest_firmware(device_type);

CREATE INDEX IF NOT EXISTS idx_latest_firmware_firmware_id
ON kafanglinlin_schema.latest_firmware(firmware_id);

-- 4. 创建更新触发器
CREATE OR REPLACE FUNCTION kafanglinlin_schema.update_latest_firmware_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_update_latest_firmware_timestamp
ON kafanglinlin_schema.latest_firmware;

CREATE TRIGGER trigger_update_latest_firmware_timestamp
    BEFORE UPDATE ON kafanglinlin_schema.latest_firmware
    FOR EACH ROW
    EXECUTE FUNCTION kafanglinlin_schema.update_latest_firmware_timestamp();

-- 验证迁移结果
SELECT 'firmware表迁移后结构:' as info;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns
WHERE table_schema = 'kafanglinlin_schema'
  AND table_name = 'firmware'
ORDER BY ordinal_position;

SELECT 'device表迁移后结构:' as info;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns
WHERE table_schema = 'kafanglinlin_schema'
  AND table_name = 'device'
ORDER BY ordinal_position;

SELECT 'latest_firmware表结构:' as info;
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns
WHERE table_schema = 'kafanglinlin_schema'
  AND table_name = 'latest_firmware'
ORDER BY ordinal_position;

SELECT '生产环境数据库迁移完成！' as result;
*/
