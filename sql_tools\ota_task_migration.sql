-- OTA任务表结构迁移脚本
-- 为ota_task表添加并行OTA升级所需的新字段
-- 执行前请确保已备份数据库

-- =====================================================
-- 测试环境数据库迁移 (kfchargingdbg)
-- Schema: kfchargingdbgc_schema
-- =====================================================

-- 设置搜索路径到测试环境schema
SET search_path TO kfchargingdbgc_schema, public;

-- 检查当前表结构
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_schema = 'kfchargingdbgc_schema' 
  AND table_name = 'ota_task' 
ORDER BY ordinal_position;

-- 添加新字段到ota_task表
-- 1. 详细状态字段
ALTER TABLE kfchargingdbgc_schema.ota_task 
ADD COLUMN IF NOT EXISTS detailed_status VARCHAR(50) DEFAULT '等待中';

-- 2. 阶段信息字段
ALTER TABLE kfchargingdbgc_schema.ota_task 
ADD COLUMN IF NOT EXISTS stage_info TEXT DEFAULT '';

-- 3. 重试次数字段
ALTER TABLE kfchargingdbgc_schema.ota_task 
ADD COLUMN IF NOT EXISTS retry_count INTEGER DEFAULT 0;

-- 4. 最大重试次数字段
ALTER TABLE kfchargingdbgc_schema.ota_task 
ADD COLUMN IF NOT EXISTS max_retries INTEGER DEFAULT 3;

-- 5. 开始时间字段
ALTER TABLE kfchargingdbgc_schema.ota_task 
ADD COLUMN IF NOT EXISTS started_at TIMESTAMP NULL;

-- 6. 完成时间字段
ALTER TABLE kfchargingdbgc_schema.ota_task 
ADD COLUMN IF NOT EXISTS completed_at TIMESTAMP NULL;

-- 为新字段添加注释
COMMENT ON COLUMN kfchargingdbgc_schema.ota_task.detailed_status IS '详细状态：等待中、初始化中、连接设备中等';
COMMENT ON COLUMN kfchargingdbgc_schema.ota_task.stage_info IS '当前阶段的详细信息';
COMMENT ON COLUMN kfchargingdbgc_schema.ota_task.retry_count IS '当前重试次数';
COMMENT ON COLUMN kfchargingdbgc_schema.ota_task.max_retries IS '最大允许重试次数';
COMMENT ON COLUMN kfchargingdbgc_schema.ota_task.started_at IS '任务开始执行时间';
COMMENT ON COLUMN kfchargingdbgc_schema.ota_task.completed_at IS '任务完成时间（成功或失败）';

-- 创建新字段的索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_ota_task_detailed_status 
ON kfchargingdbgc_schema.ota_task(detailed_status);

CREATE INDEX IF NOT EXISTS idx_ota_task_retry_count 
ON kfchargingdbgc_schema.ota_task(retry_count);

CREATE INDEX IF NOT EXISTS idx_ota_task_started_at 
ON kfchargingdbgc_schema.ota_task(started_at);

-- 更新现有记录的detailed_status字段，使其与status字段保持一致
UPDATE kfchargingdbgc_schema.ota_task 
SET detailed_status = status 
WHERE detailed_status = '等待中' AND status != '等待中';

-- 为已完成的任务设置完成时间（使用updated_at作为近似值）
UPDATE kfchargingdbgc_schema.ota_task 
SET completed_at = updated_at 
WHERE status IN ('成功', '失败') AND completed_at IS NULL;

-- 验证迁移结果
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN detailed_status IS NOT NULL THEN 1 END) as with_detailed_status,
    COUNT(CASE WHEN stage_info IS NOT NULL THEN 1 END) as with_stage_info,
    COUNT(CASE WHEN retry_count IS NOT NULL THEN 1 END) as with_retry_count,
    COUNT(CASE WHEN max_retries IS NOT NULL THEN 1 END) as with_max_retries,
    COUNT(CASE WHEN started_at IS NOT NULL THEN 1 END) as with_started_at,
    COUNT(CASE WHEN completed_at IS NOT NULL THEN 1 END) as with_completed_at
FROM kfchargingdbgc_schema.ota_task;

-- 显示更新后的表结构
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_schema = 'kfchargingdbgc_schema' 
  AND table_name = 'ota_task' 
ORDER BY ordinal_position;

-- =====================================================
-- 生产环境数据库迁移 (kafangcharging)
-- Schema: kafanglinlin_schema
-- 注意：请在测试环境验证成功后再执行此部分
-- =====================================================

/*
-- 取消注释以下代码来执行生产环境迁移

-- 设置搜索路径到生产环境schema
SET search_path TO kafanglinlin_schema, public;

-- 检查当前表结构
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_schema = 'kafanglinlin_schema' 
  AND table_name = 'ota_task' 
ORDER BY ordinal_position;

-- 添加新字段到ota_task表
ALTER TABLE kafanglinlin_schema.ota_task 
ADD COLUMN IF NOT EXISTS detailed_status VARCHAR(50) DEFAULT '等待中';

ALTER TABLE kafanglinlin_schema.ota_task 
ADD COLUMN IF NOT EXISTS stage_info TEXT DEFAULT '';

ALTER TABLE kafanglinlin_schema.ota_task 
ADD COLUMN IF NOT EXISTS retry_count INTEGER DEFAULT 0;

ALTER TABLE kafanglinlin_schema.ota_task 
ADD COLUMN IF NOT EXISTS max_retries INTEGER DEFAULT 3;

ALTER TABLE kafanglinlin_schema.ota_task 
ADD COLUMN IF NOT EXISTS started_at TIMESTAMP NULL;

ALTER TABLE kafanglinlin_schema.ota_task 
ADD COLUMN IF NOT EXISTS completed_at TIMESTAMP NULL;

-- 为新字段添加注释
COMMENT ON COLUMN kafanglinlin_schema.ota_task.detailed_status IS '详细状态：等待中、初始化中、连接设备中等';
COMMENT ON COLUMN kafanglinlin_schema.ota_task.stage_info IS '当前阶段的详细信息';
COMMENT ON COLUMN kafanglinlin_schema.ota_task.retry_count IS '当前重试次数';
COMMENT ON COLUMN kafanglinlin_schema.ota_task.max_retries IS '最大允许重试次数';
COMMENT ON COLUMN kafanglinlin_schema.ota_task.started_at IS '任务开始执行时间';
COMMENT ON COLUMN kafanglinlin_schema.ota_task.completed_at IS '任务完成时间（成功或失败）';

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_ota_task_detailed_status 
ON kafanglinlin_schema.ota_task(detailed_status);

CREATE INDEX IF NOT EXISTS idx_ota_task_retry_count 
ON kafanglinlin_schema.ota_task(retry_count);

CREATE INDEX IF NOT EXISTS idx_ota_task_started_at 
ON kafanglinlin_schema.ota_task(started_at);

-- 更新现有记录
UPDATE kafanglinlin_schema.ota_task 
SET detailed_status = status 
WHERE detailed_status = '等待中' AND status != '等待中';

UPDATE kafanglinlin_schema.ota_task 
SET completed_at = updated_at 
WHERE status IN ('成功', '失败') AND completed_at IS NULL;

-- 验证迁移结果
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN detailed_status IS NOT NULL THEN 1 END) as with_detailed_status,
    COUNT(CASE WHEN stage_info IS NOT NULL THEN 1 END) as with_stage_info,
    COUNT(CASE WHEN retry_count IS NOT NULL THEN 1 END) as with_retry_count,
    COUNT(CASE WHEN max_retries IS NOT NULL THEN 1 END) as with_max_retries,
    COUNT(CASE WHEN started_at IS NOT NULL THEN 1 END) as with_started_at,
    COUNT(CASE WHEN completed_at IS NOT NULL THEN 1 END) as with_completed_at
FROM kafanglinlin_schema.ota_task;

-- 显示更新后的表结构
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_schema = 'kafanglinlin_schema' 
  AND table_name = 'ota_task' 
ORDER BY ordinal_position;
*/

-- =====================================================
-- 迁移完成检查
-- =====================================================

-- 检查是否所有必需的字段都已添加
DO $$
DECLARE
    missing_columns TEXT[] := ARRAY[]::TEXT[];
    schema_name TEXT;
BEGIN
    -- 检查测试环境
    schema_name := 'kfchargingdbgc_schema';
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = schema_name AND table_name = 'ota_task' AND column_name = 'detailed_status'
    ) THEN
        missing_columns := array_append(missing_columns, schema_name || '.detailed_status');
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = schema_name AND table_name = 'ota_task' AND column_name = 'stage_info'
    ) THEN
        missing_columns := array_append(missing_columns, schema_name || '.stage_info');
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = schema_name AND table_name = 'ota_task' AND column_name = 'retry_count'
    ) THEN
        missing_columns := array_append(missing_columns, schema_name || '.retry_count');
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = schema_name AND table_name = 'ota_task' AND column_name = 'max_retries'
    ) THEN
        missing_columns := array_append(missing_columns, schema_name || '.max_retries');
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = schema_name AND table_name = 'ota_task' AND column_name = 'started_at'
    ) THEN
        missing_columns := array_append(missing_columns, schema_name || '.started_at');
    END IF;
    
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = schema_name AND table_name = 'ota_task' AND column_name = 'completed_at'
    ) THEN
        missing_columns := array_append(missing_columns, schema_name || '.completed_at');
    END IF;
    
    IF array_length(missing_columns, 1) > 0 THEN
        RAISE NOTICE '警告：以下字段缺失: %', array_to_string(missing_columns, ', ');
    ELSE
        RAISE NOTICE '✓ 测试环境所有必需字段已成功添加';
    END IF;
END $$;
