-- 生产环境设备表合并脚本模板
-- 生成时间: 2025-09-20 10:51:30
-- 目标: 将device、device_locations、device_parameter、debug_script合并为统一的device表
-- 
-- 使用说明:
-- 1. 请根据实际的表结构调整字段名称和数据类型
-- 2. 在执行前请先备份所有相关数据
-- 3. 建议先在测试环境验证脚本的正确性
-- 4. 执行时请在业务低峰期进行

-- 设置schema (根据实际情况修改)
-- SET search_path TO your_schema_name;

-- 开始事务
BEGIN;

-- ============================================================
-- 步骤1: 创建备份表
-- ============================================================
SELECT 'Step 1: Creating backup tables...' as status;

-- 备份原有表 (请根据实际表名调整)
CREATE TABLE device_backup_production AS SELECT * FROM device;
CREATE TABLE device_locations_backup_production AS SELECT * FROM device_locations;
CREATE TABLE device_parameter_backup_production AS SELECT * FROM device_parameter;
CREATE TABLE debug_script_backup_production AS SELECT * FROM debug_script;

SELECT 'Backup tables created successfully' as status;

-- ============================================================
-- 步骤2: 创建新的统一设备表
-- ============================================================
SELECT 'Step 2: Creating unified device table...' as status;

CREATE TABLE device_new (
    id SERIAL PRIMARY KEY,
    
    -- ========================================
    -- 原device表字段 (请根据实际字段调整)
    -- ========================================
    device_id VARCHAR(50) UNIQUE NOT NULL,
    device_name VARCHAR(100),
    product_key VARCHAR(100),
    device_secret VARCHAR(200),
    status VARCHAR(20) DEFAULT 'offline',
    firmware_version VARCHAR(50),
    last_upgrade_time TIMESTAMP,
    upgrade_status VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- ========================================
    -- 原device_locations表字段
    -- ========================================
    location_name VARCHAR(100),
    address TEXT,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    installation_date DATE,
    location_notes TEXT,
    
    -- ========================================
    -- 设备参数字段 (使用PostgreSQL数组存储)
    -- ========================================
    register_values INTEGER[],  -- 寄存器值数组，最大300个元素
    
    -- ========================================
    -- 调试脚本字段
    -- ========================================
    debug_script_enabled BOOLEAN DEFAULT false,
    debug_script_content TEXT,
    debug_script_last_run TIMESTAMP,
    debug_script_result TEXT,
    
    -- ========================================
    -- 额外字段
    -- ========================================
    device_notes TEXT,
    
    -- 索引
    CONSTRAINT unique_device_id UNIQUE (device_id)
);

-- 创建索引
CREATE INDEX idx_device_new_device_id ON device_new(device_id);
CREATE INDEX idx_device_new_status ON device_new(status);
CREATE INDEX idx_device_new_location ON device_new(location_name);

SELECT 'Unified device table created' as status;

-- ============================================================
-- 步骤3: 创建寄存器配置表
-- ============================================================
SELECT 'Step 3: Creating register configuration table...' as status;

CREATE TABLE register_config (
    id SERIAL PRIMARY KEY,
    register_address INTEGER NOT NULL,
    register_name VARCHAR(100) NOT NULL,
    register_alias VARCHAR(100),
    description TEXT,
    data_type VARCHAR(20) DEFAULT 'integer',
    category VARCHAR(50) DEFAULT 'other',
    unit VARCHAR(20),
    min_value INTEGER,
    max_value INTEGER,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT unique_register_address UNIQUE(register_address)
);

-- 创建索引
CREATE INDEX idx_register_config_address ON register_config(register_address);
CREATE INDEX idx_register_config_category ON register_config(category);
CREATE INDEX idx_register_config_active ON register_config(is_active);

SELECT 'Register configuration table created' as status;

-- ============================================================
-- 步骤4: 迁移数据到新表
-- ============================================================
SELECT 'Step 4: Migrating data to unified table...' as status;

-- 插入基础设备数据，合并location信息
-- 注意: 请根据实际的表结构和字段名称调整以下SQL
INSERT INTO device_new (
    device_id, device_name, product_key, device_secret, status, 
    firmware_version, last_upgrade_time, upgrade_status, 
    created_at, updated_at,
    location_name, address, latitude, longitude, installation_date, location_notes,
    register_values, debug_script_enabled, debug_script_content,
    debug_script_last_run, debug_script_result, device_notes
)
SELECT 
    -- 设备基本信息
    d.device_id,
    d.device_name,
    d.product_key,
    d.device_secret,
    COALESCE(d.status, 'offline') as status,
    d.firmware_version,
    d.last_upgrade_time,
    d.upgrade_status,
    COALESCE(d.created_at, CURRENT_TIMESTAMP) as created_at,
    COALESCE(d.updated_at, CURRENT_TIMESTAMP) as updated_at,
    
    -- 位置信息 (LEFT JOIN device_locations)
    dl.location_name,
    dl.address,
    dl.latitude,
    dl.longitude,
    dl.installation_date,
    dl.notes as location_notes,
    
    -- 参数数组 (从device_parameter表聚合)
    -- 注意: 这里假设device_parameter表有parameter_address和parameter_value字段
    COALESCE(
        (SELECT array_agg(parameter_value ORDER BY parameter_address) 
         FROM device_parameter dp 
         WHERE dp.device_id = d.device_id), 
        ARRAY[]::INTEGER[]
    ) as register_values,
    
    -- 调试脚本信息 (LEFT JOIN debug_script)
    COALESCE(ds.enabled, false) as debug_script_enabled,
    ds.script_content,
    ds.last_run_time,
    ds.last_result,
    
    -- 设备备注 (可以从多个来源合并)
    COALESCE(
        CASE 
            WHEN d.notes IS NOT NULL AND dl.notes IS NOT NULL THEN 
                d.notes || '; ' || dl.notes
            WHEN d.notes IS NOT NULL THEN d.notes
            WHEN dl.notes IS NOT NULL THEN dl.notes
            ELSE ''
        END, 
        ''
    ) as device_notes
    
FROM device d
LEFT JOIN device_locations dl ON d.device_id = dl.device_id
LEFT JOIN debug_script ds ON d.device_id = ds.device_id;

SELECT 'Data migration completed' as status;

-- ============================================================
-- 步骤5: 创建寄存器配置数据
-- ============================================================
SELECT 'Step 5: Creating register configuration data...' as status;

-- 插入常用的寄存器配置 (根据实际业务需求调整)
INSERT INTO register_config (register_address, register_name, register_alias, description, category, data_type, unit) VALUES
-- 时间参数
(0, 'REG_T1', 'T1时段', '第一时段时间设置', 'time', 'integer', '分钟'),
(1, 'REG_T2', 'T2时段', '第二时段时间设置', 'time', 'integer', '分钟'),
(2, 'REG_T3', 'T3时段', '第三时段时间设置', 'time', 'integer', '分钟'),
(3, 'REG_T4', 'T4时段', '第四时段时间设置', 'time', 'integer', '分钟'),
(4, 'REG_T5', 'T5时段', '第五时段时间设置', 'time', 'integer', '分钟'),
(5, 'REG_T6', 'T6时段', '第六时段时间设置', 'time', 'integer', '分钟'),

-- 功率参数
(10, 'REG_P1', 'P1功率', '第一档功率设置', 'power', 'integer', 'W'),
(11, 'REG_P2', 'P2功率', '第二档功率设置', 'power', 'integer', 'W'),
(12, 'REG_P3', 'P3功率', '第三档功率设置', 'power', 'integer', 'W'),
(13, 'REG_P4', 'P4功率', '第四档功率设置', 'power', 'integer', 'W'),
(14, 'REG_P5', 'P5功率', '第五档功率设置', 'power', 'integer', 'W'),
(15, 'REG_P6', 'P6功率', '第六档功率设置', 'power', 'integer', 'W'),

-- 温度参数
(20, 'REG_TEMP_MAX', '最高温度', '设备最高工作温度', 'other', 'integer', '°C'),
(21, 'REG_TEMP_MIN', '最低温度', '设备最低工作温度', 'other', 'integer', '°C'),
(22, 'REG_TEMP_ALARM', '温度报警', '温度报警阈值', 'other', 'integer', '°C'),

-- 电气参数
(30, 'REG_VOLTAGE', '工作电压', '设备工作电压设置', 'other', 'integer', 'V'),
(31, 'REG_CURRENT', '工作电流', '设备工作电流设置', 'other', 'integer', 'A'),
(32, 'REG_FREQUENCY', '工作频率', '设备工作频率设置', 'other', 'integer', 'Hz'),

-- 控制参数
(40, 'REG_MODE', '工作模式', '设备工作模式设置', 'other', 'integer', ''),
(41, 'REG_AUTO_START', '自动启动', '自动启动功能开关', 'other', 'boolean', ''),
(42, 'REG_PROTECTION', '保护功能', '设备保护功能设置', 'other', 'integer', '');

SELECT 'Register configuration data inserted' as status;

-- ============================================================
-- 步骤6: 验证数据完整性
-- ============================================================
SELECT 'Step 6: Verifying data integrity...' as status;

-- 检查记录数量
SELECT 'Original device count:' as info, COUNT(*) as count FROM device;
SELECT 'Original device_locations count:' as info, COUNT(*) as count FROM device_locations;
SELECT 'Original device_parameter count:' as info, COUNT(*) as count FROM device_parameter;
SELECT 'Original debug_script count:' as info, COUNT(*) as count FROM debug_script;

SELECT 'New device count:' as info, COUNT(*) as count FROM device_new;
SELECT 'Register config count:' as info, COUNT(*) as count FROM register_config;

-- 检查数据完整性
SELECT 'Devices with parameters:' as info, COUNT(*) as count 
FROM device_new 
WHERE register_values IS NOT NULL AND array_length(register_values, 1) > 0;

SELECT 'Devices with locations:' as info, COUNT(*) as count 
FROM device_new 
WHERE location_name IS NOT NULL;

SELECT 'Devices with debug scripts:' as info, COUNT(*) as count 
FROM device_new 
WHERE debug_script_enabled = true;

-- 检查寄存器配置分布
SELECT 'Register config by category:' as info;
SELECT category, COUNT(*) as count 
FROM register_config 
GROUP BY category 
ORDER BY category;

-- ============================================================
-- 步骤7: 重命名表 (谨慎操作)
-- ============================================================
SELECT 'Step 7: Table renaming (COMMENTED OUT FOR SAFETY)...' as status;

-- ⚠️ 警告: 以下操作会删除原表，请谨慎执行！
-- 建议先在测试环境验证无误后再执行
-- 执行前请确保：
-- 1. 数据已完整迁移
-- 2. 应用程序已停止
-- 3. 已做好完整备份

/*
-- 删除原表 (请谨慎执行)
DROP TABLE IF EXISTS device CASCADE;
DROP TABLE IF EXISTS device_locations CASCADE;
DROP TABLE IF EXISTS device_parameter CASCADE;
DROP TABLE IF EXISTS debug_script CASCADE;

-- 重命名新表
ALTER TABLE device_new RENAME TO device;
ALTER SEQUENCE device_new_id_seq RENAME TO device_id_seq;
ALTER SEQUENCE device_id_seq OWNED BY device.id;

-- 重新创建可能需要的视图或函数
-- (根据实际需求添加)

SELECT 'Tables renamed successfully' as status;
*/

SELECT 'Migration script completed (table renaming commented out for safety)' as status;

-- ============================================================
-- 提交或回滚事务
-- ============================================================

-- 如果一切正常，取消注释下面的COMMIT
-- COMMIT;

-- 如果需要回滚，执行:
-- ROLLBACK;

-- ============================================================
-- 最终状态检查
-- ============================================================
SELECT 'Migration completed successfully!' as final_status;
SELECT 'IMPORTANT: Please review all data before dropping original tables!' as warning;
SELECT 'NEXT STEPS:' as next_steps;
SELECT '1. Verify data integrity in device_new table' as step1;
SELECT '2. Test application with new table structure' as step2;
SELECT '3. Update application code to use device instead of device_new' as step3;
SELECT '4. Uncomment table renaming section when ready' as step4;
SELECT '5. Drop backup tables after confirming everything works' as step5;
