/* 
 * Back to Top Component - Rocket Style
 * 回到顶部组件 - 火箭样式
 * 与Liquid Glass主题保持一致
 */

/* 回到顶部按钮容器 */
.back-to-top {
    position: fixed;
    bottom: 30px;
    left: 30px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px) scale(0.8);
    transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    cursor: pointer;
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

/* 火箭按钮样式 */
.rocket-button {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, 
        rgba(255, 107, 107, 0.9) 0%, 
        rgba(255, 142, 83, 0.9) 50%,
        rgba(255, 193, 7, 0.9) 100%);
    border: none;
    box-shadow: 
        0 8px 32px rgba(255, 107, 107, 0.3),
        0 4px 16px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(20px);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    transition: all 0.15s ease-out;
}

.rocket-button:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 
        0 12px 40px rgba(255, 107, 107, 0.4),
        0 6px 20px rgba(0, 0, 0, 0.15);
    background: linear-gradient(135deg, 
        rgba(255, 107, 107, 1) 0%, 
        rgba(255, 142, 83, 1) 50%,
        rgba(255, 193, 7, 1) 100%);
}

.rocket-button:active {
    transform: translateY(-1px) scale(0.98);
}

/* 火箭图标 */
.rocket-icon {
    font-size: 24px;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    transition: all 0.15s ease-out;
    position: relative;
    z-index: 2;
}

.rocket-button:hover .rocket-icon {
    transform: translateY(-2px);
    animation: rocket-shake 0.4s ease-in-out;
}

/* 火箭抖动动画 */
@keyframes rocket-shake {
    0%, 100% { transform: translateY(-2px) rotate(0deg); }
    25% { transform: translateY(-3px) rotate(-2deg); }
    75% { transform: translateY(-1px) rotate(2deg); }
}

/* 火箭尾焰效果 */
.rocket-button::before {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 15px solid rgba(255, 193, 7, 0.8);
    opacity: 0;
    transition: all 0.15s ease-out;
    z-index: 1;
}

.rocket-button:hover::before {
    opacity: 1;
    animation: flame-flicker 0.3s ease-in-out infinite alternate;
}

/* 尾焰闪烁动画 */
@keyframes flame-flicker {
    0% { 
        border-top-color: rgba(255, 193, 7, 0.8);
        transform: translateX(-50%) scale(1);
    }
    100% { 
        border-top-color: rgba(255, 107, 107, 0.8);
        transform: translateX(-50%) scale(1.1);
    }
}

/* 粒子效果容器 */
.rocket-particles {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
    border-radius: 50%;
}

/* 粒子 */
.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    opacity: 0;
}

/* 点击时的粒子爆炸效果 */
.rocket-button.launching .particle {
    animation: particle-explosion 0.5s ease-out forwards;
}

@keyframes particle-explosion {
    0% {
        opacity: 1;
        transform: translate(0, 0) scale(1);
    }
    100% {
        opacity: 0;
        transform: translate(var(--dx), var(--dy)) scale(0);
    }
}

/* 深色模式适配 */
body.dark-mode .rocket-button {
    background: linear-gradient(135deg, 
        rgba(138, 43, 226, 0.9) 0%, 
        rgba(30, 144, 255, 0.9) 50%,
        rgba(0, 191, 255, 0.9) 100%);
    box-shadow: 
        0 8px 32px rgba(138, 43, 226, 0.3),
        0 4px 16px rgba(0, 0, 0, 0.2);
}

body.dark-mode .rocket-button:hover {
    background: linear-gradient(135deg, 
        rgba(138, 43, 226, 1) 0%, 
        rgba(30, 144, 255, 1) 50%,
        rgba(0, 191, 255, 1) 100%);
    box-shadow: 
        0 12px 40px rgba(138, 43, 226, 0.4),
        0 6px 20px rgba(0, 0, 0, 0.3);
}

body.dark-mode .rocket-button::before {
    border-top-color: rgba(0, 191, 255, 0.8);
}

body.dark-mode .rocket-button:hover::before {
    animation: flame-flicker-dark 0.5s ease-in-out infinite alternate;
}

@keyframes flame-flicker-dark {
    0% { 
        border-top-color: rgba(0, 191, 255, 0.8);
        transform: translateX(-50%) scale(1);
    }
    100% { 
        border-top-color: rgba(138, 43, 226, 0.8);
        transform: translateX(-50%) scale(1.1);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .back-to-top {
        bottom: 20px;
        left: 20px;
    }

    .rocket-button {
        width: 50px;
        height: 50px;
    }

    .rocket-icon {
        font-size: 20px;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .rocket-button {
        border: 2px solid #000;
    }
    
    body.dark-mode .rocket-button {
        border: 2px solid #fff;
    }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
    .back-to-top,
    .rocket-button,
    .rocket-icon,
    .rocket-button::before,
    .particle {
        transition: none;
        animation: none;
    }
    
    .rocket-button:hover {
        transform: none;
    }
}
