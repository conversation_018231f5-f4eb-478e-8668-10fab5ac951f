/* 3D模型查看器页面样式 */

body {
    font-family: 'Arial', sans-serif;
    background-color: #f8f9fa;
}

.container {
    margin-top: 50px;
}

.card {
    border: none;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.card-title {
    font-size: 1.25rem;
    margin-bottom: 1rem;
}

.form-control {
    border-radius: 0.25rem;
}

.btn-primary {
    background-color: #007bff;
    border: none;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.list-group-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1.25rem;
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.list-group-item:hover {
    background-color: #f8f9fa;
    cursor: pointer;
}

#modelViewer {
    background-color: #e9ecef;
    border-radius: 0.25rem;
    width: 100%;
    height: 400px;
}

#uploadStatus {
    font-size: 0.9rem;
    margin-top: 1rem;
}
