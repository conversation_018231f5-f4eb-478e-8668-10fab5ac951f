/* 
 * Liquid Glass Components
 * Specific adaptations for existing components in the OTA system
 */

/* ===== Device Status Components ===== */
body.liquid-glass-theme .device-card {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: 18px;
    box-shadow: var(--glass-shadow);
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

body.liquid-glass-theme .device-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, 
        transparent, 
        rgba(102, 126, 234, 0.6), 
        rgba(118, 75, 162, 0.6), 
        transparent);
}

body.liquid-glass-theme .device-card:hover {
    background: var(--glass-bg-light);
    box-shadow: var(--glass-shadow-hover);
    transform: translateY(-5px) scale(1.02);
}

/* Device Status Badges */
body.liquid-glass-theme .badge.bg-success-subtle {
    background: var(--success-glass) !important;
    color: rgba(255, 255, 255, 0.9) !important;
    border: 1px solid rgba(40, 167, 69, 0.3);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
}

body.liquid-glass-theme .badge.bg-danger-subtle {
    background: var(--danger-glass) !important;
    color: rgba(255, 255, 255, 0.9) !important;
    border: 1px solid rgba(220, 53, 69, 0.3);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
}

body.liquid-glass-theme .badge.text-success {
    color: rgba(40, 167, 69, 1) !important;
    text-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
}

body.liquid-glass-theme .badge.text-danger {
    color: rgba(220, 53, 69, 1) !important;
    text-shadow: 0 0 10px rgba(220, 53, 69, 0.5);
}

/* ===== OTA Task Components ===== */
body.liquid-glass-theme .progress {
    background: var(--glass-bg-dark);
    border: 1px solid var(--glass-border);
    border-radius: 25px;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
}

body.liquid-glass-theme .progress-bar {
    background: linear-gradient(45deg, 
        rgba(102, 126, 234, 0.9), 
        rgba(118, 75, 162, 0.9));
    border-radius: 25px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
}

body.liquid-glass-theme .progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent, 
        rgba(255, 255, 255, 0.3), 
        transparent);
    animation: progressShine 2s infinite;
}

/* ===== Firmware Management ===== */
body.liquid-glass-theme .firmware-card {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: 18px;
    box-shadow: var(--glass-shadow);
    transition: var(--transition-smooth);
}

body.liquid-glass-theme .firmware-card:hover {
    background: var(--glass-bg-light);
    box-shadow: var(--glass-shadow-hover);
    transform: translateY(-3px);
}

/* ===== Login/Register Forms ===== */
body.liquid-glass-theme .login-container,
body.liquid-glass-theme .register-container {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur-strong);
    -webkit-backdrop-filter: var(--glass-blur-strong);
    border: 1px solid var(--glass-border);
    border-radius: 25px;
    box-shadow: var(--glass-shadow-hover);
    padding: 40px;
    position: relative;
    overflow: hidden;
}

body.liquid-glass-theme .login-container::before,
body.liquid-glass-theme .register-container::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, 
        rgba(102, 126, 234, 0.05), 
        rgba(118, 75, 162, 0.05), 
        rgba(255, 119, 198, 0.05), 
        rgba(120, 219, 255, 0.05));
    animation: rotate 30s linear infinite;
    z-index: -1;
}

/* ===== Navigation Enhancements ===== */
body.liquid-glass-theme .navbar-toggler {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: 10px;
    padding: 8px;
    transition: var(--transition-smooth);
}

body.liquid-glass-theme .navbar-toggler:hover {
    background: var(--glass-bg-light);
    transform: scale(1.05);
}

body.liquid-glass-theme .navbar-toggler-icon {
    filter: invert(1) brightness(0.9);
}

/* ===== Sidebar (if exists) ===== */
body.liquid-glass-theme .sidebar {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur-strong);
    -webkit-backdrop-filter: var(--glass-blur-strong);
    border-right: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
}

body.liquid-glass-theme .sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    margin: 4px 8px;
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

body.liquid-glass-theme .sidebar .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent, 
        rgba(255, 255, 255, 0.1), 
        transparent);
    transition: left 0.3s ease;
}

body.liquid-glass-theme .sidebar .nav-link:hover::before {
    left: 100%;
}

body.liquid-glass-theme .sidebar .nav-link:hover {
    background: var(--glass-bg-light);
    color: rgba(255, 255, 255, 1);
    transform: translateX(5px);
}

body.liquid-glass-theme .sidebar .nav-link.active {
    background: var(--primary-glass);
    color: rgba(255, 255, 255, 1);
}

/* ===== Breadcrumb ===== */
body.liquid-glass-theme .breadcrumb {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: 15px;
    padding: 12px 20px;
    box-shadow: var(--glass-shadow);
}

body.liquid-glass-theme .breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition-smooth);
}

body.liquid-glass-theme .breadcrumb-item a:hover {
    color: rgba(255, 255, 255, 1);
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

body.liquid-glass-theme .breadcrumb-item.active {
    color: rgba(255, 255, 255, 0.9);
}

/* ===== Search Components ===== */
body.liquid-glass-theme .search-container {
    position: relative;
}

body.liquid-glass-theme .search-container .form-control {
    padding-right: 45px;
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    color: rgba(255, 255, 255, 0.9);
}

body.liquid-glass-theme .search-container .search-btn {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    padding: 8px;
    border-radius: 8px;
    transition: var(--transition-smooth);
}

body.liquid-glass-theme .search-container .search-btn:hover {
    background: var(--glass-bg-light);
    color: rgba(255, 255, 255, 1);
}

/* ===== Tooltip Enhancements ===== */
body.liquid-glass-theme .tooltip .tooltip-inner {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    color: rgba(255, 255, 255, 0.9);
    border-radius: 10px;
    box-shadow: var(--glass-shadow);
}

body.liquid-glass-theme .tooltip .tooltip-arrow::before {
    border-top-color: var(--glass-border);
}

/* ===== Popover Enhancements ===== */
body.liquid-glass-theme .popover {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur-strong);
    -webkit-backdrop-filter: var(--glass-blur-strong);
    border: 1px solid var(--glass-border);
    border-radius: 15px;
    box-shadow: var(--glass-shadow-hover);
}

body.liquid-glass-theme .popover-header {
    background: var(--glass-bg-light);
    border-bottom: 1px solid var(--glass-border);
    color: rgba(255, 255, 255, 0.9);
}

body.liquid-glass-theme .popover-body {
    color: rgba(255, 255, 255, 0.8);
}

/* ===== Tab Components ===== */
body.liquid-glass-theme .nav-tabs {
    border-bottom: 1px solid var(--glass-border);
}

body.liquid-glass-theme .nav-tabs .nav-link {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-bottom: none;
    color: rgba(255, 255, 255, 0.8);
    border-radius: 15px 15px 0 0;
    margin-right: 4px;
    transition: var(--transition-smooth);
}

body.liquid-glass-theme .nav-tabs .nav-link:hover {
    background: var(--glass-bg-light);
    color: rgba(255, 255, 255, 1);
}

body.liquid-glass-theme .nav-tabs .nav-link.active {
    background: var(--glass-bg-light);
    border-color: var(--glass-border);
    color: rgba(255, 255, 255, 1);
    box-shadow: var(--glass-shadow);
}

body.liquid-glass-theme .tab-content {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-top: none;
    border-radius: 0 15px 15px 15px;
    padding: 20px;
    box-shadow: var(--glass-shadow);
}

/* ===== Accordion Components ===== */
body.liquid-glass-theme .accordion-item {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: 15px;
    margin-bottom: 10px;
    box-shadow: var(--glass-shadow);
}

body.liquid-glass-theme .accordion-header .accordion-button {
    background: transparent;
    color: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 15px;
    transition: var(--transition-smooth);
}

body.liquid-glass-theme .accordion-header .accordion-button:hover {
    background: var(--glass-bg-light);
}

body.liquid-glass-theme .accordion-header .accordion-button:not(.collapsed) {
    background: var(--glass-bg-light);
    box-shadow: none;
}

body.liquid-glass-theme .accordion-body {
    background: transparent;
    color: rgba(255, 255, 255, 0.8);
    border-top: 1px solid var(--glass-border);
}
