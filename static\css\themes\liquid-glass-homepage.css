/* 
 * Liquid Glass Homepage Enhancements
 * Special effects for the homepage when using liquid glass theme
 */

/* ===== Hero Section ===== */
body.liquid-glass-theme .hero-section {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur-strong);
    -webkit-backdrop-filter: var(--glass-blur-strong);
    border: 1px solid var(--glass-border);
    border-radius: 25px;
    box-shadow: var(--glass-shadow-hover);
    position: relative;
    overflow: hidden;
    margin-bottom: 40px;
}

body.liquid-glass-theme .hero-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, 
        rgba(102, 126, 234, 0.1), 
        rgba(118, 75, 162, 0.1), 
        rgba(255, 119, 198, 0.1), 
        rgba(120, 219, 255, 0.1), 
        rgba(102, 126, 234, 0.1));
    animation: rotate 20s linear infinite;
    z-index: -1;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

body.liquid-glass-theme .hero-section .display-4 {
    color: rgba(255, 255, 255, 0.95);
    text-shadow: 0 2px 20px rgba(0, 0, 0, 0.3);
    font-weight: 700;
    background: linear-gradient(45deg, #fff, #f0f0f0, #fff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: textShine 3s ease-in-out infinite;
}

@keyframes textShine {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

body.liquid-glass-theme .hero-section .lead {
    color: rgba(255, 255, 255, 0.8);
    text-shadow: 0 1px 10px rgba(0, 0, 0, 0.2);
}

/* ===== Stat Cards ===== */
body.liquid-glass-theme .stat-card {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    box-shadow: var(--glass-shadow);
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

body.liquid-glass-theme .stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, 
        transparent, 
        rgba(102, 126, 234, 0.8), 
        rgba(118, 75, 162, 0.8), 
        transparent);
}

body.liquid-glass-theme .stat-card:hover {
    background: var(--glass-bg-light);
    box-shadow: var(--glass-shadow-hover);
    transform: translateY(-10px) scale(1.02);
}

body.liquid-glass-theme .stat-card .card-header {
    background: transparent;
    border-bottom: 1px solid var(--glass-border);
}

body.liquid-glass-theme .stat-card .stat-icon {
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
    animation: iconFloat 4s ease-in-out infinite;
}

@keyframes iconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-5px); }
}

body.liquid-glass-theme .stat-card .stat-number {
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    position: relative;
}

body.liquid-glass-theme .stat-card .stat-number::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 2px;
    background: linear-gradient(90deg, transparent, currentColor, transparent);
    opacity: 0.5;
}

/* ===== System Health Card ===== */
body.liquid-glass-theme .system-health {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur-strong);
    -webkit-backdrop-filter: var(--glass-blur-strong);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    box-shadow: var(--glass-shadow-hover);
    position: relative;
    overflow: hidden;
}

body.liquid-glass-theme .system-health::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, 
        rgba(79, 172, 254, 0.1) 0%, 
        transparent 70%);
    animation: pulse 4s ease-in-out infinite;
}

body.liquid-glass-theme .system-health .health-score {
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 1;
}

/* ===== Quick Action Cards ===== */
body.liquid-glass-theme .quick-action-card {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: 18px;
    box-shadow: var(--glass-shadow);
    transition: var(--transition-bounce);
    position: relative;
    overflow: hidden;
    color: rgba(255, 255, 255, 0.9) !important;
    text-decoration: none !important;
}

body.liquid-glass-theme .quick-action-card::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    transition: all 0.6s ease;
    transform: translate(-50%, -50%);
    border-radius: 50%;
}

body.liquid-glass-theme .quick-action-card:hover::before {
    width: 200px;
    height: 200px;
}

body.liquid-glass-theme .quick-action-card:hover {
    background: var(--glass-bg-light);
    box-shadow: var(--glass-shadow-hover);
    transform: translateY(-8px) scale(1.05);
    color: rgba(255, 255, 255, 1) !important;
}

body.liquid-glass-theme .quick-action-card .quick-action-icon {
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
    transition: var(--transition-smooth);
}

body.liquid-glass-theme .quick-action-card:hover .quick-action-icon {
    transform: scale(1.1) rotate(5deg);
}

/* ===== Recent Activity ===== */
body.liquid-glass-theme .recent-activity {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    box-shadow: var(--glass-shadow);
    color: rgba(255, 255, 255, 0.9);
}

body.liquid-glass-theme .activity-item {
    background: var(--glass-bg-light);
    border-left: 4px solid rgba(102, 126, 234, 0.8);
    border-radius: 12px;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

body.liquid-glass-theme .activity-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.05), transparent);
    transition: left 0.5s ease;
}

body.liquid-glass-theme .activity-item:hover::before {
    left: 100%;
}

body.liquid-glass-theme .activity-item:hover {
    background: var(--glass-bg-light);
    transform: translateX(5px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

body.liquid-glass-theme .activity-time {
    color: rgba(255, 255, 255, 0.6);
}

/* ===== Loading Animations ===== */
body.liquid-glass-theme .loading-spinner {
    border: 3px solid var(--glass-bg);
    border-top: 3px solid rgba(102, 126, 234, 0.8);
    border-radius: 50%;
    animation: liquidSpin 1.5s ease-in-out infinite;
}

@keyframes liquidSpin {
    0% { 
        transform: rotate(0deg); 
        border-top-color: rgba(102, 126, 234, 0.8);
    }
    25% { 
        border-top-color: rgba(118, 75, 162, 0.8);
    }
    50% { 
        transform: rotate(180deg); 
        border-top-color: rgba(255, 119, 198, 0.8);
    }
    75% { 
        border-top-color: rgba(120, 219, 255, 0.8);
    }
    100% { 
        transform: rotate(360deg); 
        border-top-color: rgba(102, 126, 234, 0.8);
    }
}

/* ===== Floating Particles Effect ===== */
body.liquid-glass-theme::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(2px 2px at 20px 30px, rgba(255, 255, 255, 0.1), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255, 255, 255, 0.1), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(255, 255, 255, 0.1), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255, 255, 255, 0.1), transparent),
        radial-gradient(2px 2px at 160px 30px, rgba(255, 255, 255, 0.1), transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: particleFloat 20s linear infinite;
    pointer-events: none;
    z-index: -1;
}

@keyframes particleFloat {
    0% { transform: translateY(0px); }
    100% { transform: translateY(-100px); }
}

/* ===== Responsive Adjustments ===== */
@media (max-width: 768px) {
    body.liquid-glass-theme .hero-section {
        border-radius: 20px;
        padding: 30px 20px;
    }
    
    body.liquid-glass-theme .stat-card {
        border-radius: 15px;
    }
    
    body.liquid-glass-theme .quick-action-card {
        border-radius: 15px;
    }
    
    body.liquid-glass-theme .recent-activity {
        border-radius: 15px;
    }
}

@media (max-width: 576px) {
    body.liquid-glass-theme .hero-section {
        border-radius: 15px;
        padding: 25px 15px;
    }
    
    body.liquid-glass-theme .stat-card,
    body.liquid-glass-theme .quick-action-card,
    body.liquid-glass-theme .recent-activity {
        border-radius: 12px;
    }
    
    /* Reduce particle effects on small screens */
    body.liquid-glass-theme::after {
        opacity: 0.5;
    }
}
