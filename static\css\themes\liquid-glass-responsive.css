/* 
 * Liquid Glass Responsive Design
 * Optimizations for different screen sizes and devices
 */

/* ===== Performance Optimizations ===== */
@media (prefers-reduced-motion: reduce) {
    body.liquid-glass-theme * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    body.liquid-glass-theme::before,
    body.liquid-glass-theme::after {
        animation: none !important;
    }
}

/* Reduce effects for low-end devices */
@media (max-resolution: 150dpi) {
    body.liquid-glass-theme .glass-effect,
    body.liquid-glass-theme .glass-effect-strong {
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);
    }
    
    body.liquid-glass-theme::before,
    body.liquid-glass-theme::after {
        display: none;
    }
}

/* ===== Large Desktop (1200px and up) ===== */
@media (min-width: 1200px) {
    body.liquid-glass-theme .container-fluid {
        padding-left: 2rem;
        padding-right: 2rem;
    }
    
    body.liquid-glass-theme .hero-section {
        padding: 60px 50px;
        border-radius: 30px;
    }
    
    body.liquid-glass-theme .card {
        border-radius: 25px;
    }
    
    body.liquid-glass-theme .stat-card:hover {
        transform: translateY(-15px) scale(1.03);
    }
    
    body.liquid-glass-theme .quick-action-card:hover {
        transform: translateY(-10px) scale(1.08);
    }
}

/* ===== Desktop (992px to 1199px) ===== */
@media (min-width: 992px) and (max-width: 1199px) {
    body.liquid-glass-theme .hero-section {
        padding: 50px 40px;
        border-radius: 25px;
    }
    
    body.liquid-glass-theme .card {
        border-radius: 20px;
    }
    
    body.liquid-glass-theme .btn {
        padding: 12px 24px;
        border-radius: 25px;
    }
}

/* ===== Tablet (768px to 991px) ===== */
@media (min-width: 768px) and (max-width: 991px) {
    body.liquid-glass-theme .hero-section {
        padding: 40px 30px;
        border-radius: 20px;
        margin-bottom: 30px;
    }
    
    body.liquid-glass-theme .hero-section .display-4 {
        font-size: 2.5rem;
    }
    
    body.liquid-glass-theme .card {
        border-radius: 18px;
        margin-bottom: 20px;
    }
    
    body.liquid-glass-theme .btn {
        padding: 10px 20px;
        border-radius: 22px;
    }
    
    body.liquid-glass-theme .stat-card:hover {
        transform: translateY(-8px) scale(1.02);
    }
    
    body.liquid-glass-theme .quick-action-card:hover {
        transform: translateY(-6px) scale(1.03);
    }
    
    /* Reduce glass blur for better performance */
    body.liquid-glass-theme .glass-effect {
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
    }
    
    body.liquid-glass-theme .glass-effect-strong {
        backdrop-filter: blur(25px);
        -webkit-backdrop-filter: blur(25px);
    }
    
    /* Simplify navbar */
    body.liquid-glass-theme .navbar {
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
    }
    
    /* Optimize table for tablet */
    body.liquid-glass-theme .table {
        font-size: 0.9rem;
    }
    
    body.liquid-glass-theme .table tbody tr:hover {
        transform: scale(1.01);
    }
}

/* ===== Mobile Large (576px to 767px) ===== */
@media (min-width: 576px) and (max-width: 767px) {
    body.liquid-glass-theme .hero-section {
        padding: 30px 20px;
        border-radius: 18px;
        margin-bottom: 25px;
    }
    
    body.liquid-glass-theme .hero-section .display-4 {
        font-size: 2rem;
    }
    
    body.liquid-glass-theme .hero-section .lead {
        font-size: 1rem;
    }
    
    body.liquid-glass-theme .card {
        border-radius: 15px;
        margin-bottom: 15px;
    }
    
    body.liquid-glass-theme .btn {
        padding: 10px 18px;
        border-radius: 20px;
        font-size: 0.9rem;
    }
    
    body.liquid-glass-theme .stat-card {
        margin-bottom: 15px;
    }
    
    body.liquid-glass-theme .stat-card:hover {
        transform: translateY(-5px) scale(1.01);
    }
    
    body.liquid-glass-theme .quick-action-card {
        padding: 20px 15px;
    }
    
    body.liquid-glass-theme .quick-action-card:hover {
        transform: translateY(-4px) scale(1.02);
    }
    
    body.liquid-glass-theme .quick-action-icon {
        font-size: 2.5rem;
    }
    
    /* Further reduce glass effects */
    body.liquid-glass-theme .glass-effect {
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }
    
    body.liquid-glass-theme .glass-effect-strong {
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
    }
    
    /* Optimize navbar for mobile */
    body.liquid-glass-theme .navbar {
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        padding: 0.5rem 1rem;
    }
    
    body.liquid-glass-theme .navbar-brand {
        font-size: 1.2rem;
    }
    
    /* Optimize dropdown */
    body.liquid-glass-theme .dropdown-menu {
        border-radius: 12px;
        padding: 6px;
    }
    
    body.liquid-glass-theme .dropdown-item {
        padding: 8px 12px;
        border-radius: 8px;
        font-size: 0.9rem;
    }
    
    /* Optimize table for mobile */
    body.liquid-glass-theme .table {
        font-size: 0.85rem;
    }
    
    body.liquid-glass-theme .table thead th {
        padding: 8px;
        font-size: 0.8rem;
    }
    
    body.liquid-glass-theme .table tbody td {
        padding: 8px;
    }
    
    /* Optimize forms */
    body.liquid-glass-theme .form-control {
        border-radius: 12px;
        padding: 10px 15px;
    }
    
    body.liquid-glass-theme .form-label {
        font-size: 0.9rem;
    }
}

/* ===== Mobile Small (up to 575px) ===== */
@media (max-width: 575px) {
    body.liquid-glass-theme .hero-section {
        padding: 25px 15px;
        border-radius: 15px;
        margin-bottom: 20px;
    }
    
    body.liquid-glass-theme .hero-section .display-4 {
        font-size: 1.8rem;
        line-height: 1.2;
    }
    
    body.liquid-glass-theme .hero-section .lead {
        font-size: 0.95rem;
        margin-bottom: 20px;
    }
    
    body.liquid-glass-theme .hero-section .btn {
        display: block;
        width: 100%;
        margin-bottom: 10px;
    }
    
    body.liquid-glass-theme .card {
        border-radius: 12px;
        margin-bottom: 12px;
    }
    
    body.liquid-glass-theme .btn {
        padding: 8px 16px;
        border-radius: 18px;
        font-size: 0.85rem;
    }
    
    body.liquid-glass-theme .stat-card {
        margin-bottom: 12px;
    }
    
    body.liquid-glass-theme .stat-card .stat-number {
        font-size: 2.5rem;
    }
    
    body.liquid-glass-theme .stat-card .stat-icon {
        font-size: 2rem;
    }
    
    body.liquid-glass-theme .stat-card:hover {
        transform: translateY(-3px) scale(1.005);
    }
    
    body.liquid-glass-theme .quick-action-card {
        padding: 15px 10px;
        margin-bottom: 10px;
    }
    
    body.liquid-glass-theme .quick-action-card:hover {
        transform: translateY(-2px) scale(1.01);
    }
    
    body.liquid-glass-theme .quick-action-icon {
        font-size: 2rem;
        margin-bottom: 10px;
    }
    
    body.liquid-glass-theme .quick-action-card h6 {
        font-size: 0.9rem;
        margin-bottom: 5px;
    }
    
    body.liquid-glass-theme .quick-action-card small {
        font-size: 0.75rem;
    }
    
    /* Minimal glass effects for performance */
    body.liquid-glass-theme .glass-effect {
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);
    }
    
    body.liquid-glass-theme .glass-effect-strong {
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }
    
    /* Optimize navbar for small mobile */
    body.liquid-glass-theme .navbar {
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
        padding: 0.5rem;
    }
    
    body.liquid-glass-theme .navbar-brand {
        font-size: 1.1rem;
    }
    
    body.liquid-glass-theme .nav-link {
        padding: 8px 12px;
        font-size: 0.9rem;
    }
    
    /* Optimize dropdown for small screens */
    body.liquid-glass-theme .dropdown-menu {
        border-radius: 10px;
        padding: 4px;
        min-width: 250px;
    }
    
    body.liquid-glass-theme .dropdown-item {
        padding: 6px 10px;
        border-radius: 6px;
        font-size: 0.85rem;
    }
    
    /* Optimize table for small mobile */
    body.liquid-glass-theme .table {
        font-size: 0.8rem;
    }
    
    body.liquid-glass-theme .table thead th {
        padding: 6px;
        font-size: 0.75rem;
    }
    
    body.liquid-glass-theme .table tbody td {
        padding: 6px;
    }
    
    body.liquid-glass-theme .table tbody tr:hover {
        transform: none; /* Disable hover transform on small screens */
    }
    
    /* Optimize forms for small mobile */
    body.liquid-glass-theme .form-control {
        border-radius: 10px;
        padding: 8px 12px;
        font-size: 0.9rem;
    }
    
    body.liquid-glass-theme .form-label {
        font-size: 0.85rem;
        margin-bottom: 6px;
    }
    
    /* Optimize modal for small screens */
    body.liquid-glass-theme .modal-content {
        border-radius: 15px;
        margin: 10px;
    }
    
    body.liquid-glass-theme .modal-header,
    body.liquid-glass-theme .modal-footer {
        padding: 15px;
    }
    
    body.liquid-glass-theme .modal-body {
        padding: 15px;
    }
    
    /* Optimize theme switcher for small screens */
    .theme-switcher-container {
        bottom: 15px;
        right: 15px;
    }
    
    .theme-toggle-btn {
        padding: 10px 14px;
        font-size: 13px;
        min-width: 100px;
    }
    
    .theme-dropdown {
        min-width: 260px;
        margin-bottom: 8px;
    }
    
    .theme-option {
        padding: 10px;
    }
    
    .theme-option-name {
        font-size: 0.9rem;
    }
    
    .theme-option-desc {
        font-size: 0.75rem;
    }
    
    /* Disable particle effects on small screens */
    body.liquid-glass-theme::after {
        display: none;
    }
    
    /* Reduce complex animations */
    body.liquid-glass-theme .hero-section::before {
        animation-duration: 30s;
    }
}

/* ===== Touch Device Optimizations ===== */
@media (hover: none) and (pointer: coarse) {
    /* Optimize for touch devices */
    body.liquid-glass-theme .btn {
        min-height: 44px; /* iOS recommended touch target size */
        min-width: 44px;
    }
    
    body.liquid-glass-theme .nav-link {
        min-height: 44px;
        display: flex;
        align-items: center;
    }
    
    body.liquid-glass-theme .dropdown-item {
        min-height: 44px;
        display: flex;
        align-items: center;
    }
    
    /* Remove hover effects on touch devices */
    body.liquid-glass-theme .card:hover,
    body.liquid-glass-theme .btn:hover,
    body.liquid-glass-theme .quick-action-card:hover {
        transform: none;
    }
    
    /* Use active states instead */
    body.liquid-glass-theme .card:active,
    body.liquid-glass-theme .btn:active,
    body.liquid-glass-theme .quick-action-card:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }
}

/* ===== High DPI Display Optimizations ===== */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    body.liquid-glass-theme .glass-effect,
    body.liquid-glass-theme .glass-effect-strong {
        /* Enhanced blur for high DPI displays */
        backdrop-filter: var(--glass-blur);
        -webkit-backdrop-filter: var(--glass-blur);
    }
    
    body.liquid-glass-theme .card,
    body.liquid-glass-theme .btn,
    body.liquid-glass-theme .navbar {
        /* Sharper borders on high DPI */
        border-width: 0.5px;
    }
}
