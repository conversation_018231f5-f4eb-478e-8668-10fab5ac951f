/* 
 * Liquid Glass Theme for OTA Device Management System
 * Inspired by iOS 26 Design System
 * Author: AI Assistant
 * Version: 1.0.0
 */

/* ===== CSS Variables for Liquid Glass Theme ===== */
:root {
    /* Glass Effect Variables */
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-bg-light: rgba(255, 255, 255, 0.15);
    --glass-bg-dark: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    --glass-shadow-hover: 0 12px 40px rgba(0, 0, 0, 0.15);
    --glass-blur: blur(20px);
    --glass-blur-strong: blur(40px);
    
    /* Color Palette */
    --primary-glass: rgba(0, 123, 255, 0.8);
    --success-glass: rgba(40, 167, 69, 0.8);
    --warning-glass: rgba(255, 193, 7, 0.8);
    --danger-glass: rgba(220, 53, 69, 0.8);
    --info-glass: rgba(23, 162, 184, 0.8);
    
    /* Background Gradients */
    --bg-gradient-primary: linear-gradient(135deg, 
        rgba(102, 126, 234, 0.1) 0%, 
        rgba(118, 75, 162, 0.1) 100%);
    --bg-gradient-secondary: linear-gradient(135deg, 
        rgba(108, 117, 125, 0.1) 0%, 
        rgba(73, 80, 87, 0.1) 100%);
    
    /* Animation Variables */
    --transition-smooth: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --hover-scale: scale(1.02);
    --active-scale: scale(0.98);
}

/* Dark Mode Variables */
body.dark-mode {
    --glass-bg: rgba(30, 30, 30, 0.3);
    --glass-bg-light: rgba(30, 30, 30, 0.4);
    --glass-bg-dark: rgba(30, 30, 30, 0.2);
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    --glass-shadow-hover: 0 12px 40px rgba(0, 0, 0, 0.4);
    
    --bg-gradient-primary: linear-gradient(135deg, 
        rgba(102, 126, 234, 0.2) 0%, 
        rgba(118, 75, 162, 0.2) 100%);
}

/* ===== Base Glass Effects ===== */
.glass-effect {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
    transition: var(--transition-smooth);
}

.glass-effect-strong {
    background: var(--glass-bg-light);
    backdrop-filter: var(--glass-blur-strong);
    -webkit-backdrop-filter: var(--glass-blur-strong);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
}

.glass-hover:hover {
    background: var(--glass-bg-light);
    box-shadow: var(--glass-shadow-hover);
    transform: var(--hover-scale);
}

.glass-active:active {
    transform: var(--active-scale);
}

/* ===== Liquid Glass Body Styles ===== */
body.liquid-glass-theme {
    background: linear-gradient(135deg, 
        #667eea 0%, 
        #764ba2 25%, 
        #f093fb 50%, 
        #f5576c 75%, 
        #4facfe 100%);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    min-height: 100vh;
    position: relative;
}

body.liquid-glass-theme::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Dark mode background */
body.liquid-glass-theme.dark-mode {
    background: linear-gradient(135deg, 
        #1a1a2e 0%, 
        #16213e 25%, 
        #0f3460 50%, 
        #533483 75%, 
        #1a1a2e 100%);
}

body.liquid-glass-theme.dark-mode::before {
    background: radial-gradient(circle at 20% 80%, rgba(79, 172, 254, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%);
}

/* ===== Navigation Bar ===== */
body.liquid-glass-theme .navbar {
    background: var(--glass-bg) !important;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border-bottom: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
    transition: var(--transition-smooth);
}

body.liquid-glass-theme .navbar-brand {
    color: rgba(255, 255, 255, 0.95) !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    font-weight: 700;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

body.liquid-glass-theme .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

body.liquid-glass-theme .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

body.liquid-glass-theme .nav-link:hover::before {
    left: 100%;
}

body.liquid-glass-theme .nav-link:hover {
    color: rgba(255, 255, 255, 1) !important;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

/* ===== Cards ===== */
body.liquid-glass-theme .card {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    box-shadow: var(--glass-shadow);
    transition: var(--transition-smooth);
    overflow: hidden;
    position: relative;
}

body.liquid-glass-theme .card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
}

body.liquid-glass-theme .card:hover {
    background: var(--glass-bg-light);
    box-shadow: var(--glass-shadow-hover);
    transform: translateY(-8px) var(--hover-scale);
}

body.liquid-glass-theme .card-header {
    background: rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid var(--glass-border);
    border-radius: 20px 20px 0 0 !important;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
}

body.liquid-glass-theme .card-body {
    background: transparent;
}

/* ===== Buttons ===== */
body.liquid-glass-theme .btn {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: 25px;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    padding: 12px 24px;
    transition: var(--transition-bounce);
    position: relative;
    overflow: hidden;
    box-shadow: var(--glass-shadow);
}

body.liquid-glass-theme .btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    transition: all 0.6s ease;
    transform: translate(-50%, -50%);
    border-radius: 50%;
}

body.liquid-glass-theme .btn:hover::before {
    width: 300px;
    height: 300px;
}

body.liquid-glass-theme .btn:hover {
    background: var(--glass-bg-light);
    box-shadow: var(--glass-shadow-hover);
    transform: translateY(-2px) var(--hover-scale);
    color: rgba(255, 255, 255, 1);
}

body.liquid-glass-theme .btn:active {
    transform: var(--active-scale);
}

/* Button Variants */
body.liquid-glass-theme .btn-primary {
    background: var(--primary-glass);
    border-color: rgba(0, 123, 255, 0.3);
}

body.liquid-glass-theme .btn-success {
    background: var(--success-glass);
    border-color: rgba(40, 167, 69, 0.3);
}

body.liquid-glass-theme .btn-warning {
    background: var(--warning-glass);
    border-color: rgba(255, 193, 7, 0.3);
    color: rgba(0, 0, 0, 0.8);
}

body.liquid-glass-theme .btn-danger {
    background: var(--danger-glass);
    border-color: rgba(220, 53, 69, 0.3);
}

body.liquid-glass-theme .btn-info {
    background: var(--info-glass);
    border-color: rgba(23, 162, 184, 0.3);
}

/* ===== Tables ===== */
body.liquid-glass-theme .table {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--glass-shadow);
    border: 1px solid var(--glass-border);
}

body.liquid-glass-theme .table thead th {
    background: var(--glass-bg-light);
    border-bottom: 1px solid var(--glass-border);
    color: rgba(255, 255, 255, 0.9);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.85rem;
}

body.liquid-glass-theme .table tbody tr {
    transition: var(--transition-smooth);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

body.liquid-glass-theme .table tbody tr:hover {
    background: var(--glass-bg-light);
    transform: var(--hover-scale);
    box-shadow: inset 0 0 20px rgba(255, 255, 255, 0.1);
}

body.liquid-glass-theme .table tbody td {
    color: rgba(255, 255, 255, 0.85);
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    vertical-align: middle;
}

/* ===== Dropdown Menus ===== */
body.liquid-glass-theme .dropdown-menu {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: 15px;
    box-shadow: var(--glass-shadow-hover);
    padding: 8px;
    margin-top: 8px;
}

body.liquid-glass-theme .dropdown-item {
    color: rgba(255, 255, 255, 0.9);
    border-radius: 10px;
    padding: 10px 16px;
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

body.liquid-glass-theme .dropdown-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.3s ease;
}

body.liquid-glass-theme .dropdown-item:hover::before {
    left: 100%;
}

body.liquid-glass-theme .dropdown-item:hover {
    background: var(--glass-bg-light);
    color: rgba(255, 255, 255, 1);
    transform: translateX(5px);
}

body.liquid-glass-theme .dropdown-divider {
    border-color: var(--glass-border);
    margin: 8px 0;
}

/* ===== Forms ===== */
body.liquid-glass-theme .form-control {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: 15px;
    color: rgba(255, 255, 255, 0.9);
    transition: var(--transition-smooth);
    box-shadow: var(--glass-shadow);
}

body.liquid-glass-theme .form-control:focus {
    background: var(--glass-bg-light);
    border-color: rgba(102, 126, 234, 0.5);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25), var(--glass-shadow-hover);
    color: rgba(255, 255, 255, 1);
}

body.liquid-glass-theme .form-control::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

body.liquid-glass-theme .form-label {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    margin-bottom: 8px;
}

/* ===== Alerts ===== */
body.liquid-glass-theme .alert {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: 15px;
    box-shadow: var(--glass-shadow);
    color: rgba(255, 255, 255, 0.9);
    position: relative;
    overflow: hidden;
}

body.liquid-glass-theme .alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.3));
}

body.liquid-glass-theme .alert-success::before {
    background: linear-gradient(180deg, rgba(40, 167, 69, 0.8), rgba(40, 167, 69, 0.3));
}

body.liquid-glass-theme .alert-warning::before {
    background: linear-gradient(180deg, rgba(255, 193, 7, 0.8), rgba(255, 193, 7, 0.3));
}

body.liquid-glass-theme .alert-danger::before {
    background: linear-gradient(180deg, rgba(220, 53, 69, 0.8), rgba(220, 53, 69, 0.3));
}

body.liquid-glass-theme .alert-info::before {
    background: linear-gradient(180deg, rgba(23, 162, 184, 0.8), rgba(23, 162, 184, 0.3));
}

/* ===== Badges ===== */
body.liquid-glass-theme .badge {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    color: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    padding: 6px 12px;
    font-weight: 500;
    box-shadow: var(--glass-shadow);
}

body.liquid-glass-theme .badge.bg-success {
    background: var(--success-glass) !important;
    border-color: rgba(40, 167, 69, 0.3);
}

body.liquid-glass-theme .badge.bg-warning {
    background: var(--warning-glass) !important;
    border-color: rgba(255, 193, 7, 0.3);
    color: rgba(0, 0, 0, 0.8) !important;
}

body.liquid-glass-theme .badge.bg-danger {
    background: var(--danger-glass) !important;
    border-color: rgba(220, 53, 69, 0.3);
}

body.liquid-glass-theme .badge.bg-info {
    background: var(--info-glass) !important;
    border-color: rgba(23, 162, 184, 0.3);
}

/* ===== Modal Dialogs ===== */
body.liquid-glass-theme .modal-content {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur-strong);
    -webkit-backdrop-filter: var(--glass-blur-strong);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    box-shadow: var(--glass-shadow-hover);
    color: rgba(255, 255, 255, 0.9);
}

body.liquid-glass-theme .modal-header {
    border-bottom: 1px solid var(--glass-border);
    background: var(--glass-bg-light);
    border-radius: 20px 20px 0 0;
}

body.liquid-glass-theme .modal-footer {
    border-top: 1px solid var(--glass-border);
    background: var(--glass-bg-light);
    border-radius: 0 0 20px 20px;
}

body.liquid-glass-theme .modal-backdrop {
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

/* ===== Progress Bars ===== */
body.liquid-glass-theme .progress {
    background: var(--glass-bg-dark);
    border-radius: 25px;
    border: 1px solid var(--glass-border);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

body.liquid-glass-theme .progress-bar {
    background: linear-gradient(45deg, rgba(102, 126, 234, 0.8), rgba(118, 75, 162, 0.8));
    border-radius: 25px;
    position: relative;
    overflow: hidden;
}

body.liquid-glass-theme .progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: progressShine 2s infinite;
}

@keyframes progressShine {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* ===== Pagination ===== */
body.liquid-glass-theme .pagination .page-link {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    color: rgba(255, 255, 255, 0.9);
    margin: 0 2px;
    border-radius: 10px;
    transition: var(--transition-smooth);
}

body.liquid-glass-theme .pagination .page-link:hover {
    background: var(--glass-bg-light);
    color: rgba(255, 255, 255, 1);
    transform: var(--hover-scale);
}

body.liquid-glass-theme .pagination .page-item.active .page-link {
    background: var(--primary-glass);
    border-color: rgba(102, 126, 234, 0.3);
}

/* ===== Floating Elements ===== */
body.liquid-glass-theme .floating-element {
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* ===== Special Effects ===== */
.liquid-ripple {
    position: relative;
    overflow: hidden;
}

.liquid-ripple::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    transform: translate(-50%, -50%);
    transition: width 0.6s ease, height 0.6s ease;
}

.liquid-ripple:hover::after {
    width: 200px;
    height: 200px;
}

/* ===== Scrollbar Styling ===== */
body.liquid-glass-theme ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

body.liquid-glass-theme ::-webkit-scrollbar-track {
    background: var(--glass-bg-dark);
    border-radius: 10px;
}

body.liquid-glass-theme ::-webkit-scrollbar-thumb {
    background: var(--glass-bg-light);
    border-radius: 10px;
    border: 1px solid var(--glass-border);
}

body.liquid-glass-theme ::-webkit-scrollbar-thumb:hover {
    background: var(--primary-glass);
}

/* ===== Footer ===== */
body.liquid-glass-theme .footer {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border-top: 1px solid var(--glass-border);
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
    color: rgba(255, 255, 255, 0.8);
}

/* ===== Loading Spinner ===== */
body.liquid-glass-theme .loading-spinner {
    border: 3px solid var(--glass-bg);
    border-top: 3px solid rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== Responsive Design ===== */
@media (max-width: 768px) {
    body.liquid-glass-theme .card {
        border-radius: 15px;
        margin-bottom: 15px;
    }

    body.liquid-glass-theme .btn {
        padding: 10px 20px;
        border-radius: 20px;
    }

    body.liquid-glass-theme .navbar {
        backdrop-filter: blur(15px);
        -webkit-backdrop-filter: blur(15px);
    }

    /* Reduce glass effects on mobile for performance */
    body.liquid-glass-theme .glass-effect {
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }
}

@media (max-width: 576px) {
    body.liquid-glass-theme .card {
        border-radius: 12px;
    }

    body.liquid-glass-theme .btn {
        padding: 8px 16px;
        border-radius: 18px;
    }

    /* Further reduce effects on small screens */
    body.liquid-glass-theme .glass-effect {
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);
    }
}
