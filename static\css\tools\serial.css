/* 串口终端样式 */
.serial-terminal {
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 500px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.terminal-header {
    padding: 15px;
    border-bottom: 1px solid #e5e5e5;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.connection-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

.connection-controls select {
    width: 120px;
}

.display-controls {
    display: flex;
    gap: 15px;
}

.terminal-content {
    flex: 1;
    padding: 15px;
    overflow-y: auto;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 14px;
    line-height: 1.5;
    background: #1e1e1e;
    color: #d4d4d4;
}

.terminal-line {
    white-space: pre-wrap;
    word-break: break-all;
    margin-bottom: 4px;
}

.terminal-line.received {
    color: #9cdcfe;
}

.terminal-line.sent {
    color: #4ec9b0;
}

.terminal-line.error {
    color: #f14c4c;
}

.terminal-line.success {
    color: #4caf50;
}

.terminal-line.info {
    color: #ffd700;
}

.terminal-input {
    padding: 15px;
    border-top: 1px solid #e5e5e5;
    background: #f8f9fa;
    border-radius: 0 0 8px 8px;
}

.send-options {
    margin-top: 10px;
    display: flex;
    gap: 15px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .connection-controls,
    .display-controls {
        flex-direction: column;
        gap: 5px;
    }
    
    .connection-controls select {
        width: 100%;
    }
    
    .terminal-content {
        font-size: 12px;
    }
} 