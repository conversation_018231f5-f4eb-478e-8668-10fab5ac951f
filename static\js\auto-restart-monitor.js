/**
 * 自动重启监控组件
 * 显示调试脚本自动重启的状态和日志
 */

class AutoRestartMonitor {
    constructor() {
        this.refreshInterval = 30000; // 30秒刷新一次
        this.intervalId = null;
        this.isVisible = false;
        
        this.initializeUI();
        this.startMonitoring();
    }

    /**
     * 初始化用户界面
     */
    initializeUI() {
        this.createStatusWidget();
        this.bindEvents();
    }

    /**
     * 创建状态小部件
     */
    createStatusWidget() {
        // 查找合适的位置插入状态小部件
        const navbar = document.querySelector('.navbar-nav');
        if (navbar) {
            const statusItem = document.createElement('li');
            statusItem.className = 'nav-item dropdown';
            statusItem.innerHTML = `
                <a class="nav-link dropdown-toggle" href="#" id="autoRestartDropdown" role="button" 
                   data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-sync-alt me-1" id="restartStatusIcon"></i>
                    <span id="restartStatusText">自动重启</span>
                    <span class="badge bg-secondary ms-1" id="runningScriptsCount">0</span>
                </a>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="autoRestartDropdown" style="min-width: 400px;">
                    <li><h6 class="dropdown-header">调试脚本自动重启状态</h6></li>
                    <li><hr class="dropdown-divider"></li>
                    <li class="px-3 py-2">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>服务状态:</span>
                            <span id="serviceStatus" class="badge bg-secondary">未知</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>运行脚本数:</span>
                            <span id="detailRunningCount" class="badge bg-info">0</span>
                        </div>
                        <div class="mb-2">
                            <small class="text-muted">最后更新: <span id="lastUpdateTime">-</span></small>
                        </div>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    <li class="px-3 py-2">
                        <button class="btn btn-sm btn-outline-primary w-100 mb-2" id="refreshStatusBtn">
                            <i class="fas fa-refresh me-1"></i>刷新状态
                        </button>
                        <button class="btn btn-sm btn-outline-success w-100 mb-2" id="triggerRestartBtn">
                            <i class="fas fa-play me-1"></i>手动重启
                        </button>
                        <button class="btn btn-sm btn-outline-info w-100" id="viewLogsBtn">
                            <i class="fas fa-file-alt me-1"></i>查看日志
                        </button>
                    </li>
                </ul>
            `;
            
            navbar.appendChild(statusItem);
        }

        // 创建日志查看模态框
        this.createLogsModal();
    }

    /**
     * 创建日志查看模态框
     */
    createLogsModal() {
        const modalHTML = `
        <div class="modal fade" id="autoRestartLogsModal" tabindex="-1" aria-labelledby="autoRestartLogsModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="autoRestartLogsModalLabel">
                            <i class="fas fa-file-alt me-2"></i>自动重启日志
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="text-muted">最近20条日志记录</span>
                                <button class="btn btn-sm btn-outline-primary" id="refreshLogsBtn">
                                    <i class="fas fa-refresh me-1"></i>刷新
                                </button>
                            </div>
                        </div>
                        <div id="logsContainer" style="max-height: 400px; overflow-y: auto;">
                            <div class="text-center text-muted">
                                <i class="fas fa-spinner fa-spin me-1"></i>加载中...
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 刷新状态按钮
        document.getElementById('refreshStatusBtn')?.addEventListener('click', () => {
            this.refreshStatus();
        });

        // 手动触发重启按钮
        document.getElementById('triggerRestartBtn')?.addEventListener('click', () => {
            this.triggerManualRestart();
        });

        // 查看日志按钮
        document.getElementById('viewLogsBtn')?.addEventListener('click', () => {
            this.showLogsModal();
        });

        // 刷新日志按钮
        document.getElementById('refreshLogsBtn')?.addEventListener('click', () => {
            this.refreshLogs();
        });

        // 下拉菜单显示/隐藏事件
        const dropdown = document.getElementById('autoRestartDropdown');
        if (dropdown) {
            dropdown.addEventListener('show.bs.dropdown', () => {
                this.isVisible = true;
                this.refreshStatus();
            });

            dropdown.addEventListener('hide.bs.dropdown', () => {
                this.isVisible = false;
            });
        }
    }

    /**
     * 开始监控
     */
    startMonitoring() {
        // 立即刷新一次
        this.refreshStatus();

        // 设置定时刷新
        this.intervalId = setInterval(() => {
            if (this.isVisible) {
                this.refreshStatus();
            }
        }, this.refreshInterval);
    }

    /**
     * 停止监控
     */
    stopMonitoring() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
    }

    /**
     * 刷新状态
     */
    async refreshStatus() {
        try {
            const response = await fetch('/debug_script/auto_restart_status');
            const data = await response.json();

            if (data.success) {
                this.updateStatusDisplay(data.status);
            } else {
                this.showError('获取状态失败: ' + (data.error || '未知错误'));
            }
        } catch (error) {
            console.error('刷新自动重启状态失败:', error);
            this.showError('网络错误，无法获取状态');
        }
    }

    /**
     * 更新状态显示
     */
    updateStatusDisplay(status) {
        // 更新图标和状态
        const statusIcon = document.getElementById('restartStatusIcon');
        const statusText = document.getElementById('restartStatusText');
        const runningCount = document.getElementById('runningScriptsCount');
        const serviceStatus = document.getElementById('serviceStatus');
        const detailRunningCount = document.getElementById('detailRunningCount');
        const lastUpdateTime = document.getElementById('lastUpdateTime');

        if (statusIcon && statusText && runningCount) {
            if (status.is_running) {
                statusIcon.className = 'fas fa-sync-alt fa-spin me-1 text-success';
                serviceStatus.className = 'badge bg-success';
                serviceStatus.textContent = '运行中';
            } else {
                statusIcon.className = 'fas fa-sync-alt me-1 text-secondary';
                serviceStatus.className = 'badge bg-secondary';
                serviceStatus.textContent = '已停止';
            }

            runningCount.textContent = status.running_scripts_count || 0;
            detailRunningCount.textContent = status.running_scripts_count || 0;
            lastUpdateTime.textContent = new Date().toLocaleTimeString('zh-CN');
        }
    }

    /**
     * 手动触发重启
     */
    async triggerManualRestart() {
        try {
            const confirmResult = confirm('确定要手动触发自动重启吗？这将重新启动所有已启用的调试脚本。');
            if (!confirmResult) {
                return;
            }

            const button = document.getElementById('triggerRestartBtn');
            const originalHTML = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>重启中...';
            button.disabled = true;

            const response = await fetch('/debug_script/trigger_auto_restart', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const data = await response.json();

            if (data.success) {
                this.showSuccess('自动重启已触发');
                // 延迟刷新状态
                setTimeout(() => {
                    this.refreshStatus();
                }, 2000);
            } else {
                this.showError('触发重启失败: ' + (data.error || '未知错误'));
            }

        } catch (error) {
            console.error('触发手动重启失败:', error);
            this.showError('网络错误，无法触发重启');
        } finally {
            const button = document.getElementById('triggerRestartBtn');
            button.innerHTML = '<i class="fas fa-play me-1"></i>手动重启';
            button.disabled = false;
        }
    }

    /**
     * 显示日志模态框
     */
    showLogsModal() {
        const modal = new bootstrap.Modal(document.getElementById('autoRestartLogsModal'));
        modal.show();
        this.refreshLogs();
    }

    /**
     * 刷新日志
     */
    async refreshLogs() {
        try {
            const logsContainer = document.getElementById('logsContainer');
            logsContainer.innerHTML = '<div class="text-center text-muted"><i class="fas fa-spinner fa-spin me-1"></i>加载中...</div>';

            const response = await fetch('/debug_script/auto_restart_status');
            const data = await response.json();

            if (data.success && data.status.recent_logs) {
                this.displayLogs(data.status.recent_logs);
            } else {
                logsContainer.innerHTML = '<div class="text-center text-muted">暂无日志记录</div>';
            }
        } catch (error) {
            console.error('刷新日志失败:', error);
            const logsContainer = document.getElementById('logsContainer');
            logsContainer.innerHTML = '<div class="text-center text-danger">加载日志失败</div>';
        }
    }

    /**
     * 显示日志
     */
    displayLogs(logs) {
        const logsContainer = document.getElementById('logsContainer');
        
        if (!logs || logs.length === 0) {
            logsContainer.innerHTML = '<div class="text-center text-muted">暂无日志记录</div>';
            return;
        }

        const logsHTML = logs.map(log => {
            const isError = log.includes('失败') || log.includes('异常') || log.includes('错误');
            const isSuccess = log.includes('成功') || log.includes('完成');
            
            let badgeClass = 'bg-secondary';
            if (isError) badgeClass = 'bg-danger';
            else if (isSuccess) badgeClass = 'bg-success';

            return `
                <div class="border-bottom py-2">
                    <div class="d-flex justify-content-between align-items-start">
                        <span class="text-muted small">${log}</span>
                        <span class="badge ${badgeClass} ms-2">
                            ${isError ? '错误' : isSuccess ? '成功' : '信息'}
                        </span>
                    </div>
                </div>
            `;
        }).join('');

        logsContainer.innerHTML = logsHTML;
    }

    /**
     * 显示成功消息
     */
    showSuccess(message) {
        // 这里可以使用toast或其他通知方式
        alert('成功: ' + message);
    }

    /**
     * 显示错误消息
     */
    showError(message) {
        // 这里可以使用toast或其他通知方式
        alert('错误: ' + message);
    }
}

// 自动初始化
document.addEventListener('DOMContentLoaded', function() {
    // 只在管理员页面或调试页面初始化
    if (document.querySelector('.navbar') && 
        (window.location.pathname.includes('debug') || 
         window.location.pathname.includes('admin'))) {
        window.autoRestartMonitor = new AutoRestartMonitor();
    }
});

// 导出类
window.AutoRestartMonitor = AutoRestartMonitor;
