/**
 * 串口终端UI组件
 * 提供串口通信的用户界面
 */
class SerialTerminal {
    constructor(containerId, serialTool) {
        this.container = document.getElementById(containerId);
        this.serialTool = serialTool;
        this.initializeUI();
        this.setupEventListeners();
    }

    /**
     * 初始化用户界面
     * @private
     */
    initializeUI() {
        this.container.innerHTML = `
            <div class="serial-terminal">
                <div class="terminal-header">
                    <div class="connection-controls">
                        <select id="baudRate" class="form-select">
                            <option value="9600">9600</option>
                            <option value="19200">19200</option>
                            <option value="38400">38400</option>
                            <option value="57600">57600</option>
                            <option value="115200" selected>115200</option>
                        </select>
                        <button id="connectBtn" class="btn btn-primary">
                            <i class="fas fa-plug"></i> 连接
                        </button>
                        <button id="clearBtn" class="btn btn-secondary">
                            <i class="fas fa-eraser"></i> 清空
                        </button>
                    </div>
                    <div class="display-controls">
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" id="autoScroll" checked>
                            <label class="form-check-label" for="autoScroll">自动滚动</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" id="showTimestamp">
                            <label class="form-check-label" for="showTimestamp">显示时间戳</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" id="showHex">
                            <label class="form-check-label" for="showHex">HEX显示</label>
                        </div>
                    </div>
                </div>
                <div id="terminal" class="terminal-content"></div>
                <div class="terminal-input">
                    <div class="input-group">
                        <input type="text" id="sendInput" class="form-control" placeholder="输入要发送的数据...">
                        <button id="sendBtn" class="btn btn-primary">
                            <i class="fas fa-paper-plane"></i> 发送
                        </button>
                    </div>
                    <div class="send-options">
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" id="sendHex">
                            <label class="form-check-label" for="sendHex">HEX发送</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" id="sendNewline" checked>
                            <label class="form-check-label" for="sendNewline">发送新行</label>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 获取UI元素引用
        this.elements = {
            baudRate: document.getElementById('baudRate'),
            connectBtn: document.getElementById('connectBtn'),
            clearBtn: document.getElementById('clearBtn'),
            terminal: document.getElementById('terminal'),
            sendInput: document.getElementById('sendInput'),
            sendBtn: document.getElementById('sendBtn'),
            autoScroll: document.getElementById('autoScroll'),
            showTimestamp: document.getElementById('showTimestamp'),
            showHex: document.getElementById('showHex'),
            sendHex: document.getElementById('sendHex'),
            sendNewline: document.getElementById('sendNewline')
        };
    }

    /**
     * 设置事件监听器
     * @private
     */
    setupEventListeners() {
        // 连接按钮事件
        this.elements.connectBtn.addEventListener('click', () => {
            if (!this.serialTool.port) {
                this.connect();
            } else {
                this.disconnect();
            }
        });

        // 清空按钮事件
        this.elements.clearBtn.addEventListener('click', () => {
            this.elements.terminal.innerHTML = '';
        });

        // 发送按钮事件
        this.elements.sendBtn.addEventListener('click', () => {
            this.sendData();
        });

        // 输入框回车事件
        this.elements.sendInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendData();
            }
        });

        // 设置串口工具回调
        this.serialTool.init({
            baudRate: parseInt(this.elements.baudRate.value),
            onReceive: (data) => this.handleReceiveData(data),
            onConnect: () => this.handleConnect(),
            onDisconnect: () => this.handleDisconnect(),
            onError: (error) => this.handleError(error)
        });
    }

    /**
     * 连接串口
     * @private
     */
    async connect() {
        try {
            await this.serialTool.requestPort();
        } catch (error) {
            this.appendToTerminal('错误: ' + error.message, 'error');
        }
    }

    /**
     * 断开串口连接
     * @private
     */
    async disconnect() {
        await this.serialTool.disconnect();
    }

    /**
     * 发送数据
     * @private
     */
    async sendData() {
        const input = this.elements.sendInput.value;
        if (!input) return;

        try {
            let data = input;
            if (this.elements.sendHex.checked) {
                data = this.hexStringToBytes(input);
            }
            if (this.elements.sendNewline.checked) {
                data = data + '\n';
            }

            await this.serialTool.send(data);
            this.appendToTerminal(`发送: ${input}`, 'sent');
            this.elements.sendInput.value = '';
        } catch (error) {
            this.appendToTerminal('发送错误: ' + error.message, 'error');
        }
    }

    /**
     * 处理接收到的数据
     * @param {Uint8Array} data - 接收到的数据
     * @private
     */
    handleReceiveData(data) {
        const decoder = new TextDecoder();
        let text = decoder.decode(data);
        
        if (this.elements.showHex.checked) {
            text = Array.from(data)
                .map(b => b.toString(16).padStart(2, '0'))
                .join(' ');
        }

        if (this.elements.showTimestamp.checked) {
            const timestamp = new Date().toLocaleTimeString();
            text = `[${timestamp}] ${text}`;
        }

        this.appendToTerminal(text, 'received');
    }

    /**
     * 将内容添加到终端显示区域
     * @param {string} text - 要显示的文本
     * @param {string} type - 消息类型
     * @private
     */
    appendToTerminal(text, type = '') {
        const line = document.createElement('div');
        line.className = `terminal-line ${type}`;
        line.textContent = text;
        this.elements.terminal.appendChild(line);

        if (this.elements.autoScroll.checked) {
            this.elements.terminal.scrollTop = this.elements.terminal.scrollHeight;
        }
    }

    /**
     * 处理连接成功
     * @private
     */
    handleConnect() {
        this.elements.connectBtn.innerHTML = '<i class="fas fa-times"></i> 断开';
        this.elements.connectBtn.classList.replace('btn-primary', 'btn-danger');
        this.appendToTerminal('串口已连接', 'success');
    }

    /**
     * 处理断开连接
     * @private
     */
    handleDisconnect() {
        this.elements.connectBtn.innerHTML = '<i class="fas fa-plug"></i> 连接';
        this.elements.connectBtn.classList.replace('btn-danger', 'btn-primary');
        this.appendToTerminal('串口已断开', 'info');
    }

    /**
     * 处理错误
     * @param {Object} error - 错误信息
     * @private
     */
    handleError(error) {
        this.appendToTerminal(`错误: ${error.message}`, 'error');
    }

    /**
     * 将十六进制字符串转换为字节数组
     * @param {string} hexString - 十六进制字符串
     * @returns {Uint8Array}
     * @private
     */
    hexStringToBytes(hexString) {
        hexString = hexString.replace(/\s+/g, '');
        const bytes = new Uint8Array(hexString.length / 2);
        for (let i = 0; i < hexString.length; i += 2) {
            bytes[i / 2] = parseInt(hexString.substr(i, 2), 16);
        }
        return bytes;
    }
}

// 导出组件
export default SerialTerminal; 