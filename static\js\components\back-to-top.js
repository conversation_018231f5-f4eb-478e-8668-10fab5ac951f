/**
 * Back to Top Component - Rocket Style
 * 回到顶部组件 - 火箭样式
 * 与Liquid Glass主题保持一致
 */

class BackToTop {
    constructor(options = {}) {
        this.options = {
            // 显示按钮的滚动阈值（像素）
            showThreshold: 300,
            // 滚动动画持续时间（毫秒）
            scrollDuration: 500,
            // 按钮容器选择器
            containerSelector: 'body',
            // 是否启用粒子效果
            enableParticles: true,
            // 是否启用声音效果（如果有的话）
            enableSound: false,
            // 自定义样式类
            customClass: '',
            ...options
        };

        this.isVisible = false;
        this.isScrolling = false;
        this.button = null;
        this.particles = [];

        this.init();
    }

    /**
     * 初始化组件
     */
    init() {
        this.createButton();
        this.bindEvents();
        this.checkScroll();
    }

    /**
     * 创建回到顶部按钮
     */
    createButton() {
        // 创建按钮容器
        this.button = document.createElement('div');
        this.button.className = `back-to-top ${this.options.customClass}`;
        this.button.setAttribute('aria-label', '回到顶部');
        this.button.setAttribute('role', 'button');
        this.button.setAttribute('tabindex', '0');

        // 创建火箭按钮
        const rocketButton = document.createElement('button');
        rocketButton.className = 'rocket-button';
        rocketButton.setAttribute('title', '回到顶部');

        // 创建火箭图标
        const rocketIcon = document.createElement('i');
        rocketIcon.className = 'fas fa-rocket rocket-icon';

        // 创建粒子容器
        const particlesContainer = document.createElement('div');
        particlesContainer.className = 'rocket-particles';

        // 组装按钮
        rocketButton.appendChild(rocketIcon);
        if (this.options.enableParticles) {
            rocketButton.appendChild(particlesContainer);
        }
        this.button.appendChild(rocketButton);

        // 添加到页面
        const container = document.querySelector(this.options.containerSelector);
        container.appendChild(this.button);
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 滚动事件监听
        window.addEventListener('scroll', this.throttle(this.handleScroll.bind(this), 16));

        // 点击事件监听
        this.button.addEventListener('click', this.handleClick.bind(this));

        // 键盘事件监听（无障碍支持）
        this.button.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.handleClick();
            }
        });

        // 鼠标进入/离开事件
        this.button.addEventListener('mouseenter', this.handleMouseEnter.bind(this));
        this.button.addEventListener('mouseleave', this.handleMouseLeave.bind(this));
    }

    /**
     * 处理滚动事件
     */
    handleScroll() {
        this.checkScroll();
    }

    /**
     * 检查滚动位置并显示/隐藏按钮
     */
    checkScroll() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const shouldShow = scrollTop > this.options.showThreshold;

        if (shouldShow && !this.isVisible) {
            this.show();
        } else if (!shouldShow && this.isVisible) {
            this.hide();
        }
    }

    /**
     * 显示按钮
     */
    show() {
        this.isVisible = true;
        this.button.classList.add('show');
        
        // 触发显示动画
        requestAnimationFrame(() => {
            this.button.style.transform = 'translateY(0) scale(1)';
        });
    }

    /**
     * 隐藏按钮
     */
    hide() {
        this.isVisible = false;
        this.button.classList.remove('show');
    }

    /**
     * 处理点击事件
     */
    handleClick() {
        if (this.isScrolling) return;

        // 添加发射动画类
        const rocketButton = this.button.querySelector('.rocket-button');
        rocketButton.classList.add('launching');

        // 创建粒子爆炸效果
        if (this.options.enableParticles) {
            this.createParticleExplosion();
        }

        // 执行滚动到顶部
        this.scrollToTop();

        // 移除发射动画类
        setTimeout(() => {
            rocketButton.classList.remove('launching');
        }, 500);
    }

    /**
     * 平滑滚动到顶部
     */
    scrollToTop() {
        this.isScrolling = true;
        const startPosition = window.pageYOffset;
        const startTime = performance.now();

        const animateScroll = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / this.options.scrollDuration, 1);

            // 使用缓动函数
            const easeOutCubic = 1 - Math.pow(1 - progress, 3);
            const currentPosition = startPosition * (1 - easeOutCubic);

            window.scrollTo(0, currentPosition);

            if (progress < 1) {
                requestAnimationFrame(animateScroll);
            } else {
                this.isScrolling = false;
            }
        };

        requestAnimationFrame(animateScroll);
    }

    /**
     * 创建粒子爆炸效果
     */
    createParticleExplosion() {
        const particlesContainer = this.button.querySelector('.rocket-particles');
        if (!particlesContainer) return;

        // 清除现有粒子
        particlesContainer.innerHTML = '';

        // 创建多个粒子
        const particleCount = 12;
        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';

            // 随机方向和距离
            const angle = (i / particleCount) * 2 * Math.PI;
            const distance = 40 + Math.random() * 20;
            const dx = Math.cos(angle) * distance;
            const dy = Math.sin(angle) * distance;

            particle.style.setProperty('--dx', `${dx}px`);
            particle.style.setProperty('--dy', `${dy}px`);

            particlesContainer.appendChild(particle);
        }
    }

    /**
     * 处理鼠标进入事件
     */
    handleMouseEnter() {
        const rocketIcon = this.button.querySelector('.rocket-icon');
        rocketIcon.style.transform = 'translateY(-2px)';
    }

    /**
     * 处理鼠标离开事件
     */
    handleMouseLeave() {
        const rocketIcon = this.button.querySelector('.rocket-icon');
        rocketIcon.style.transform = '';
    }

    /**
     * 节流函数
     */
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * 销毁组件
     */
    destroy() {
        if (this.button && this.button.parentNode) {
            this.button.parentNode.removeChild(this.button);
        }
        window.removeEventListener('scroll', this.handleScroll);
    }

    /**
     * 更新配置
     */
    updateOptions(newOptions) {
        this.options = { ...this.options, ...newOptions };
        this.checkScroll();
    }
}

// 自动初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否已经存在实例
    if (!window.backToTopInstance) {
        window.backToTopInstance = new BackToTop({
            showThreshold: 300,
            scrollDuration: 500,
            enableParticles: true
        });
    }
});

// 导出类供其他脚本使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = BackToTop;
} else {
    window.BackToTop = BackToTop;
}
