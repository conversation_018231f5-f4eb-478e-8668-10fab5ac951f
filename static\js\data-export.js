/**
 * 数据导出功能组件
 * 支持单个数据类型导出和批量导出
 */

class DataExporter {
    constructor(deviceId) {
        this.deviceId = deviceId;
        this.isExporting = false;
        this.supportedFormats = ['xlsx', 'csv'];
        this.supportedDataTypes = [
            { value: 'power', name: '功率数据', icon: 'fas fa-bolt' },
            { value: 'voltage', name: '电压数据', icon: 'fas fa-plug' },
            { value: 'temperature', name: '温度数据', icon: 'fas fa-thermometer-half' },
            { value: 'csq', name: '信号质量数据', icon: 'fas fa-signal' }
        ];
        
        this.initializeUI();
    }

    /**
     * 初始化用户界面
     */
    initializeUI() {
        // 创建导出按钮和模态框
        this.createExportButton();
        this.createExportModal();
        this.bindEvents();
    }

    /**
     * 创建导出按钮
     */
    createExportButton() {
        // 查找合适的位置插入导出按钮
        const controlsContainer = document.querySelector('.card-header .row .col-md-6:last-child');
        if (controlsContainer) {
            const exportButton = document.createElement('button');
            exportButton.className = 'btn btn-success btn-sm ms-2';
            exportButton.innerHTML = '<i class="fas fa-download me-1"></i>导出数据';
            exportButton.id = 'exportDataBtn';
            
            controlsContainer.appendChild(exportButton);
        }
    }

    /**
     * 创建导出模态框
     */
    createExportModal() {
        const modalHTML = `
        <div class="modal fade" id="exportModal" tabindex="-1" aria-labelledby="exportModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="exportModalLabel">
                            <i class="fas fa-download me-2"></i>数据导出
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <!-- 导出类型选择 -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">导出类型</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="exportType" id="singleExport" value="single" checked>
                                        <label class="form-check-label" for="singleExport">
                                            <i class="fas fa-file me-1"></i>单个数据类型导出
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="exportType" id="batchExport" value="batch">
                                        <label class="form-check-label" for="batchExport">
                                            <i class="fas fa-files me-1"></i>批量数据导出
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 数据类型选择 -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">数据类型</label>
                            <div id="singleDataTypeContainer">
                                <select class="form-select" id="singleDataType">
                                    ${this.supportedDataTypes.map(type => 
                                        `<option value="${type.value}"><i class="${type.icon}"></i> ${type.name}</option>`
                                    ).join('')}
                                </select>
                            </div>
                            <div id="batchDataTypeContainer" style="display: none;">
                                <div class="row">
                                    ${this.supportedDataTypes.map(type => `
                                        <div class="col-md-6 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="dataType_${type.value}" value="${type.value}">
                                                <label class="form-check-label" for="dataType_${type.value}">
                                                    <i class="${type.icon} me-1"></i>${type.name}
                                                </label>
                                            </div>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        </div>

                        <!-- 日期范围选择 -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">日期范围</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <label for="exportStartDate" class="form-label">开始日期</label>
                                    <input type="date" class="form-control" id="exportStartDate" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="exportEndDate" class="form-label">结束日期（可选）</label>
                                    <input type="date" class="form-control" id="exportEndDate">
                                </div>
                            </div>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                如果不选择结束日期，将只导出开始日期当天的数据。最大导出范围为30天。
                            </div>
                        </div>

                        <!-- 文件格式选择 -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">文件格式</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="exportFormat" id="formatXlsx" value="xlsx" checked>
                                        <label class="form-check-label" for="formatXlsx">
                                            <i class="fas fa-file-excel me-1 text-success"></i>Excel (.xlsx)
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="exportFormat" id="formatCsv" value="csv">
                                        <label class="form-check-label" for="formatCsv">
                                            <i class="fas fa-file-csv me-1 text-info"></i>CSV (.csv)
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 导出进度 -->
                        <div id="exportProgress" style="display: none;">
                            <div class="mb-3">
                                <label class="form-label">导出进度</label>
                                <div class="progress">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                         role="progressbar" style="width: 0%" id="exportProgressBar">
                                        <span id="exportProgressText">0%</span>
                                    </div>
                                </div>
                            </div>
                            <div id="exportStatus" class="text-muted">
                                <i class="fas fa-spinner fa-spin me-1"></i>准备导出...
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" id="startExportBtn">
                            <i class="fas fa-download me-1"></i>开始导出
                        </button>
                    </div>
                </div>
            </div>
        </div>
        `;

        // 添加模态框到页面
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        
        // 设置默认日期为今天
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('exportStartDate').value = today;
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 导出按钮点击事件
        document.getElementById('exportDataBtn')?.addEventListener('click', () => {
            this.showExportModal();
        });

        // 导出类型切换事件
        document.querySelectorAll('input[name="exportType"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                this.toggleExportType(e.target.value);
            });
        });

        // 开始导出按钮事件
        document.getElementById('startExportBtn')?.addEventListener('click', () => {
            this.startExport();
        });
    }

    /**
     * 显示导出模态框
     */
    showExportModal() {
        const modal = new bootstrap.Modal(document.getElementById('exportModal'));
        modal.show();
    }

    /**
     * 切换导出类型
     */
    toggleExportType(type) {
        const singleContainer = document.getElementById('singleDataTypeContainer');
        const batchContainer = document.getElementById('batchDataTypeContainer');

        if (type === 'single') {
            singleContainer.style.display = 'block';
            batchContainer.style.display = 'none';
        } else {
            singleContainer.style.display = 'none';
            batchContainer.style.display = 'block';
        }
    }

    /**
     * 开始导出
     */
    async startExport() {
        if (this.isExporting) {
            return;
        }

        try {
            // 验证输入
            const validation = this.validateInput();
            if (!validation.valid) {
                alert(validation.message);
                return;
            }

            this.isExporting = true;
            this.showProgress();

            const exportType = document.querySelector('input[name="exportType"]:checked').value;
            const format = document.querySelector('input[name="exportFormat"]:checked').value;
            const startDate = document.getElementById('exportStartDate').value;
            const endDate = document.getElementById('exportEndDate').value;

            if (exportType === 'single') {
                await this.exportSingleData(format, startDate, endDate);
            } else {
                await this.exportBatchData(format, startDate, endDate);
            }

        } catch (error) {
            console.error('导出失败:', error);
            this.showError('导出失败: ' + error.message);
        } finally {
            this.isExporting = false;
            this.hideProgress();
        }
    }

    /**
     * 验证输入
     */
    validateInput() {
        const startDate = document.getElementById('exportStartDate').value;
        if (!startDate) {
            return { valid: false, message: '请选择开始日期' };
        }

        const exportType = document.querySelector('input[name="exportType"]:checked').value;
        if (exportType === 'batch') {
            const selectedTypes = Array.from(document.querySelectorAll('#batchDataTypeContainer input[type="checkbox"]:checked'));
            if (selectedTypes.length === 0) {
                return { valid: false, message: '请至少选择一种数据类型' };
            }
        }

        // 验证日期范围
        const endDate = document.getElementById('exportEndDate').value;
        if (endDate && endDate < startDate) {
            return { valid: false, message: '结束日期不能早于开始日期' };
        }

        if (endDate) {
            const daysDiff = (new Date(endDate) - new Date(startDate)) / (1000 * 60 * 60 * 24);
            if (daysDiff > 30) {
                return { valid: false, message: '导出日期范围不能超过30天' };
            }
        }

        return { valid: true };
    }

    /**
     * 导出单个数据类型
     */
    async exportSingleData(format, startDate, endDate) {
        const dataType = document.getElementById('singleDataType').value;

        this.updateProgress(20, '准备导出数据...');

        const response = await fetch(`/debug_script/export_single/${this.deviceId}/${dataType}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                start_date: startDate,
                end_date: endDate,
                format: format
            })
        });

        this.updateProgress(80, '生成文件中...');

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || '导出失败');
        }

        this.updateProgress(100, '下载文件...');

        // 下载文件
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = this.getFilenameFromResponse(response) || `${this.deviceId}_${dataType}_${startDate}.${format}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        this.showSuccess('数据导出成功！');
        this.closeModal();
    }

    /**
     * 批量导出数据
     */
    async exportBatchData(format, startDate, endDate) {
        const selectedTypes = Array.from(document.querySelectorAll('#batchDataTypeContainer input[type="checkbox"]:checked'))
            .map(checkbox => checkbox.value);

        this.updateProgress(20, '准备导出数据...');

        const response = await fetch(`/debug_script/export_batch/${this.deviceId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                data_types: selectedTypes,
                start_date: startDate,
                end_date: endDate,
                format: format
            })
        });

        this.updateProgress(80, '生成文件中...');

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || '批量导出失败');
        }

        this.updateProgress(100, '下载文件...');

        // 下载文件
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = this.getFilenameFromResponse(response) || `${this.deviceId}_batch_${startDate}.${format}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        this.showSuccess('批量数据导出成功！');
        this.closeModal();
    }

    /**
     * 显示进度
     */
    showProgress() {
        document.getElementById('exportProgress').style.display = 'block';
        document.getElementById('startExportBtn').disabled = true;
        document.getElementById('startExportBtn').innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>导出中...';
    }

    /**
     * 隐藏进度
     */
    hideProgress() {
        document.getElementById('exportProgress').style.display = 'none';
        document.getElementById('startExportBtn').disabled = false;
        document.getElementById('startExportBtn').innerHTML = '<i class="fas fa-download me-1"></i>开始导出';
    }

    /**
     * 更新进度
     */
    updateProgress(percent, message) {
        const progressBar = document.getElementById('exportProgressBar');
        const progressText = document.getElementById('exportProgressText');
        const statusText = document.getElementById('exportStatus');

        progressBar.style.width = percent + '%';
        progressText.textContent = percent + '%';
        statusText.innerHTML = `<i class="fas fa-spinner fa-spin me-1"></i>${message}`;
    }

    /**
     * 显示成功消息
     */
    showSuccess(message) {
        const statusText = document.getElementById('exportStatus');
        statusText.innerHTML = `<i class="fas fa-check-circle text-success me-1"></i>${message}`;
    }

    /**
     * 显示错误消息
     */
    showError(message) {
        const statusText = document.getElementById('exportStatus');
        statusText.innerHTML = `<i class="fas fa-exclamation-circle text-danger me-1"></i>${message}`;
    }

    /**
     * 从响应头获取文件名
     */
    getFilenameFromResponse(response) {
        const contentDisposition = response.headers.get('Content-Disposition');
        if (contentDisposition) {
            const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
            if (filenameMatch && filenameMatch[1]) {
                return filenameMatch[1].replace(/['"]/g, '');
            }
        }
        return null;
    }

    /**
     * 关闭模态框
     */
    closeModal() {
        setTimeout(() => {
            const modal = bootstrap.Modal.getInstance(document.getElementById('exportModal'));
            if (modal) {
                modal.hide();
            }
        }, 2000);
    }
}

// 自动初始化（如果页面包含设备ID）
document.addEventListener('DOMContentLoaded', function() {
    // 从页面中获取设备ID（假设在某个元素中）
    const deviceIdElement = document.querySelector('[data-device-id]');
    if (deviceIdElement) {
        const deviceId = deviceIdElement.getAttribute('data-device-id');
        window.dataExporter = new DataExporter(deviceId);
    }
});

// 导出类
window.DataExporter = DataExporter;
