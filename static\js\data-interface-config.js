/**
 * 统一的数据接口配置
 * 定义各种数据类型的配置和格式化规则
 */

const DataInterfaceConfig = {
    // 数据类型配置
    dataTypes: {
        power: {
            name: '功率',
            unit: 'W',
            color: '#007bff',
            hasChannels: true,
            channels: 10,
            apiEndpoint: 'power_data',
            chartType: 'line',
            yAxisMin: null,
            yAxisMax: null,
            decimals: 2
        },
        voltage: {
            name: '电压',
            unit: 'V',
            color: '#28a745',
            hasChannels: false,
            apiEndpoint: 'voltage_data',
            chartType: 'line',
            yAxisMin: 0,
            yAxisMax: 300,
            decimals: 2
        },
        temperature: {
            name: '温度',
            unit: '°C',
            color: '#dc3545',
            hasChannels: false,
            apiEndpoint: 'temperature_data',
            chartType: 'line',
            yAxisMin: -20,
            yAxisMax: 80,
            decimals: 1
        },
        csq: {
            name: '信号质量',
            unit: '',
            color: '#ffc107',
            hasChannels: false,
            apiEndpoint: 'csq_data',
            chartType: 'line',
            yAxisMin: 0,
            yAxisMax: 31,
            decimals: 0,
            hasSecondaryValue: true,
            secondaryValueName: 'BER'
        }
    },

    // 时间范围配置
    timeRanges: {
        all: { name: '全天', hours: null },
        morning: { name: '上午 (6:00-12:00)', hours: [6, 12] },
        afternoon: { name: '下午 (12:00-18:00)', hours: [12, 18] },
        evening: { name: '晚上 (18:00-24:00)', hours: [18, 24] },
        night: { name: '夜间 (0:00-6:00)', hours: [0, 6] }
    },

    // 图表配置
    chartConfig: {
        responsive: true,
        maintainAspectRatio: false,
        interaction: {
            mode: 'index',
            intersect: false,
        },
        plugins: {
            legend: {
                display: true,
                position: 'top'
            },
            tooltip: {
                mode: 'index',
                intersect: false,
                callbacks: {
                    title: function(context) {
                        const date = new Date(context[0].parsed.x);
                        return date.toLocaleString('zh-CN');
                    }
                }
            },
            zoom: {
                pan: {
                    enabled: true,
                    mode: 'xy'
                },
                zoom: {
                    wheel: {
                        enabled: true
                    },
                    pinch: {
                        enabled: true
                    },
                    mode: 'xy'
                }
            }
        },
        scales: {
            x: {
                type: 'time',
                time: {
                    displayFormats: {
                        hour: 'HH:mm',
                        minute: 'HH:mm'
                    }
                },
                title: {
                    display: true,
                    text: '时间'
                }
            },
            y: {
                beginAtZero: false,
                title: {
                    display: true,
                    text: '数值'
                }
            }
        }
    },

    // 数据格式化函数
    formatters: {
        /**
         * 格式化数值
         */
        formatValue: function(value, dataType) {
            if (value === null || value === undefined) {
                return 'N/A';
            }
            
            const config = this.dataTypes[dataType];
            if (!config) {
                return value.toString();
            }
            
            const decimals = config.decimals || 2;
            return Number(value).toFixed(decimals) + (config.unit ? ' ' + config.unit : '');
        },

        /**
         * 格式化时间
         */
        formatTime: function(timestamp) {
            const date = new Date(timestamp);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        },

        /**
         * 格式化日期
         */
        formatDate: function(timestamp) {
            const date = new Date(timestamp);
            return date.toLocaleDateString('zh-CN');
        },

        /**
         * 格式化信号质量
         */
        formatCSQ: function(csq) {
            if (csq === null || csq === undefined) {
                return { quality: '未知', dbm: '未知' };
            }
            
            let quality = '';
            if (csq >= 20) quality = '优';
            else if (csq >= 15) quality = '良';
            else if (csq >= 10) quality = '中';
            else quality = '差';

            let dbm = '';
            if (csq === 0) dbm = '≤-115dBm';
            else if (csq === 1) dbm = '-111dBm';
            else if (csq >= 2 && csq <= 30) dbm = `-${113 - (csq * 2)}dBm`;
            else if (csq === 31) dbm = '≥-51dBm';
            else dbm = '未知';

            return { quality, dbm };
        }
    },

    // 数据验证函数
    validators: {
        /**
         * 验证数据格式
         */
        validateData: function(data, dataType) {
            if (!data || !Array.isArray(data)) {
                return { valid: false, error: '数据格式无效' };
            }

            if (data.length === 0) {
                return { valid: false, error: '没有数据' };
            }

            // 检查数据结构
            for (let i = 0; i < Math.min(data.length, 10); i++) {
                const item = data[i];
                if (!item.time || item.value === undefined) {
                    return { valid: false, error: '数据结构无效' };
                }
            }

            return { valid: true };
        },

        /**
         * 验证日期格式
         */
        validateDate: function(dateString) {
            const regex = /^\d{4}-\d{2}-\d{2}$/;
            if (!regex.test(dateString)) {
                return { valid: false, error: '日期格式应为YYYY-MM-DD' };
            }

            const date = new Date(dateString);
            if (isNaN(date.getTime())) {
                return { valid: false, error: '无效的日期' };
            }

            return { valid: true };
        }
    },

    // 工具函数
    utils: {
        /**
         * 过滤时间范围数据
         */
        filterByTimeRange: function(data, timeRange) {
            if (!timeRange || timeRange === 'all') {
                return data;
            }

            const range = this.timeRanges[timeRange];
            if (!range || !range.hours) {
                return data;
            }

            const [startHour, endHour] = range.hours;
            
            return data.filter(point => {
                const hour = new Date(point.time).getHours();
                return hour >= startHour && hour < endHour;
            });
        },

        /**
         * 计算统计信息
         */
        calculateStats: function(data, dataType) {
            if (!data || data.length === 0) {
                return {
                    count: 0,
                    min: null,
                    max: null,
                    avg: null,
                    latest: null
                };
            }

            const values = data.map(item => item.value).filter(v => v !== null && v !== undefined);
            
            if (values.length === 0) {
                return {
                    count: data.length,
                    min: null,
                    max: null,
                    avg: null,
                    latest: null
                };
            }

            const min = Math.min(...values);
            const max = Math.max(...values);
            const avg = values.reduce((sum, val) => sum + val, 0) / values.length;
            const latest = data[data.length - 1];

            return {
                count: data.length,
                min: min,
                max: max,
                avg: avg,
                latest: latest ? latest.value : null,
                latestTime: latest ? latest.time : null
            };
        },

        /**
         * 生成颜色数组
         */
        generateColors: function(count) {
            const baseColors = [
                '#007bff', '#28a745', '#dc3545', '#ffc107', '#6f42c1',
                '#fd7e14', '#20c997', '#6c757d', '#e83e8c', '#17a2b8'
            ];

            const colors = [];
            for (let i = 0; i < count; i++) {
                colors.push(baseColors[i % baseColors.length]);
            }

            return colors;
        },

        /**
         * 防抖函数
         */
        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    }
};

// 导出配置
window.DataInterfaceConfig = DataInterfaceConfig;
