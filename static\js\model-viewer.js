// 3D模型查看器脚本

document.addEventListener('DOMContentLoaded', () => {
    const uploadForm = document.getElementById('uploadForm');
    const modelFileInput = document.getElementById('modelFile');
    const uploadStatus = document.getElementById('uploadStatus');
    const fileList = document.getElementById('fileList');
    const modelViewer = document.getElementById('modelViewer');

    // 文件大小限制（100MB）
    const MAX_FILE_SIZE = 100 * 1024 * 1024;

    // 允许的文件类型
    const ALLOWED_TYPES = ['.gltf', '.glb'];

    // 文件列表存储
    let files = [];

    // 移除之前的事件监听器，避免重复添加
    const removePreviousListeners = () => {
        modelViewer.removeEventListener('load', loadHandler);
        modelViewer.removeEventListener('error', errorHandler);
    };

    const loadHandler = () => {
        console.log('模型加载成功:', currentFileName);
        uploadStatus.innerHTML += `<div class="alert alert-info">模型 ${currentFileName} 加载成功</div>`;
    };

    const errorHandler = (error) => {
        console.error('模型加载失败:', currentFileName, error);
        let errorMessage = '未知错误';
        if (error.detail) {
            errorMessage = error.detail.message || JSON.stringify(error.detail);
        } else if (error.message) {
            errorMessage = error.message;
        }
        uploadStatus.innerHTML += `<div class="alert alert-danger">模型 ${currentFileName} 加载失败: ${errorMessage}</div>`;
    };

    let currentFileName = '';

    // 获取已上传的模型列表
    async function loadModelList() {
        try {
            const response = await fetch('/api/models');
            if (!response.ok) {
                throw new Error('获取模型列表失败');
            }
            const models = await response.json();
            files = models;
            updateFileList();
        } catch (error) {
            console.error('加载模型列表失败:', error);
            uploadStatus.innerHTML = `<div class="alert alert-danger">加载模型列表失败: ${error.message}</div>`;
        }
    }

    // 页面加载时获取模型列表
    loadModelList();

    uploadForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        const fileInput = modelFileInput.files;
        if (fileInput.length === 0) {
            uploadStatus.innerHTML = '<div class="alert alert-warning">请选择文件</div>';
            return;
        }

        uploadStatus.innerHTML = '';
        let hasError = false;

        for (let i = 0; i < fileInput.length; i++) {
            const file = fileInput[i];
            const fileExtension = file.name.slice(file.name.lastIndexOf('.'));

            // 检查文件类型
            if (!ALLOWED_TYPES.includes(fileExtension.toLowerCase())) {
                uploadStatus.innerHTML += `<div class="alert alert-danger">文件 ${file.name} 类型不受支持，仅支持 glTF, GLB 格式</div>`;
                hasError = true;
                continue;
            }

            // 检查文件大小
            if (file.size > MAX_FILE_SIZE) {
                uploadStatus.innerHTML += `<div class="alert alert-danger">文件 ${file.name} 太大，最大允许 100MB</div>`;
                hasError = true;
                continue;
            }

            try {
                // 创建 FormData 对象
                const formData = new FormData();
                formData.append('file', file);

                // 发送文件到服务器
                const response = await fetch('/api/upload-model', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error('上传失败');
                }

                const result = await response.json();
                
                // 存储文件信息
                files.push({
                    name: file.name,
                    size: file.size,
                    path: result.path
                });
                
                updateFileList();
                uploadStatus.innerHTML += `<div class="alert alert-success">文件 ${file.name} 上传成功</div>`;
            } catch (error) {
                uploadStatus.innerHTML += `<div class="alert alert-danger">文件 ${file.name} 上传失败: ${error.message}</div>`;
                hasError = true;
            }
        }

        if (!hasError) {
            uploadForm.reset();
        }
    });

    function updateFileList() {
        fileList.innerHTML = '';
        files.forEach((file, index) => {
            const li = document.createElement('li');
            li.className = 'list-group-item';
            li.innerHTML = `
                <span>${file.name} (${(file.size / 1024).toFixed(2)} KB)</span>
                <button class="btn btn-sm btn-outline-primary" onclick="viewModel(${index})">查看</button>
            `;
            fileList.appendChild(li);
        });
    }

    window.viewModel = function(index) {
        const file = files[index];
        modelViewer.src = file.path;
        modelViewer.alt = file.name;
        
        // 设置光照以确保模型可见
        modelViewer.environmentImage = 'neutral';
        modelViewer.shadowIntensity = 1.0;
        
        // 监听模型加载事件
        removePreviousListeners();
        currentFileName = file.name;
        modelViewer.addEventListener('load', loadHandler);
        modelViewer.addEventListener('error', errorHandler);
    };
});
