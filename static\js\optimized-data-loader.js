/**
 * 优化的数据加载器
 * 支持分页、数据摘要和按需加载
 */

class OptimizedDataLoader {
    constructor(deviceId) {
        this.deviceId = deviceId;
        this.cache = new Map();
        this.loadingStates = new Set();
        this.abortControllers = new Map();
    }

    /**
     * 加载数据（智能选择摘要或详细数据）
     */
    async loadData(dataType, date, options = {}) {
        const {
            useSummary = true,
            summaryInterval = 60,
            pageSize = 1000,
            forceRefresh = false
        } = options;

        // 生成缓存键
        const cacheKey = `${dataType}_${date}_${useSummary ? 'summary' : 'detail'}`;
        
        // 检查缓存
        if (!forceRefresh && this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }

        // 检查是否正在加载
        if (this.loadingStates.has(cacheKey)) {
            return null;
        }

        this.loadingStates.add(cacheKey);

        try {
            let data;
            
            if (useSummary) {
                // 首先尝试加载摘要数据
                data = await this.loadSummaryData(dataType, date, summaryInterval);
                
                // 如果摘要数据点数过少，则加载详细数据
                if (!data || data.length < 10) {
                    data = await this.loadDetailedData(dataType, date, pageSize);
                }
            } else {
                // 直接加载详细数据
                data = await this.loadDetailedData(dataType, date, pageSize);
            }

            // 缓存结果
            if (data) {
                this.cache.set(cacheKey, data);
            }

            return data;

        } finally {
            this.loadingStates.delete(cacheKey);
        }
    }

    /**
     * 加载摘要数据
     */
    async loadSummaryData(dataType, date, interval = 60) {
        const url = `/debug_script/data_summary/${this.deviceId}/${dataType}?date=${date}&interval=${interval}`;
        
        try {
            const response = await this.fetchWithTimeout(url);
            const data = await response.json();
            
            if (data.error) {
                throw new Error(data.error);
            }
            
            return data.summary_data.map(item => ({
                time: item.time,
                value: item.avg_value,
                min_value: item.min_value,
                max_value: item.max_value,
                count: item.count,
                isSummary: true
            }));
        } catch (error) {
            console.warn(`加载${dataType}摘要数据失败:`, error);
            return null;
        }
    }

    /**
     * 加载详细数据
     */
    async loadDetailedData(dataType, date, pageSize = 1000) {
        // 对于功率数据，使用特殊的API
        if (dataType === 'power') {
            return await this.loadPowerData(date);
        }

        const url = `/debug_script/${dataType}_data/${this.deviceId}?date=${date}`;
        
        try {
            const response = await this.fetchWithTimeout(url);
            const data = await response.json();
            
            if (data.error) {
                throw new Error(data.error);
            }
            
            const dataKey = `${dataType}_data`;
            return data[dataKey] || [];
        } catch (error) {
            console.error(`加载${dataType}详细数据失败:`, error);
            throw error;
        }
    }

    /**
     * 加载功率数据（特殊处理）
     */
    async loadPowerData(date) {
        const url = `/debug_script/power_data/${this.deviceId}?date=${date}`;
        
        try {
            const response = await this.fetchWithTimeout(url);
            const data = await response.json();
            
            if (data.error) {
                throw new Error(data.error);
            }
            
            return data.power_data || {};
        } catch (error) {
            console.error('加载功率详细数据失败:', error);
            throw error;
        }
    }

    /**
     * 分页加载数据
     */
    async loadPaginatedData(dataType, date, page = 1, pageSize = 1000) {
        const url = `/debug_script/data_paginated/${this.deviceId}/${dataType}?date=${date}&page=${page}&page_size=${pageSize}`;
        
        try {
            const response = await this.fetchWithTimeout(url);
            const data = await response.json();
            
            if (data.error) {
                throw new Error(data.error);
            }
            
            return {
                data: data.data || [],
                pagination: data.pagination || {}
            };
        } catch (error) {
            console.error(`分页加载${dataType}数据失败:`, error);
            throw error;
        }
    }

    /**
     * 获取数据统计信息
     */
    async getDataStatistics() {
        const url = `/debug_script/data_statistics/${this.deviceId}`;
        
        try {
            const response = await this.fetchWithTimeout(url);
            const data = await response.json();
            
            if (data.error) {
                throw new Error(data.error);
            }
            
            return data.statistics || {};
        } catch (error) {
            console.error('获取数据统计失败:', error);
            throw error;
        }
    }

    /**
     * 带超时的fetch请求
     */
    async fetchWithTimeout(url, timeout = 30000) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);
        
        // 存储控制器以便可以取消
        this.abortControllers.set(url, controller);
        
        try {
            const response = await fetch(url, {
                signal: controller.signal
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            return response;
        } finally {
            clearTimeout(timeoutId);
            this.abortControllers.delete(url);
        }
    }

    /**
     * 取消所有正在进行的请求
     */
    cancelAllRequests() {
        for (const controller of this.abortControllers.values()) {
            controller.abort();
        }
        this.abortControllers.clear();
        this.loadingStates.clear();
    }

    /**
     * 清除缓存
     */
    clearCache() {
        this.cache.clear();
    }

    /**
     * 获取缓存大小
     */
    getCacheSize() {
        return this.cache.size;
    }
}

/**
 * 图表性能优化器
 */
class ChartOptimizer {
    constructor() {
        this.maxDataPoints = 2000; // 最大数据点数
        this.decimationThreshold = 5000; // 数据抽取阈值
    }

    /**
     * 优化数据点数量
     */
    optimizeDataPoints(data, maxPoints = this.maxDataPoints) {
        if (!Array.isArray(data) || data.length <= maxPoints) {
            return data;
        }

        // 计算抽取步长
        const step = Math.ceil(data.length / maxPoints);
        
        // 使用LTTB算法进行数据抽取（简化版）
        return this.downsampleLTTB(data, maxPoints);
    }

    /**
     * LTTB (Largest Triangle Three Buckets) 数据抽取算法
     */
    downsampleLTTB(data, threshold) {
        if (data.length <= threshold) {
            return data;
        }

        const sampled = [];
        const bucketSize = (data.length - 2) / (threshold - 2);
        
        // 第一个点
        sampled.push(data[0]);
        
        for (let i = 1; i < threshold - 1; i++) {
            const bucketStart = Math.floor(i * bucketSize) + 1;
            const bucketEnd = Math.floor((i + 1) * bucketSize) + 1;
            
            // 找到桶中的最佳点
            let maxArea = -1;
            let maxAreaIndex = bucketStart;
            
            for (let j = bucketStart; j < bucketEnd && j < data.length; j++) {
                const area = this.calculateTriangleArea(
                    sampled[sampled.length - 1],
                    data[j],
                    data[Math.min(bucketEnd, data.length - 1)]
                );
                
                if (area > maxArea) {
                    maxArea = area;
                    maxAreaIndex = j;
                }
            }
            
            sampled.push(data[maxAreaIndex]);
        }
        
        // 最后一个点
        sampled.push(data[data.length - 1]);
        
        return sampled;
    }

    /**
     * 计算三角形面积
     */
    calculateTriangleArea(a, b, c) {
        const aTime = new Date(a.time).getTime();
        const bTime = new Date(b.time).getTime();
        const cTime = new Date(c.time).getTime();
        
        return Math.abs(
            (aTime - cTime) * (b.value - a.value) - 
            (aTime - bTime) * (c.value - a.value)
        ) * 0.5;
    }
}

// 导出类
window.OptimizedDataLoader = OptimizedDataLoader;
window.ChartOptimizer = ChartOptimizer;
