/**
 * Theme Switcher for OTA Device Management System
 * Supports switching between default and liquid glass themes
 * Author: AI Assistant
 * Version: 1.0.0
 */

class ThemeSwitcher {
    constructor() {
        this.themes = {
            default: {
                name: '默认主题',
                class: '',
                icon: '🎨',
                description: '经典的Bootstrap主题'
            },
            liquidGlass: {
                name: '液态玻璃',
                class: 'liquid-glass-theme',
                icon: '💎',
                description: 'iOS 26风格的液态玻璃效果'
            }
        };

        this.currentTheme = this.getStoredTheme() || 'default';
        this.isTransitioning = false;
        this.performanceMode = this.detectPerformanceMode();

        this.init();
    }
    
    init() {
        this.loadThemeCSS();
        this.createThemeSwitcher();
        this.applyTheme(this.currentTheme, false);
        this.bindEvents();
    }
    
    /**
     * 动态加载主题CSS文件
     */
    loadThemeCSS() {
        // 加载主要的液态玻璃主题CSS
        const liquidGlassCSS = document.createElement('link');
        liquidGlassCSS.rel = 'stylesheet';
        liquidGlassCSS.href = '/static/css/themes/liquid-glass.css';
        liquidGlassCSS.id = 'liquid-glass-theme-css';
        document.head.appendChild(liquidGlassCSS);

        // 加载组件样式CSS
        const componentsCSS = document.createElement('link');
        componentsCSS.rel = 'stylesheet';
        componentsCSS.href = '/static/css/themes/liquid-glass-components.css';
        componentsCSS.id = 'liquid-glass-components-css';
        document.head.appendChild(componentsCSS);

        // 加载响应式样式CSS
        const responsiveCSS = document.createElement('link');
        responsiveCSS.rel = 'stylesheet';
        responsiveCSS.href = '/static/css/themes/liquid-glass-responsive.css';
        responsiveCSS.id = 'liquid-glass-responsive-css';
        document.head.appendChild(responsiveCSS);

        // 加载兼容性样式CSS
        const compatibilityCSS = document.createElement('link');
        compatibilityCSS.rel = 'stylesheet';
        compatibilityCSS.href = '/static/css/themes/liquid-glass-compatibility.css';
        compatibilityCSS.id = 'liquid-glass-compatibility-css';
        document.head.appendChild(compatibilityCSS);
    }
    
    /**
     * 创建主题切换器UI
     */
    createThemeSwitcher() {
        // 创建主题切换按钮
        const switcherContainer = document.createElement('div');
        switcherContainer.className = 'theme-switcher-container';
        switcherContainer.innerHTML = `
            <div class="theme-switcher">
                <button class="theme-toggle-btn" id="themeToggleBtn" title="切换主题">
                    <span class="theme-icon">${this.themes[this.currentTheme].icon}</span>
                    <span class="theme-name">${this.themes[this.currentTheme].name}</span>
                </button>
                <div class="theme-dropdown" id="themeDropdown">
                    ${Object.entries(this.themes).map(([key, theme]) => `
                        <div class="theme-option ${key === this.currentTheme ? 'active' : ''}" 
                             data-theme="${key}">
                            <span class="theme-option-icon">${theme.icon}</span>
                            <div class="theme-option-info">
                                <div class="theme-option-name">${theme.name}</div>
                                <div class="theme-option-desc">${theme.description}</div>
                            </div>
                            ${key === this.currentTheme ? '<i class="fas fa-check"></i>' : ''}
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
        
        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            .theme-switcher-container {
                position: fixed;
                bottom: 20px;
                right: 20px;
                z-index: 1050;
            }
            
            .theme-switcher {
                position: relative;
            }
            
            .theme-toggle-btn {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 12px 16px;
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(20px);
                -webkit-backdrop-filter: blur(20px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 25px;
                color: rgba(255, 255, 255, 0.9);
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.3s ease;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
                min-width: 120px;
            }
            
            .theme-toggle-btn:hover {
                background: rgba(255, 255, 255, 0.15);
                transform: translateY(-2px);
                box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);
            }
            
            .theme-icon {
                font-size: 18px;
            }
            
            .theme-dropdown {
                position: absolute;
                bottom: 100%;
                right: 0;
                margin-bottom: 10px;
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(20px);
                -webkit-backdrop-filter: blur(20px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 15px;
                padding: 8px;
                min-width: 280px;
                opacity: 0;
                visibility: hidden;
                transform: translateY(10px);
                transition: all 0.3s ease;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
            }
            
            .theme-dropdown.show {
                opacity: 1;
                visibility: visible;
                transform: translateY(0);
            }
            
            .theme-option {
                display: flex;
                align-items: center;
                gap: 12px;
                padding: 12px;
                border-radius: 10px;
                cursor: pointer;
                transition: all 0.3s ease;
                position: relative;
            }
            
            .theme-option:hover {
                background: rgba(255, 255, 255, 0.1);
                transform: translateX(5px);
            }
            
            .theme-option.active {
                background: rgba(102, 126, 234, 0.3);
                border: 1px solid rgba(102, 126, 234, 0.5);
            }
            
            .theme-option-icon {
                font-size: 20px;
                width: 24px;
                text-align: center;
            }
            
            .theme-option-info {
                flex: 1;
            }
            
            .theme-option-name {
                color: rgba(255, 255, 255, 0.9);
                font-weight: 500;
                margin-bottom: 2px;
            }
            
            .theme-option-desc {
                color: rgba(255, 255, 255, 0.6);
                font-size: 12px;
            }
            
            .theme-option .fas {
                color: rgba(102, 126, 234, 0.8);
            }
            
            /* Dark mode adjustments */
            body.dark-mode .theme-toggle-btn,
            body.dark-mode .theme-dropdown {
                background: rgba(30, 30, 30, 0.8);
                border-color: rgba(255, 255, 255, 0.1);
            }
            
            /* Transition overlay */
            .theme-transition-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: radial-gradient(circle at center, 
                    rgba(102, 126, 234, 0.3) 0%, 
                    rgba(118, 75, 162, 0.3) 50%, 
                    rgba(255, 255, 255, 0.1) 100%);
                backdrop-filter: blur(10px);
                -webkit-backdrop-filter: blur(10px);
                z-index: 9999;
                opacity: 0;
                visibility: hidden;
                transition: all 0.5s ease;
                pointer-events: none;
            }
            
            .theme-transition-overlay.active {
                opacity: 1;
                visibility: visible;
            }
            
            .theme-transition-overlay .transition-content {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                text-align: center;
                color: rgba(255, 255, 255, 0.9);
            }
            
            .theme-transition-overlay .transition-icon {
                font-size: 4rem;
                margin-bottom: 20px;
                animation: pulse 1.5s ease-in-out infinite;
            }
            
            @keyframes pulse {
                0%, 100% { transform: scale(1); opacity: 0.8; }
                50% { transform: scale(1.1); opacity: 1; }
            }
        `;
        
        document.head.appendChild(style);
        document.body.appendChild(switcherContainer);
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        const toggleBtn = document.getElementById('themeToggleBtn');
        const dropdown = document.getElementById('themeDropdown');
        
        // 切换下拉菜单
        toggleBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            dropdown.classList.toggle('show');
        });
        
        // 点击外部关闭下拉菜单
        document.addEventListener('click', () => {
            dropdown.classList.remove('show');
        });
        
        // 主题选择
        dropdown.addEventListener('click', (e) => {
            e.stopPropagation();
            const option = e.target.closest('.theme-option');
            if (option) {
                const theme = option.dataset.theme;
                if (theme !== this.currentTheme) {
                    this.switchTheme(theme);
                }
                dropdown.classList.remove('show');
            }
        });
        
        // 键盘快捷键 (Ctrl/Cmd + Shift + T)
        document.addEventListener('keydown', (e) => {
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'T') {
                e.preventDefault();
                const themes = Object.keys(this.themes);
                const currentIndex = themes.indexOf(this.currentTheme);
                const nextIndex = (currentIndex + 1) % themes.length;
                this.switchTheme(themes[nextIndex]);
            }
        });
    }
    
    /**
     * 切换主题
     */
    switchTheme(theme) {
        if (this.isTransitioning || theme === this.currentTheme) return;
        
        this.isTransitioning = true;
        this.showTransitionOverlay(theme);
        
        setTimeout(() => {
            this.applyTheme(theme);
            this.currentTheme = theme;
            this.storeTheme(theme);
            this.updateSwitcherUI();
            
            setTimeout(() => {
                this.hideTransitionOverlay();
                this.isTransitioning = false;
            }, 300);
        }, 250);
    }
    
    /**
     * 应用主题
     */
    applyTheme(theme, animate = true) {
        const body = document.body;

        // 移除所有主题类
        Object.values(this.themes).forEach(t => {
            if (t.class) body.classList.remove(t.class);
        });

        // 添加新主题类
        if (this.themes[theme].class) {
            body.classList.add(this.themes[theme].class);

            // 如果是液态玻璃主题，应用性能优化
            if (theme === 'liquidGlass') {
                this.applyPerformanceOptimizations();
            }
        }

        // 触发自定义事件
        const event = new CustomEvent('themeChanged', {
            detail: { theme, themeData: this.themes[theme] }
        });
        document.dispatchEvent(event);
    }
    
    /**
     * 显示过渡动画
     */
    showTransitionOverlay(theme) {
        const overlay = document.createElement('div');
        overlay.className = 'theme-transition-overlay';
        overlay.innerHTML = `
            <div class="transition-content">
                <div class="transition-icon">${this.themes[theme].icon}</div>
                <h4>切换到 ${this.themes[theme].name}</h4>
                <p>${this.themes[theme].description}</p>
            </div>
        `;
        
        document.body.appendChild(overlay);
        
        // 触发动画
        requestAnimationFrame(() => {
            overlay.classList.add('active');
        });
        
        this.transitionOverlay = overlay;
    }
    
    /**
     * 隐藏过渡动画
     */
    hideTransitionOverlay() {
        if (this.transitionOverlay) {
            this.transitionOverlay.classList.remove('active');
            setTimeout(() => {
                if (this.transitionOverlay && this.transitionOverlay.parentNode) {
                    this.transitionOverlay.parentNode.removeChild(this.transitionOverlay);
                }
                this.transitionOverlay = null;
            }, 500);
        }
    }
    
    /**
     * 更新切换器UI
     */
    updateSwitcherUI() {
        const toggleBtn = document.getElementById('themeToggleBtn');
        const dropdown = document.getElementById('themeDropdown');
        
        if (toggleBtn) {
            const icon = toggleBtn.querySelector('.theme-icon');
            const name = toggleBtn.querySelector('.theme-name');
            
            icon.textContent = this.themes[this.currentTheme].icon;
            name.textContent = this.themes[this.currentTheme].name;
        }
        
        if (dropdown) {
            // 更新选中状态
            dropdown.querySelectorAll('.theme-option').forEach(option => {
                const theme = option.dataset.theme;
                const isActive = theme === this.currentTheme;
                
                option.classList.toggle('active', isActive);
                
                const checkIcon = option.querySelector('.fas');
                if (isActive && !checkIcon) {
                    option.innerHTML += '<i class="fas fa-check"></i>';
                } else if (!isActive && checkIcon) {
                    checkIcon.remove();
                }
            });
        }
    }
    
    /**
     * 存储主题设置
     */
    storeTheme(theme) {
        try {
            localStorage.setItem('ota-theme', theme);
        } catch (e) {
            console.warn('无法保存主题设置:', e);
        }
    }
    
    /**
     * 获取存储的主题设置
     */
    getStoredTheme() {
        try {
            return localStorage.getItem('ota-theme');
        } catch (e) {
            console.warn('无法读取主题设置:', e);
            return null;
        }
    }
    
    /**
     * 获取当前主题
     */
    getCurrentTheme() {
        return this.currentTheme;
    }
    
    /**
     * 获取主题信息
     */
    getThemeInfo(theme) {
        return this.themes[theme] || null;
    }

    /**
     * 检测设备性能并决定是否启用性能模式
     */
    detectPerformanceMode() {
        // 检测设备内存
        const memory = navigator.deviceMemory;
        if (memory && memory < 4) {
            return true;
        }

        // 检测网络连接
        const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
        if (connection && (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g')) {
            return true;
        }

        // 检测是否为移动设备
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        if (isMobile && window.innerWidth < 768) {
            return true;
        }

        // 检测是否支持 backdrop-filter
        const supportsBackdropFilter = CSS.supports('backdrop-filter', 'blur(1px)') ||
                                      CSS.supports('-webkit-backdrop-filter', 'blur(1px)');
        if (!supportsBackdropFilter) {
            return true;
        }

        // 检测用户偏好
        if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            return true;
        }

        return false;
    }

    /**
     * 应用性能优化
     */
    applyPerformanceOptimizations() {
        const body = document.body;

        if (this.performanceMode) {
            body.classList.add('performance-mode');
            console.log('液态玻璃主题: 已启用性能模式');
        } else {
            body.classList.remove('performance-mode');
        }

        // 检测并应用其他优化
        this.optimizeForDevice();
    }

    /**
     * 根据设备特性进行优化
     */
    optimizeForDevice() {
        const body = document.body;

        // 检测高 DPI 显示器
        if (window.devicePixelRatio > 2) {
            body.classList.add('high-dpi');
        }

        // 检测触摸设备
        if ('ontouchstart' in window || navigator.maxTouchPoints > 0) {
            body.classList.add('touch-device');
        }

        // 检测暗色模式偏好
        if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
            body.classList.add('prefers-dark');
        }

        // 检测高对比度偏好
        if (window.matchMedia('(prefers-contrast: high)').matches) {
            body.classList.add('high-contrast');
        }
    }

    /**
     * 启用调试模式
     */
    enableDebugMode() {
        document.body.classList.add('debug-mode');
        console.log('液态玻璃主题: 调试模式已启用');

        // 显示性能信息
        this.showPerformanceInfo();
    }

    /**
     * 禁用调试模式
     */
    disableDebugMode() {
        document.body.classList.remove('debug-mode');
        console.log('液态玻璃主题: 调试模式已禁用');
    }

    /**
     * 显示性能信息
     */
    showPerformanceInfo() {
        const info = {
            theme: this.currentTheme,
            performanceMode: this.performanceMode,
            deviceMemory: navigator.deviceMemory || 'unknown',
            connection: navigator.connection ? navigator.connection.effectiveType : 'unknown',
            supportsBackdropFilter: CSS.supports('backdrop-filter', 'blur(1px)'),
            devicePixelRatio: window.devicePixelRatio,
            screenSize: `${window.innerWidth}x${window.innerHeight}`,
            userAgent: navigator.userAgent
        };

        console.table(info);
        return info;
    }
}

// 初始化主题切换器
document.addEventListener('DOMContentLoaded', () => {
    window.themeSwitcher = new ThemeSwitcher();
    
    // 添加主题变化监听器示例
    document.addEventListener('themeChanged', (e) => {
        console.log('主题已切换:', e.detail);
        
        // 可以在这里添加主题切换后的额外处理
        // 例如：更新图表颜色、重新渲染某些组件等
    });
});

// 导出供其他脚本使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ThemeSwitcher;
}
