// 串口工具类
class SerialTool {
    constructor() {
        this.port = null;
        this.reader = null;
        this.writer = null;
        this.connected = false;
        this.autoScroll = true;
        this.terminal = document.getElementById('terminal');
        this.connectBtn = document.getElementById('connect');
        this.disconnectBtn = document.getElementById('disconnect');
        this.sendBtn = document.getElementById('send');
        this.clearBtn = document.getElementById('clear');
        this.autoScrollCheckbox = document.getElementById('autoScroll');
        this.sendInput = document.getElementById('sendData');
        this.hexSendCheckbox = document.getElementById('hexSend');
        this.hexDisplayCheckbox = document.getElementById('hexDisplay');
        this.baudrateSelect = document.getElementById('baudrate');
        this.autoNewlineCheckbox = document.getElementById('autoNewline');
        this.receiveBuffer = '';
        
        this.initEventListeners();
        this.checkSerialSupport();
        this.populateBaudrateOptions();
    }

    initEventListeners() {
        // 连接按钮点击事件
        this.connectBtn.addEventListener('click', () => this.connect());
        
        // 断开按钮点击事件
        this.disconnectBtn.addEventListener('click', () => this.disconnect());
        
        // 发送按钮点击事件
        this.sendBtn.addEventListener('click', () => this.sendData());
        
        // 清除按钮点击事件
        this.clearBtn.addEventListener('click', () => this.clearTerminal());
        
        // 自动滚动复选框事件
        this.autoScrollCheckbox.addEventListener('change', (e) => {
            this.autoScroll = e.target.checked;
        });
        
        // 发送输入框回车事件
        this.sendInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendData();
            }
        });
        
        // 十六进制显示复选框事件
        this.hexDisplayCheckbox.addEventListener('change', () => {
            this.appendToTerminal('系统', '显示模式已切换为' + (this.hexDisplayCheckbox.checked ? '十六进制' : '文本'));
        });
        
        // 十六进制发送复选框事件
        this.hexSendCheckbox.addEventListener('change', () => {
            this.appendToTerminal('系统', '发送模式已切换为' + (this.hexSendCheckbox.checked ? '十六进制' : '文本'));
        });
    }
    
    // 填充波特率选项
    populateBaudrateOptions() {
        const baudrates = [
            9600, 14400, 19200, 38400, 56000, 57600, 115200, 
            128000, 230400, 256000, 460800, 512000, 750000, 
            921600, 1500000
        ];
        
        this.baudrateSelect.innerHTML = '';
        baudrates.forEach(baudrate => {
            const option = document.createElement('option');
            option.value = baudrate;
            option.textContent = baudrate;
            this.baudrateSelect.appendChild(option);
        });
    }
    
    // 检查浏览器是否支持Web Serial API
    checkSerialSupport() {
        if (!('serial' in navigator)) {
            this.appendToTerminal('系统', '您的浏览器不支持Web Serial API，请使用Chrome或Edge浏览器');
            this.connectBtn.disabled = true;
            document.getElementById('browserSupport').style.display = 'block';
            document.getElementById('serialTool').style.display = 'none';
            return false;
        }
        return true;
    }
    
    // 连接串口
    async connect() {
        if (!this.checkSerialSupport()) return;
        
        try {
            // 请求串口访问权限
            this.port = await navigator.serial.requestPort();
            
            // 打开串口
            await this.port.open({
                baudRate: parseInt(this.baudrateSelect.value)
            });
            
            this.connected = true;
            this.connectBtn.disabled = true;
            this.disconnectBtn.disabled = false;
            this.sendBtn.disabled = false;
            this.baudrateSelect.disabled = true;
            
            // 开始读取数据
            this.startReading();
            
            this.appendToTerminal('系统', '串口连接成功');
        } catch (error) {
            console.error('连接失败:', error);
            this.appendToTerminal('系统', '连接失败: ' + error.message);
            this.connected = false;
            this.connectBtn.disabled = false;
            this.disconnectBtn.disabled = true;
            this.sendBtn.disabled = true;
            this.baudrateSelect.disabled = false;
        }
    }
    
    // 断开连接
    async disconnect() {
        try {
            // 先停止读取
            if (this.reader) {
                try {
                    await this.reader.cancel();
                } catch (e) {
                    console.error('取消读取失败:', e);
                }
                this.reader = null;
            }
            
            // 关闭串口
            if (this.port) {
                try {
                    await this.port.close();
                } catch (e) {
                    console.error('关闭串口失败:', e);
                }
                this.port = null;
            }
            
            this.connected = false;
            this.connectBtn.disabled = false;
            this.disconnectBtn.disabled = true;
            this.sendBtn.disabled = true;
            this.baudrateSelect.disabled = false;
            
            this.appendToTerminal('系统', '串口已断开连接');
        } catch (error) {
            console.error('断开连接失败:', error);
            this.appendToTerminal('系统', '断开连接失败: ' + error.message);
            
            // 强制重置状态
            this.connected = false;
            this.connectBtn.disabled = false;
            this.disconnectBtn.disabled = true;
            this.sendBtn.disabled = true;
            this.baudrateSelect.disabled = false;
            this.port = null;
            this.reader = null;
        }
    }
    
    // 开始读取数据
    async startReading() {
        if (!this.port || !this.port.readable) return;
        
        try {
            this.reader = this.port.readable.getReader();
            
            while (this.port && this.port.readable) {
                try {
                    const { value, done } = await this.reader.read();
                    if (done) {
                        break;
                    }
                    
                    // 处理接收到的数据
                    if (this.hexDisplayCheckbox.checked) {
                        // 十六进制显示
                        const hexArray = Array.from(value).map(b => 
                            b.toString(16).padStart(2, '0').toUpperCase()
                        );
                        this.appendToTerminal('接收', hexArray.join(' '));
                    } else {
                        // 新增缓冲区管理
                        const textChunk = new TextDecoder().decode(value);
                        this.receiveBuffer += textChunk;
                        
                        // 按换行符分割（支持多种换行格式）
                        const lines = this.receiveBuffer.split(/(\r\n|\n|\r)/);
                        this.receiveBuffer = lines.pop(); // 保存未完成的行
                        
                        lines.forEach(line => {
                            if (line && !['\r\n', '\n', '\r'].includes(line)) {
                                this.appendToTerminal('接收', line);
                            }
                        });
                    }
                } catch (error) {
                    console.error('读取错误:', error);
                    this.appendToTerminal('系统', '读取错误: ' + error.message);
                    break;
                }
            }
        } catch (error) {
            console.error('启动读取失败:', error);
            this.appendToTerminal('系统', '启动读取失败: ' + error.message);
        } finally {
            // 释放读取器
            if (this.reader) {
                try {
                    this.reader.releaseLock();
                } catch (e) {
                    console.error('释放读取器失败:', e);
                }
                this.reader = null;
            }
            
            // 如果连接仍然存在但读取已停止，尝试重新连接
            if (this.connected && this.port) {
                this.appendToTerminal('系统', '读取已停止，尝试重新连接...');
                setTimeout(() => this.disconnect(), 1000);
            }
        }
    }
    
    // 发送数据
    async sendData() {
        if (!this.connected || !this.port) {
            this.appendToTerminal('系统', '请先连接串口');
            return;
        }
        
        const data = this.sendInput.value;
        if (!data) {
            return;
        }
        
        try {
            this.writer = this.port.writable.getWriter();
            
            let sendData;
            if (this.hexSendCheckbox.checked) {
                // 十六进制发送
                try {
                    const hexArray = data.replace(/\s+/g, '').match(/.{1,2}/g) || [];
                    sendData = new Uint8Array(hexArray.map(hex => parseInt(hex, 16)));
                } catch (e) {
                    this.appendToTerminal('系统', '十六进制格式错误，请确保输入正确的十六进制数据');
                    return;
                }
            } else {
                // 文本发送
                const encoder = new TextEncoder();
                // 根据自动换行选项决定是否添加换行符
                const textToSend = this.autoNewlineCheckbox.checked ? data + '\r\n' : data;
                sendData = encoder.encode(textToSend);
            }
            
            await this.writer.write(sendData);
            this.appendToTerminal('发送', data);
            // 不再清空输入框，保留内容以便再次发送
            // this.sendInput.value = '';
        } catch (error) {
            console.error('发送错误:', error);
            this.appendToTerminal('系统', '发送错误: ' + error.message);
        } finally {
            if (this.writer) {
                try {
                    this.writer.releaseLock();
                } catch (e) {
                    console.error('释放写入器失败:', e);
                }
                this.writer = null;
            }
        }
    }
    
    // 添加内容到终端
    appendToTerminal(type, text) {
        const div = document.createElement('div');
        div.className = 'terminal-line';
        
        const time = new Date().toLocaleTimeString();
        const typeClass = type === '发送' ? 'text-primary' : 
                         type === '接收' ? 'text-success' : 
                         'text-warning';
        
        // 修改显示文本的颜色为浅绿色或浅蓝色
        const textColorClass = type === '发送' ? 'send-text' : 
                              type === '接收' ? 'receive-text' : 
                              'system-text';
        
        div.innerHTML = `
            <span class="text-muted">[${time}]</span>
            <span class="${typeClass}">[${type}]</span>
            <span class="${textColorClass}">${text}</span>
        `;
        
        this.terminal.appendChild(div);
        
        // 如果启用了自动滚动，滚动到底部
        if (this.autoScroll) {
            setTimeout(() => {
                this.terminal.scrollTop = this.terminal.scrollHeight;
            }, 0);
        }
    }
    
    // 清空终端
    clearTerminal() {
        this.terminal.innerHTML = '';
    }
}

// 页面加载完成后初始化串口工具
document.addEventListener('DOMContentLoaded', () => {
    // 检查浏览器是否支持Web Serial API
    if (!('serial' in navigator)) {
        document.getElementById('browserSupport').style.display = 'block';
        document.getElementById('serialTool').style.display = 'none';
        return;
    }
    
    // 初始化串口工具
    window.serialTool = new SerialTool();
}); 