<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>充电桩协议解析器 - OTA设备管理系统</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap');
        body {
            padding-top: 70px;
            font-family: 'Roboto', sans-serif;
            background: #f4f6f8;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        .header p {
            color: #7f8c8d;
            font-size: 1.1em;
        }
        .input-section {
            margin-bottom: 30px;
        }
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 12px;
            font-size: 1.1em;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 0.2rem rgba(52,152,219,0.25);
        }
        .btn-primary {
            background-color: #3498db;
            border: none;
            padding: 12px 30px;
            font-size: 1.1em;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
        }
        .result-section {
            margin-top: 30px;
        }
        .result-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            margin-top: 20px;
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 0 10px rgba(0,0,0,0.05);
        }
        .result-table th {
            background-color: #f8f9fa;
            color: #2c3e50;
            font-weight: 600;
            padding: 15px;
            text-align: left;
            border-bottom: 2px solid #e9ecef;
        }
        .result-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #e9ecef;
            color: #2c3e50;
        }
        .result-table tr:last-child td {
            border-bottom: none;
        }
        .result-table tr:hover {
            background-color: #f8f9fa;
        }
        .section-header {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }
        .error-container {
            display: none;
            background-color: #fee;
            color: #e74c3c;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            border: 1px solid #fcc;
        }
        .hex-value {
            font-family: monospace;
            color: #3498db;
        }
        .description {
            color: #7f8c8d;
        }
        .footer {
            margin-top: 50px;
            padding: 20px 0;
            border-top: 1px solid #e5e5e5;
            text-align: center;
            background: #ffffff;
        }
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            .header h1 {
                font-size: 2em;
            }
            .result-table {
                display: block;
                overflow-x: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-code"></i> 充电桩协议解析器</h1>
            <p>输入十六进制格式的消息，解析其内容和含义</p>
        </div>
        
        <div class="input-section">
            <div class="row">
                <div class="col-md-9">
                    <input type="text" id="message-input" class="form-control" 
                           placeholder="请输入十六进制格式的消息（例如：AA BB CC DD）"
                           value="0x55aa00030005f5e4f30035019612e5bff00306000000961a5501a0aaaaaa0a019c176601b11766010f17660141176601cb1766b1cd">
                </div>
                <div class="col-md-3">
                    <button id="parse-button" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i> 解析消息
                    </button>
                </div>
            </div>
        </div>

        <div id="error-container" class="error-container"></div>
        
        <div class="result-section">
            <div id="result-container"></div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <p class="text-muted">© 2025 OTA设备管理系统</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="protocol.js"></script>
    <script src="parsers/constants.js"></script>
    <script src="parsers/request-parsers.js"></script>
    <script src="parsers/command-parsers.js"></script>
    <script src="parsers/event-parsers.js"></script>
    <script src="parsers/index.js"></script>
    <script src="parser.js"></script>
</body>
</html> 