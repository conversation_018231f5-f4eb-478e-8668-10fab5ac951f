// 消息解析器类
class MessageParser {
    constructor() {
        this.input = document.getElementById('message-input');
        this.parseButton = document.getElementById('parse-button');
        this.resultContainer = document.getElementById('result-container');
        this.errorContainer = document.getElementById('error-container');
        
        this.parseButton.addEventListener('click', () => this.parseMessage());
    }

    // 解析消息
    parseMessage() {
        try {
            const hexMessage = this.input.value.trim();
            if (!hexMessage) {
                this.showError('请输入十六进制消息');
                return;
            }

            const bytes = hexToBytes(hexMessage);
            if (bytes.length < PROTOCOL.FIXED_PART_LEN) {
                this.showError('消息长度不足');
                return;
            }

            // 验证魔数 (0x55 0xAA)
            const magic = (bytes[0] << 8) | bytes[1];
            if (magic !== PROTOCOL.MAGIC_HEADER) {
                this.showError('无效的魔数');
                return;
            }

            // 验证协议版本 (0x0003)
            const version = (bytes[2] << 8) | bytes[3];
            if (version < PROTOCOL.PROTOCOL_VERSION_MIN || version > PROTOCOL.PROTOCOL_VERSION_MAX) {
                this.showError('不支持的协议版本');
                return;
            }

            // 解析设备ID (40位唯一ID，每个中控出厂配置完成，在后台添加后激活)
            const deviceId = ((BigInt(bytes[4]) << 32n) | 
                            (BigInt(bytes[5]) << 24n) | 
                            (BigInt(bytes[6]) << 16n) | 
                            (BigInt(bytes[7]) << 8n) | 
                            BigInt(bytes[8])).toString(16).toUpperCase().padStart(10, '0');
            
            // 解析块长度 (块长度，单位字节，为整个二进制数据块的长度，大端序)
            const blockLen = (bytes[9] << 8) | bytes[10];
            
            // 解析Unix毫秒时间戳 (省略了8字节的高两个字节)
            const timestamp = ((BigInt(bytes[11]) << 40n) | 
                             (BigInt(bytes[12]) << 32n) | 
                             (BigInt(bytes[13]) << 24n) | 
                             (BigInt(bytes[14]) << 16n) | 
                             (BigInt(bytes[15]) << 8n) | 
                             BigInt(bytes[16]));
            
            // 解析消息类型 (0x02: 命令)
            const msgType = bytes[17];
            
            // 解析消息对象 (0x02: 启动充电命令, 0x05: 停止充电命令)
            const msgObj = bytes[18];
            
            // 验证CRC
            const messageCRC = (bytes[blockLen - 1] << 8) | bytes[blockLen - 2];
            const calculatedCRC = CRC16_Modbus(bytes.slice(0, blockLen - 2));
            if (messageCRC !== calculatedCRC) {
                this.showError(`CRC校验失败，消息CRC: 0x${messageCRC.toString(16).toUpperCase()}, 计算CRC: 0x${calculatedCRC.toString(16).toUpperCase()}`);
                return;
            }

            // 解析数据部分 (从第19字节开始)
            const dataOffset = 19; // 固定部分长度
            const dataLen = blockLen - dataOffset - 2; // 减去CRC长度
            const data = bytes.slice(dataOffset, dataOffset + dataLen);
            
            // 显示解析结果
            this.displayResult({
                magic: `0x${bytes[0].toString(16).padStart(2, '0').toUpperCase()}${bytes[1].toString(16).padStart(2, '0').toUpperCase()}`,
                version: `0x${version.toString(16).padStart(4, '0').toUpperCase()}`,
                deviceId: {
                    value: deviceId,
                    description: `中控ID: 0x${deviceId} (40位唯一ID，每个中控出厂配置完成，在后台添加后激活)`
                },
                blockLen: {
                    value: blockLen,
                    description: `块长度: ${blockLen} (单位字节，为整个二进制数据块的长度，大端序)`
                },
                timestamp: {
                    value: timestamp.toString(),
                    formatted: formatTimestamp(Number(timestamp))
                },
                msgType: {
                    value: `0x${msgType.toString(16).padStart(2, '0').toUpperCase()}`,
                    description: getMsgTypeString(msgType)
                },
                msgObj: {
                    value: `0x${msgObj.toString(16).padStart(2, '0').toUpperCase()}`,
                    description: B2_MsgObj_String(msgType, msgObj)
                },
                crc: `0x${messageCRC.toString(16).padStart(4, '0').toUpperCase()}`,
                dataLen: dataLen,
                data: parseMessageData(msgType, msgObj, data)
            });

        } catch (error) {
            this.showError('解析失败: ' + error.message);
        }
    }

    // 显示解析结果
    displayResult(result) {
        this.errorContainer.style.display = 'none';
        this.resultContainer.innerHTML = '';

        // 创建表格
        const table = document.createElement('table');
        table.className = 'result-table';

        // 添加表头
        const thead = document.createElement('thead');
        thead.innerHTML = `
            <tr>
                <th>字段</th>
                <th>值</th>
                <th>描述</th>
            </tr>
        `;
        table.appendChild(thead);

        // 添加表体
        const tbody = document.createElement('tbody');
        
        // 添加消息头信息
        const headerSection = document.createElement('tr');
        headerSection.innerHTML = '<td colspan="3" class="section-header">消息头</td>';
        tbody.appendChild(headerSection);

        this.addTableRow(tbody, '块头魔术码', result.magic, '代表这是二进制数据而不是Json字符串');
        this.addTableRow(tbody, '协议版本', result.version, '代表版本号V0.0.3');
        this.addTableRow(tbody, '设备ID', `0x${result.deviceId.value}`, result.deviceId.description);
        this.addTableRow(tbody, '块长度', result.blockLen.value, result.blockLen.description);
        this.addTableRow(tbody, '时间戳', result.timestamp.value, result.timestamp.formatted);
        this.addTableRow(tbody, '消息类型', result.msgType.value, result.msgType.description);
        this.addTableRow(tbody, '消息对象', result.msgObj.value, result.msgObj.description);

        // 添加数据部分
        if (result.data) {
            // 添加数据头
            const dataSection = document.createElement('tr');
            dataSection.innerHTML = '<td colspan="3" class="section-header">消息数据</td>';
            tbody.appendChild(dataSection);

            // 添加数据描述
            if (result.data.description) {
                this.addTableRow(tbody, '数据说明', '', result.data.description);
            }

            // 添加数据详情，HEX字符串显示数值
            this.addDataDetails(tbody, result.data);
        }

        // 添加CRC校验  
        const crcSection = document.createElement('tr');
        crcSection.innerHTML = '<td colspan="3" class="section-header">CRC校验</td>';
        tbody.appendChild(crcSection);

        this.addTableRow(tbody, 'CRC校验', result.crc, 'Modbus-CRC校验码，校验前面的所有字节');
        table.appendChild(tbody);
        this.resultContainer.appendChild(table);
    }

    // 添加数据详情
    addDataDetails(tbody, data) {
        for (const [key, value] of Object.entries(data)) {
            // 跳过description字段，已单独显示
            if (key === 'description') continue;
            
            // 处理插座详情数组
            if (key === 'plugDetails' && Array.isArray(value)) {
                this.addTableRow(tbody, '插座状态', '', `共${value.length}个插座`);
                
                // 创建插座状态表格
                const plugTable = document.createElement('table');
                plugTable.style.width = '100%';
                plugTable.style.marginTop = '10px';
                plugTable.style.borderCollapse = 'collapse';
                
                // 添加表头
                const plugThead = document.createElement('thead');
                plugThead.innerHTML = `
                    <tr>
                        <th style="border:1px solid #ddd;padding:8px;">插座编号</th>
                        <th style="border:1px solid #ddd;padding:8px;">类型</th>
                        <th style="border:1px solid #ddd;padding:8px;">状态</th>
                    </tr>
                `;
                plugTable.appendChild(plugThead);
                
                // 添加表体
                const plugTbody = document.createElement('tbody');
                for (const plug of value) {
                    const tr = document.createElement('tr');
                    tr.innerHTML = `
                        <td style="border:1px solid #ddd;padding:8px;">${plug.plugId}</td>
                        <td style="border:1px solid #ddd;padding:8px;">${plug.type}</td>
                        <td style="border:1px solid #ddd;padding:8px;">${plug.statusText}</td>
                    `;
                    plugTbody.appendChild(tr);
                }
                plugTable.appendChild(plugTbody);
                
                // 将表格添加到新行
                const row = document.createElement('tr');
                const cell = document.createElement('td');
                cell.colSpan = 3;
                cell.appendChild(plugTable);
                row.appendChild(cell);
                tbody.appendChild(row);
                continue;
            }
            
            // 处理计费信息数组
            if (key === 'billingInfo' && Array.isArray(value)) {
                this.addTableRow(tbody, '计费信息', '', `共${value.length}条计费记录`);
                
                if (value.length > 0) {
                    // 创建计费信息表格
                    const billingTable = document.createElement('table');
                    billingTable.style.width = '100%';
                    billingTable.style.marginTop = '10px';
                    billingTable.style.borderCollapse = 'collapse';
                    
                    // 添加表头
                    const billingThead = document.createElement('thead');
                    
                    // 动态生成表头，适应不同的计费信息字段
                    const headers = Object.keys(value[0])
                        .filter(k => k !== 'description')
                        .map(k => `<th style="border:1px solid #ddd;padding:8px;">${this.formatFieldName(k)}</th>`)
                        .join('');
                    
                    billingThead.innerHTML = `<tr>${headers}</tr>`;
                    billingTable.appendChild(billingThead);
                    
                    // 添加表体
                    const billingTbody = document.createElement('tbody');
                    for (const bill of value) {
                        const tr = document.createElement('tr');
                        
                        // 动态生成行数据
                        const cells = Object.entries(bill)
                            .filter(([k]) => k !== 'description')
                            .map(([k, v]) => `<td style="border:1px solid #ddd;padding:8px;">${v}</td>`)
                            .join('');
                        
                        tr.innerHTML = cells;
                        billingTbody.appendChild(tr);
                    }
                    billingTable.appendChild(billingTbody);
                    
                    // 将表格添加到新行
                    const row = document.createElement('tr');
                    const cell = document.createElement('td');
                    cell.colSpan = 3;
                    cell.appendChild(billingTable);
                    row.appendChild(cell);
                    tbody.appendChild(row);
                }
                continue;
            }
            
            // 处理一般数组
            if (Array.isArray(value)) {
                this.addTableRow(tbody, this.formatFieldName(key), JSON.stringify(value));
                continue;
            }
            
            // 处理对象
            if (typeof value === 'object' && value !== null) {
                this.addTableRow(tbody, this.formatFieldName(key), JSON.stringify(value));
                continue;
            }
            
            // 处理基本类型
            this.addTableRow(tbody, this.formatFieldName(key), value);
        }
    }

    // 添加表格行
    addTableRow(tbody, field, value, description = '') {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${field}</td>
            <td>${value}</td>
            <td>${description}</td>
        `;
        tbody.appendChild(row);
    }

    // 格式化字段名
    formatFieldName(name) {
        return name.replace(/([A-Z])/g, ' $1')
            .replace(/^./, str => str.toUpperCase())
            .replace(/([a-z])([A-Z])/g, '$1 $2');
    }

    // 显示错误信息
    showError(message) {
        this.errorContainer.textContent = message;
        this.errorContainer.style.display = 'block';
        this.resultContainer.innerHTML = '';
    }
}

// 获取消息对象字符串
function B2_MsgObj_String(msgType, msgObj) {
    switch (msgType) {
        case MSG_TYPE.REQ: // 请求
            return getReqTypeString(msgObj);
        case MSG_TYPE.REQ_RSP: // 请求回复
            return getReqTypeString(msgObj);
        case MSG_TYPE.CMD: // 命令
            return getCmdTypeString(msgObj);
        case MSG_TYPE.CMD_RSP: // 命令回复
            return getCmdTypeString(msgObj);
        case MSG_TYPE.EVT: // 事件
            return getEvtTypeString(msgObj);
        case MSG_TYPE.SET: // 设置
            return getSetTypeString(msgObj);
        case MSG_TYPE.SET_RSP: // 设置回复
            return getSetTypeString(msgObj);
        case MSG_TYPE.READ: // 读取
            return getReadTypeString(msgObj);
        case MSG_TYPE.READ_RSP: // 读取回复
            return getReadTypeString(msgObj);
        default:
            return "未知消息对象";
    }
}

// 格式化时间戳
function formatTimestamp(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
    }) + " (UTC+8)";
}

// 初始化解析器
document.addEventListener('DOMContentLoaded', () => {
    new MessageParser();
}); 