
// 命令数据解析
class CommandParsers {
    // 设备心跳命令 (0x01)
    static parseHeartbeat(data) {
        const heartSeq = (data[0] << 8) | data[1];
        const plugNum = data[2];
        
        const result = {
            heartSeq: heartSeq,
            plugNum: plugNum,
            plugStatus: [],
            description: `心跳序号: ${heartSeq}, 插座数量: ${plugNum}`
        };
        
        // 解析插座状态
        for (let i = 0; i < Math.min(plugNum, data.length - 3); i++) {
            result.plugStatus.push(data[3 + i]);
        }
        
        return result;
    }

    // 启动充电命令 (0x02)
    static parseStartCharge(data) {
        const sessionId = (data[0] << 8) | data[1];
        const plugId = data[2];
        
        return {
            sessionId: sessionId,
            plugId: plugId,
            description: `会话ID: ${sessionId}, 插座编号: ${plugId}` +
                (plugId < PLUG_TYPE.WIRELESS.MIN ? " (有线充电)" : " (无线充电)")
        };
    }

    // 充电会话重启命令 (0x03)
    static parseRestartChargeSession(data) {
        const sessionId = (data[0] << 8) | data[1];
        const plugId = data[2];
        
        return {
            sessionId: sessionId,
            plugId: plugId,
            description: `重启会话ID: ${sessionId}, 插座编号: ${plugId}` +
                (plugId < PLUG_TYPE.WIRELESS.MIN ? " (有线充电)" : " (无线充电)")
        };
    }

    // 计费时间片命令 (0x04)
    static parseBillingTimeSlice(data) {
        const sessionId = (data[0] << 8) | data[1];
        const plugId = data[2];
        
        return {
            sessionId: sessionId,
            plugId: plugId,
            description: `计费会话ID: ${sessionId}, 插座编号: ${plugId}` +
                (plugId < PLUG_TYPE.WIRELESS.MIN ? " (有线充电)" : " (无线充电)")
        };
    }

    // 停止充电命令 (0x05)
    static parseStopCharge(data) {
        const sessionId = (data[0] << 8) | data[1];
        const plugId = data[2];
        
        return {
            sessionId: sessionId,
            plugId: plugId,
            description: `会话ID: ${sessionId}, 插座编号: ${plugId}` +
                (plugId < PLUG_TYPE.WIRELESS.MIN ? " (有线充电)" : " (无线充电)")
        };
    }

    // 判断插座是否处于充电模式
    static isPlugInChargeMode(plugStatus, plugId) {
        const statusIdx = Math.floor(plugId / 4);
        if (statusIdx >= plugStatus.length) {
            return false;
        }
        
        const shift = 2 * (plugId % 4);
        const status = (plugStatus[statusIdx] >> shift) & 0x03;
        
        // 有线充电状态
        if (plugId < PLUG_TYPE.WIRELESS.MIN) {
            return status === PLUG_TYPE.WIRED.STATUS.IN_CHARGING;
        } 
        // 无线充电状态
        else {
            return status === PLUG_TYPE.WIRELESS.STATUS.IN_CHARGING;
        }
    }

    // 设备心跳命令及计费时间片统一命令 (0x06)
    static parseHeartbeatAndBilling(data) {
        const result = {};
        
        // 解析会话ID
        result.sessionId = (data[0] << 8) | data[1];
        
        // 解析心跳序号
        result.heartSeq = (data[2] << 8) | data[3];
        
        // 解析插座数量
        result.plugNum = data[4];
        
        // 解析插座状态数组
        const plugStatusCount = Math.ceil(result.plugNum / 4);
        result.plugStatus = [];
        
        // 读取所有插座状态字节
        for (let i = 0; i < Math.min(plugStatusCount, data.length - 5); i++) {
            result.plugStatus.push(data[5 + i]);
        }
        
        // 添加总体描述
        result.description = `会话ID: ${result.sessionId}, 心跳序号: ${result.heartSeq}, ` +
            `插座总数: ${result.plugNum}个`;
        

        // 解析有线充电和无线充电插座的状态
        result.plugDetails = [];
        
        // 解析有线充电插座状态
        for (let plugId = PLUG_TYPE.WIRED.MIN; plugId <= PLUG_TYPE.WIRED.MAX; plugId++) {
            const statusIdx = Math.floor(plugId / 4);
            if (statusIdx < result.plugStatus.length) {
                const shift = 2 * (plugId % 4);
                const status = (result.plugStatus[statusIdx] >> shift) & 0x03;
                
                let statusText = "";
                switch (status) {
                    case PLUG_TYPE.WIRED.STATUS.NOT_STARTED:
                        statusText = `未启动: ${status}`;
                        break;
                    case PLUG_TYPE.WIRED.STATUS.IN_CHARGING:
                        statusText = `充电中: ${status}`;
                        break;
                    case PLUG_TYPE.WIRED.STATUS.FAULT:
                        statusText = `故障: ${status}`;
                        break;
                    case PLUG_TYPE.WIRED.STATUS.RESERVED:
                        statusText = `保留: ${status}`;
                        break;
                }
                
                result.plugDetails.push({
                    plugId: plugId+1,
                    type: "有线",
                    status: status,
                    statusText: statusText
                });
            }
        }
        
        // 解析无线充电插座状态
        for (let plugId = PLUG_TYPE.WIRELESS.MIN; plugId <= PLUG_TYPE.WIRELESS.MAX; plugId++) {
            // 插座总数小于PLUG_TYPE.WIRED.MAX就没有无线充电插座
            if (result.plugNum > PLUG_TYPE.WIRED.MAX) {
                break;
            }
            const statusIdx = Math.floor(plugId / 4);
            if (statusIdx < result.plugStatus.length) {
                const shift = 2 * (plugId % 4);
                const status = (result.plugStatus[statusIdx] >> shift) & 0x03;
                
                let statusText = "";
                switch (status) {
                    case PLUG_TYPE.WIRELESS.STATUS.NOT_STARTED:
                        statusText = `未启动(接收模块未连接): ${status}`;
                        break;
                    case PLUG_TYPE.WIRELESS.STATUS.IN_CHARGING:
                        statusText = `充电中(接收模块已连接且启动充电): ${status}`;
                        break;
                    case PLUG_TYPE.WIRELESS.STATUS.FAULT:
                        statusText = `无线发射模块未连接: ${status}`;
                        break;
                    case PLUG_TYPE.WIRELESS.STATUS.IDLE:
                        statusText = `空闲(接收模块已连接但未启动充电): ${status}`;
                        break;
                }
                
                result.plugDetails.push({
                    plugId: plugId+1,
                    type: "无线",
                    status: status,
                    statusText: statusText
                });
            }
        }
        return result;
    }

    // OTA启动命令 (0x07)
    static parseOtaStart(data) {
        if (data.length < 7) {
            return {
                hex: bytesToHex(data),
                description: "OTA启动命令数据异常"
            };
        }
        
        const otaType = data[0];
        const otaVersion = (data[1] << 24) | (data[2] << 16) | (data[3] << 8) | data[4];
        const otaLength = (data[5] << 8) | data[6];
        
        let otaTypeText = "";
        switch (otaType) {
            case 0x01:
                otaTypeText = "固件升级";
                break;
            case 0x02:
                otaTypeText = "配置更新";
                break;
            default:
                otaTypeText = "未知类型";
        }
        
        return {
            otaType: otaType,
            otaTypeText: otaTypeText,
            otaVersion: otaVersion,
            otaLength: otaLength,
            description: `OTA类型: ${otaTypeText}, 版本: ${otaVersion}, 长度: ${otaLength}字节`
        };
    }
    
    // OTA中止命令 (0x08)
    static parseOtaAbort(data) {
        return {
            description: "OTA中止命令"
        };
    }
    
    // OTA结果查询 (0x09)
    static parseOtaResultQuery(data) {
        return {
            description: "OTA结果查询命令"
        };
    }
    
    // 设备强制重启命令 (0x0A)
    static parseDeviceForceReboot(data) {
        return {
            description: "设备强制重启命令"
        };
    }
    
    // OTA数据传输命令 (0x0B)
    static parseOtaDataTransfer(data) {
        if (data.length < 3) {
            return {
                hex: bytesToHex(data),
                description: "OTA数据传输命令数据异常"
            };
        }
        
        const blockNum = (data[0] << 8) | data[1];
        const dataLength = data[2];
        const otaData = data.slice(3, 3 + dataLength);
        
        return {
            blockNum: blockNum,
            dataLength: dataLength,
            otaData: bytesToHex(otaData),
            description: `OTA数据块编号: ${blockNum}, 数据长度: ${dataLength}字节`
        };
    }
    
    // 启动无线充电命令 (0x81)
    static parseStartWirelessCharge(data) {
        if (data.length < 3) {
            return {
                hex: bytesToHex(data),
                description: "启动无线充电命令数据异常"
            };
        }
        
        const plugId = data[0];
        const power = (data[1] << 8) | data[2];
        
        return {
            plugId: plugId,
            power: power,
            description: `无线插座${plugId}启动充电, 功率设置: ${power}W`
        };
    }
    
    // 停止无线充电命令 (0x82)
    static parseUserStopWirelessCharge(data) {
        if (data.length < 1) {
            return {
                hex: bytesToHex(data),
                description: "停止无线充电命令数据异常"
            };
        }
        
        const plugId = data[0];
        
        return {
            plugId: plugId,
            description: `停止无线插座${plugId}充电`
        };
    }

    // 根据命令类型解析数据
    static parse(cmdType, data) {
        switch (cmdType) {
            case CMD_TYPE.HEARTBEAT:
                return this.parseHeartbeat(data);
            case CMD_TYPE.START_CHARGE:
                return this.parseStartCharge(data);
            case CMD_TYPE.RESTART_CHARGE_SESSION:
                return this.parseRestartChargeSession(data);
            case CMD_TYPE.BILLING_TIME_SLICE:
                return this.parseBillingTimeSlice(data);
            case CMD_TYPE.STOP_CHARGE:
                return this.parseStopCharge(data);
            case CMD_TYPE.HEARTBEAT_AND_BILLING:
                return this.parseHeartbeatAndBilling(data);
            case CMD_TYPE.OTA_START:
                return this.parseOtaStart(data);
            case CMD_TYPE.OTA_ABORT:
                return this.parseOtaAbort(data);
            case CMD_TYPE.OTA_RESULT_QUERY:
                return this.parseOtaResultQuery(data);
            case CMD_TYPE.DEVICE_FORCE_REBOOT:
                return this.parseDeviceForceReboot(data);
            case CMD_TYPE.OTA_DATA_TRANSFER:
                return this.parseOtaDataTransfer(data);
            case CMD_TYPE.START_WIRELESS_CHARGE:
                return this.parseStartWirelessCharge(data);
            case CMD_TYPE.USER_STOP_WIRELESS_CHARGE:
                return this.parseUserStopWirelessCharge(data);
            default:
                return {
                    hex: bytesToHex(data),
                    description: "未知命令类型数据"
                };
        }
    }
} 


// 命令回复数据解析
class CommandRspParsers {
    // 设备心跳命令 (0x01)
    static parseHeartbeat(data) {
        const heartSeq = (data[0] << 8) | data[1];
        const plugNum = data[2];
        
        const result = {
            heartSeq: heartSeq,
            plugNum: plugNum,
            plugStatus: [],
            description: `心跳序号: ${heartSeq}, 插座数量: ${plugNum}`
        };
        
        // 解析插座状态
        for (let i = 0; i < Math.min(plugNum, data.length - 3); i++) {
            result.plugStatus.push(data[3 + i]);
        }
        
        return result;
    }

    // 启动充电命令 (0x02)
    static parseStartCharge(data) {
        const sessionId = (data[0] << 8) | data[1];
        const plugId = data[2];
        
        return {
            sessionId: sessionId,
            plugId: plugId,
            description: `会话ID: ${sessionId}, 插座编号: ${plugId}` +
                (plugId < PLUG_TYPE.WIRELESS.MIN ? " (有线充电)" : " (无线充电)")
        };
    }

    // 充电会话重启命令 (0x03)
    static parseRestartChargeSession(data) {
        const sessionId = (data[0] << 8) | data[1];
        const plugId = data[2];
        
        return {
            sessionId: sessionId,
            plugId: plugId,
            description: `重启会话ID: ${sessionId}, 插座编号: ${plugId}` +
                (plugId < PLUG_TYPE.WIRELESS.MIN ? " (有线充电)" : " (无线充电)")
        };
    }

    // 计费时间片命令 (0x04)
    static parseBillingTimeSlice(data) {
        const sessionId = (data[0] << 8) | data[1];
        const plugId = data[2];
        
        return {
            sessionId: sessionId,
            plugId: plugId,
            description: `计费会话ID: ${sessionId}, 插座编号: ${plugId}` +
                (plugId < PLUG_TYPE.WIRELESS.MIN ? " (有线充电)" : " (无线充电)")
        };
    }

    // 停止充电命令 (0x05)
    static parseStopCharge(data) {
        const sessionId = (data[0] << 8) | data[1];
        const plugId = data[2];
        
        return {
            sessionId: sessionId,
            plugId: plugId,
            description: `会话ID: ${sessionId}, 插座编号: ${plugId}` +
                (plugId < PLUG_TYPE.WIRELESS.MIN ? " (有线充电)" : " (无线充电)")
        };
    }

    // 判断插座是否处于充电模式
    static isPlugInChargeMode(plugStatus, plugId) {
        const statusIdx = Math.floor(plugId / 4);
        if (statusIdx >= plugStatus.length) {
            return false;
        }
        
        const shift = 2 * (plugId % 4);
        const status = (plugStatus[statusIdx] >> shift) & 0x03;
        
        // 有线充电状态
        if (plugId < PLUG_TYPE.WIRELESS.MIN) {
            return status === PLUG_TYPE.WIRED.STATUS.IN_CHARGING;
        } 
        // 无线充电状态
        else {
            return status === PLUG_TYPE.WIRELESS.STATUS.IN_CHARGING;
        }
    }

    // 设备心跳命令及计费时间片统一命令 (0x06)
    static parseHeartbeatAndBilling(data) {
        const result = {};
        
        // 解析会话ID
        result.sessionId = (data[0] << 8) | data[1];
        
        // 解析心跳序号
        result.heartSeq = (data[2] << 8) | data[3];
        
        // 解析插座数量
        result.plugNum = data[4];
        
        // 解析插座状态数组
        const plugStatusCount = Math.ceil(result.plugNum / 4);
        result.plugStatus = [];
        
        // 读取所有插座状态字节
        for (let i = 0; i < Math.min(plugStatusCount, data.length - 5); i++) {
            result.plugStatus.push(data[5 + i]);
        }
        
        // 解析有线充电和无线充电插座的状态
        result.plugDetails = [];
        
        // 解析有线充电插座状态
        for (let plugId = PLUG_TYPE.WIRED.MIN; plugId <= PLUG_TYPE.WIRED.MAX; plugId++) {
            const statusIdx = Math.floor(plugId / 4);
            if (statusIdx < result.plugStatus.length) {
                const shift = 2 * (plugId % 4);
                const status = (result.plugStatus[statusIdx] >> shift) & 0x03;
                
                let statusText = "";
                switch (status) {
                    case PLUG_TYPE.WIRED.STATUS.NOT_STARTED:
                        statusText = `未启动: ${status}`;
                        break;
                    case PLUG_TYPE.WIRED.STATUS.IN_CHARGING:
                        statusText = `充电中: ${status}`;
                        break;
                    case PLUG_TYPE.WIRED.STATUS.FAULT:
                        statusText = `故障: ${status}`;
                        break;
                    case PLUG_TYPE.WIRED.STATUS.RESERVED:
                        statusText = `保留: ${status}`;
                        break;
                }
                
                result.plugDetails.push({
                    plugId: plugId+1,
                    type: "有线",
                    status: status,
                    statusText: statusText
                });
            }
        }
        
        // 解析无线充电插座状态
        for (let plugId = PLUG_TYPE.WIRELESS.MIN; plugId <= PLUG_TYPE.WIRELESS.MAX; plugId++) {
            // 插座总数小于PLUG_TYPE.WIRED.MAX就没有无线充电插座
            if (result.plugNum > PLUG_TYPE.WIRED.MAX) {
                break;
            }
            const statusIdx = Math.floor(plugId / 4);
            if (statusIdx < result.plugStatus.length) {
                const shift = 2 * (plugId % 4);
                const status = (result.plugStatus[statusIdx] >> shift) & 0x03;
                
                let statusText = "";
                switch (status) {
                    case PLUG_TYPE.WIRELESS.STATUS.NOT_STARTED:
                        statusText = `未启动(接收模块未连接): ${status}`;
                        break;
                    case PLUG_TYPE.WIRELESS.STATUS.IN_CHARGING:
                        statusText = `充电中(接收模块已连接且启动充电): ${status}`;
                        break;
                    case PLUG_TYPE.WIRELESS.STATUS.FAULT:
                        statusText = `无线发射模块未连接: ${status}`;
                        break;
                    case PLUG_TYPE.WIRELESS.STATUS.IDLE:
                        statusText = `空闲(接收模块已连接但未启动充电): ${status}`;
                        break;
                }
                
                result.plugDetails.push({
                    plugId: plugId+1,
                    type: "无线",
                    status: status,
                    statusText: statusText
                });
            }
        }
        
        // 解析计费信息
        result.billingInfo = [];
        let offset = 5 + result.plugStatus.length;
        
        // 有线充电的计费信息
        for (let i = PLUG_TYPE.WIRED.MIN; i <= PLUG_TYPE.WIRED.MAX; i++) {
            if (this.isPlugInChargeMode(result.plugStatus, i) && offset + 4 <= data.length) {
                const avgPower = (data[offset] << 8) | data[offset + 1];
                const powerSumTime = (data[offset + 2] << 8) | data[offset + 3];
                
                result.billingInfo.push({
                    plugId: i+1,
                    type: "有线",
                    avgPower: avgPower,
                    powerSumTime: powerSumTime,
                    actualPower: (avgPower * 0.85).toFixed(2), // 实际功率考虑功率因数
                    billDuration: (powerSumTime * 0.1).toFixed(1),
                    description: `有线插座${i} - 平均功率: ${avgPower}W, 计费时间: ${powerSumTime * 0.1}s`
                });
                
                offset += 4;
            }
        }
        
        // 无线充电的计费信息
        for (let i = PLUG_TYPE.WIRELESS.MIN; i <= PLUG_TYPE.WIRELESS.MAX; i++) {
            if (this.isPlugInChargeMode(result.plugStatus, i) && offset + 4 <= data.length) {
                const avgPower = (data[offset] << 8) | data[offset + 1];
                const powerSumTime = (data[offset + 2] << 8) | data[offset + 3];
                
                result.billingInfo.push({
                    plugId: i+1,
                    type: "无线",
                    avgPower: avgPower,
                    powerSumTime: powerSumTime,
                    billDuration: (powerSumTime * 0.1).toFixed(1),
                    description: `无线插座${i} - 平均功率: ${avgPower}W, 计费时间: ${powerSumTime * 0.1}s`
                });
                
                offset += 4;
            }
        }
        
        // 添加总体描述
        result.description = `会话ID: ${result.sessionId}, 心跳序号: ${result.heartSeq}, ` +
            `插座总数: ${result.plugNum}, 正在充电中的插座: ${result.billingInfo.length}个`;
        
        return result;
    }

    // OTA启动命令 (0x07)
    static parseOtaStart(data) {
        if (data.length < 7) {
            return {
                hex: bytesToHex(data),
                description: "OTA启动命令数据异常"
            };
        }
        
        const otaType = data[0];
        const otaVersion = (data[1] << 24) | (data[2] << 16) | (data[3] << 8) | data[4];
        const otaLength = (data[5] << 8) | data[6];
        
        let otaTypeText = "";
        switch (otaType) {
            case 0x01:
                otaTypeText = "固件升级";
                break;
            case 0x02:
                otaTypeText = "配置更新";
                break;
            default:
                otaTypeText = "未知类型";
        }
        
        return {
            otaType: otaType,
            otaTypeText: otaTypeText,
            otaVersion: otaVersion,
            otaLength: otaLength,
            description: `OTA类型: ${otaTypeText}, 版本: ${otaVersion}, 长度: ${otaLength}字节`
        };
    }
    
    // OTA中止命令 (0x08)
    static parseOtaAbort(data) {
        return {
            description: "OTA中止命令"
        };
    }
    
    // OTA结果查询 (0x09)
    static parseOtaResultQuery(data) {
        return {
            description: "OTA结果查询命令"
        };
    }
    
    // 设备强制重启命令 (0x0A)
    static parseDeviceForceReboot(data) {
        return {
            description: "设备强制重启命令"
        };
    }
    
    // OTA数据传输命令 (0x0B)
    static parseOtaDataTransfer(data) {
        if (data.length < 3) {
            return {
                hex: bytesToHex(data),
                description: "OTA数据传输命令数据异常"
            };
        }
        
        const blockNum = (data[0] << 8) | data[1];
        const dataLength = data[2];
        const otaData = data.slice(3, 3 + dataLength);
        
        return {
            blockNum: blockNum,
            dataLength: dataLength,
            otaData: bytesToHex(otaData),
            description: `OTA数据块编号: ${blockNum}, 数据长度: ${dataLength}字节`
        };
    }
    
    // 启动无线充电命令 (0x81)
    static parseStartWirelessCharge(data) {
        if (data.length < 3) {
            return {
                hex: bytesToHex(data),
                description: "启动无线充电命令数据异常"
            };
        }
        
        const plugId = data[0];
        const power = (data[1] << 8) | data[2];
        
        return {
            plugId: plugId,
            power: power,
            description: `无线插座${plugId}启动充电, 功率设置: ${power}W`
        };
    }
    
    // 停止无线充电命令 (0x82)
    static parseUserStopWirelessCharge(data) {
        if (data.length < 1) {
            return {
                hex: bytesToHex(data),
                description: "停止无线充电命令数据异常"
            };
        }
        
        const plugId = data[0];
        
        return {
            plugId: plugId,
            description: `停止无线插座${plugId}充电`
        };
    }

    // 根据命令类型解析数据
    static parse(cmdType, data) {
        switch (cmdType) {
            case CMD_TYPE.HEARTBEAT:
                return this.parseHeartbeat(data);
            case CMD_TYPE.START_CHARGE:
                return this.parseStartCharge(data);
            case CMD_TYPE.RESTART_CHARGE_SESSION:
                return this.parseRestartChargeSession(data);
            case CMD_TYPE.BILLING_TIME_SLICE:
                return this.parseBillingTimeSlice(data);
            case CMD_TYPE.STOP_CHARGE:
                return this.parseStopCharge(data);
            case CMD_TYPE.HEARTBEAT_AND_BILLING:
                return this.parseHeartbeatAndBilling(data);
            case CMD_TYPE.OTA_START:
                return this.parseOtaStart(data);
            case CMD_TYPE.OTA_ABORT:
                return this.parseOtaAbort(data);
            case CMD_TYPE.OTA_RESULT_QUERY:
                return this.parseOtaResultQuery(data);
            case CMD_TYPE.DEVICE_FORCE_REBOOT:
                return this.parseDeviceForceReboot(data);
            case CMD_TYPE.OTA_DATA_TRANSFER:
                return this.parseOtaDataTransfer(data);
            case CMD_TYPE.START_WIRELESS_CHARGE:
                return this.parseStartWirelessCharge(data);
            case CMD_TYPE.USER_STOP_WIRELESS_CHARGE:
                return this.parseUserStopWirelessCharge(data);
            default:
                return {
                    hex: bytesToHex(data),
                    description: "未知命令类型数据"
                };
        }
    }
} 