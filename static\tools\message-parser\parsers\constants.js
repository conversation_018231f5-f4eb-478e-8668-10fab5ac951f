// 消息类型常量
const PLUG_TYPE = {
    WIRED: {
        MIN: 0,
        MAX: 9,
        STATUS: {
            NOT_STARTED: 0,  // 未启动
            IN_CHARGING: 1,  // 在充电会话中
            FAULT: 2,        // 故障
            RESERVED: 3      // 保留
        }
    },
    WIRELESS: {
        MIN: 10,
        MAX: 25,
        STATUS: {
            NOT_STARTED: 0,  // 未启动->接收模块未连接
            IN_CHARGING: 1,  // 在充电会话中->接收模块已连接，且已经启动充电
            FAULT: 2,        // 无线发射模块未连接
            IDLE: 3          // 接收模块已连接，但未启动充电
        }
    }
}; 