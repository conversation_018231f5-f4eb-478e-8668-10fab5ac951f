// 事件数据解析
class EventParsers {
    // 用户超时未接入充电器事件 (0x01)
    static parseUserTimeoutNoPlugIn(data) {
        const sessionId = (data[0] << 8) | data[1];
        const plugId = data[2];
        
        return {
            sessionId: sessionId,
            plugId: plugId,
            description: `会话ID: ${sessionId}, 插座${plugId}用户超时未接入充电器`
        };
    }

    // 用户主动拔出充电器事件 (0x02)
    static parseUserPullOutPlug(data) {
        const sessionId = (data[0] << 8) | data[1];
        const plugId = data[2];
        
        return {
            sessionId: sessionId,
            plugId: plugId,
            description: `会话ID: ${sessionId}, 插座${plugId}用户主动拔出充电器`
        };
    }

    // 电动车充满事件 (0x03)
    static parseUserChargeOk(data) {
        const sessionId = (data[0] << 8) | data[1];
        const plugId = data[2];
        
        return {
            sessionId: sessionId,
            plugId: plugId,
            description: `会话ID: ${sessionId}, 插座${plugId}电动车充满`
        };
    }

    // 插座功率长期过高 (0x04)
    static parsePlugPowerOverLimit(data) {
        const plugId = data[0];
        const power = (data[1] << 8) | data[2];
        
        return {
            plugId: plugId,
            power: power,
            description: `插座${plugId}功率长期过高: ${power}W`
        };
    }

    // 插座功率超过安全限制 (0x05)
    static parsePlugPowerOverSafeLimit(data) {
        const plugId = data[0];
        const power = (data[1] << 8) | data[2];
        
        return {
            plugId: plugId,
            power: power,
            description: `插座${plugId}功率超过安全限制: ${power}W`
        };
    }

    // 系统总功率超过限制被切断 (0x06)
    static parseMainPowerOverTotalLimit(data) {
        const totalPower = (data[0] << 8) | data[1];
        
        return {
            totalPower: totalPower,
            description: `系统总功率超过限制被切断: ${totalPower}W`
        };
    }

    // 主板温度超过限制被断开 (0x07)
    static parseMainBoardTempOverLimit(data) {
        const temperature = data[0];
        
        return {
            temperature: temperature,
            description: `主板温度超过限制被断开: ${temperature}°C`
        };
    }

    // 中控疑似被断电 (0x08)
    static parseMainPowerOffSuspected(data) {
        const powerOffTime = (data[0] << 8) | data[1];
        
        return {
            powerOffTime: powerOffTime,
            description: `中控疑似被断电: ${powerOffTime}秒`
        };
    }

    // 充电过程中继电器异常开路 (0x09)
    static parseRelayOpenCircuit(data) {
        const plugId = data[0];
        
        return {
            plugId: plugId,
            description: `插座${plugId}充电过程中继电器异常开路`
        };
    }

    // 无线充电完成事件 (0x81)
    static parseWirelessChargeFinish(data) {
        const plugId = data[0];
        const status = data[1];
        const errorCode = (data[2] << 8) | data[3];
        
        let statusText = "";
        switch (status) {
            case 0x00:
                statusText = "正常完成";
                break;
            case 0x01:
                statusText = "异常完成";
                break;
            default:
                statusText = "未知状态";
        }
        
        return {
            plugId: plugId,
            status: status,
            statusText: statusText,
            errorCode: errorCode,
            description: `无线插座${plugId}充电完成, 状态: ${statusText}, 错误码: 0x${errorCode.toString(16).toUpperCase()}`
        };
    }

    // 用户回收无线充电器停止充电事件 (0x82)
    static parseUserRecycleWirelessChargerStopCharge(data) {
        const plugId = data[0];
        
        return {
            plugId: plugId,
            description: `用户回收无线插座${plugId}充电器停止充电`
        };
    }

    // 根据事件类型解析数据
    static parse(evtType, data) {
        switch (evtType) {
            case EVT_TYPE.USER_TIMEOUT_NO_PLUG_IN:
                return this.parseUserTimeoutNoPlugIn(data);
            case EVT_TYPE.USER_PULL_OUT_PLUG:
                return this.parseUserPullOutPlug(data);
            case EVT_TYPE.USER_CHARGE_OK:
                return this.parseUserChargeOk(data);
            case EVT_TYPE.PLUG_POWER_OVER_LIMIT:
                return this.parsePlugPowerOverLimit(data);
            case EVT_TYPE.PLUG_POWER_OVER_SAFE_LIMIT:
                return this.parsePlugPowerOverSafeLimit(data);
            case EVT_TYPE.MAIN_POWER_OVER_TOTAL_LIMIT:
                return this.parseMainPowerOverTotalLimit(data);
            case EVT_TYPE.MAIN_BOARD_TEMP_OVER_LIMIT:
                return this.parseMainBoardTempOverLimit(data);
            case EVT_TYPE.MAIN_POWER_OFF_SUSPECTED:
                return this.parseMainPowerOffSuspected(data);
            case EVT_TYPE.RELAY_OPEN_CIRCUIT:
                return this.parseRelayOpenCircuit(data);
            case EVT_TYPE.WIRELESS_CHARGE_FINISH:
                return this.parseWirelessChargeFinish(data);
            case EVT_TYPE.USER_RECYCLE_WIRELESS_CHARGER_STOP_CHARGE:
                return this.parseUserRecycleWirelessChargerStopCharge(data);
            default:
                return {
                    hex: bytesToHex(data),
                    description: "未知事件类型数据"
                };
        }
    }
} 

// 事件回复数据解析
class EventRspParsers {
    // 用户超时未接入充电器事件 (0x01)
    static parseUserTimeoutNoPlugIn(data) {
        const sessionId = (data[0] << 8) | data[1];
        const plugId = data[2];
        
        return {
            sessionId: sessionId,
            plugId: plugId,
            description: `会话ID: ${sessionId}, 插座${plugId}用户超时未接入充电器`
        };
    }

    // 用户主动拔出充电器事件 (0x02)
    static parseUserPullOutPlug(data) {
        const sessionId = (data[0] << 8) | data[1];
        const plugId = data[2];
        
        return {
            sessionId: sessionId,
            plugId: plugId,
            description: `会话ID: ${sessionId}, 插座${plugId}用户主动拔出充电器`
        };
    }

    // 电动车充满事件 (0x03)
    static parseUserChargeOk(data) {
        const sessionId = (data[0] << 8) | data[1];
        const plugId = data[2];
        
        return {
            sessionId: sessionId,
            plugId: plugId,
            description: `会话ID: ${sessionId}, 插座${plugId}电动车充满`
        };
    }

    // 插座功率长期过高 (0x04)
    static parsePlugPowerOverLimit(data) {
        const plugId = data[0];
        const power = (data[1] << 8) | data[2];
        
        return {
            plugId: plugId,
            power: power,
            description: `插座${plugId}功率长期过高: ${power}W`
        };
    }

    // 插座功率超过安全限制 (0x05)
    static parsePlugPowerOverSafeLimit(data) {
        const plugId = data[0];
        const power = (data[1] << 8) | data[2];
        
        return {
            plugId: plugId,
            power: power,
            description: `插座${plugId}功率超过安全限制: ${power}W`
        };
    }

    // 系统总功率超过限制被切断 (0x06)
    static parseMainPowerOverTotalLimit(data) {
        const totalPower = (data[0] << 8) | data[1];
        
        return {
            totalPower: totalPower,
            description: `系统总功率超过限制被切断: ${totalPower}W`
        };
    }

    // 主板温度超过限制被断开 (0x07)
    static parseMainBoardTempOverLimit(data) {
        const temperature = data[0];
        
        return {
            temperature: temperature,
            description: `主板温度超过限制被断开: ${temperature}°C`
        };
    }

    // 中控疑似被断电 (0x08)
    static parseMainPowerOffSuspected(data) {
        const powerOffTime = (data[0] << 8) | data[1];
        
        return {
            powerOffTime: powerOffTime,
            description: `中控疑似被断电: ${powerOffTime}秒`
        };
    }

    // 充电过程中继电器异常开路 (0x09)
    static parseRelayOpenCircuit(data) {
        const plugId = data[0];
        
        return {
            plugId: plugId,
            description: `插座${plugId}充电过程中继电器异常开路`
        };
    }

    // 无线充电完成事件 (0x81)
    static parseWirelessChargeFinish(data) {
        const plugId = data[0];
        const status = data[1];
        const errorCode = (data[2] << 8) | data[3];
        
        let statusText = "";
        switch (status) {
            case 0x00:
                statusText = "正常完成";
                break;
            case 0x01:
                statusText = "异常完成";
                break;
            default:
                statusText = "未知状态";
        }
        
        return {
            plugId: plugId,
            status: status,
            statusText: statusText,
            errorCode: errorCode,
            description: `无线插座${plugId}充电完成, 状态: ${statusText}, 错误码: 0x${errorCode.toString(16).toUpperCase()}`
        };
    }

    // 用户回收无线充电器停止充电事件 (0x82)
    static parseUserRecycleWirelessChargerStopCharge(data) {
        const plugId = data[0];
        
        return {
            plugId: plugId,
            description: `用户回收无线插座${plugId}充电器停止充电`
        };
    }

    // 根据事件类型解析数据
    static parse(evtType, data) {
        switch (evtType) {
            case EVT_TYPE.USER_TIMEOUT_NO_PLUG_IN:
                return this.parseUserTimeoutNoPlugIn(data);
            case EVT_TYPE.USER_PULL_OUT_PLUG:
                return this.parseUserPullOutPlug(data);
            case EVT_TYPE.USER_CHARGE_OK:
                return this.parseUserChargeOk(data);
            case EVT_TYPE.PLUG_POWER_OVER_LIMIT:
                return this.parsePlugPowerOverLimit(data);
            case EVT_TYPE.PLUG_POWER_OVER_SAFE_LIMIT:
                return this.parsePlugPowerOverSafeLimit(data);
            case EVT_TYPE.MAIN_POWER_OVER_TOTAL_LIMIT:
                return this.parseMainPowerOverTotalLimit(data);
            case EVT_TYPE.MAIN_BOARD_TEMP_OVER_LIMIT:
                return this.parseMainBoardTempOverLimit(data);
            case EVT_TYPE.MAIN_POWER_OFF_SUSPECTED:
                return this.parseMainPowerOffSuspected(data);
            case EVT_TYPE.RELAY_OPEN_CIRCUIT:
                return this.parseRelayOpenCircuit(data);
            case EVT_TYPE.WIRELESS_CHARGE_FINISH:
                return this.parseWirelessChargeFinish(data);
            case EVT_TYPE.USER_RECYCLE_WIRELESS_CHARGER_STOP_CHARGE:
                return this.parseUserRecycleWirelessChargerStopCharge(data);
            default:
                return {
                    hex: bytesToHex(data),
                    description: "未知事件类型数据"
                };
        }
    }
} 