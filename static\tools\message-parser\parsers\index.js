
// 根据消息类型和对象解析数据
function parseMessageData(msgType, msgObj, data) {
    if (data.length === 0) return null;

    switch (msgType) {
        case MSG_TYPE.REQ:
            return RequestParsers.parse(msgObj, data);
        case MSG_TYPE.REQ_RSP:
            return RequestRspParsers.parse(msgObj, data);
        case MSG_TYPE.CMD:
            return CommandParsers.parse(msgObj, data);
        case MSG_TYPE.CMD_RSP:
            return CommandRspParsers.parse(msgObj, data);
        case MSG_TYPE.EVT:
            return EventParsers.parse(msgObj, data);
        case MSG_TYPE.EVT_RSP:
            return EventRspParsers.parse(msgObj, data);
        // case MSG_TYPE.SET:
        //     return SetParsers.parse(msgObj, data);
        // case MSG_TYPE.SET_RSP:
        //     return SetRspParsers.parse(msgObj, data);
        // case MSG_TYPE.READ:
        //     return ReadParsers.parse(msgObj, data);
        // case MSG_TYPE.READ_RSP:
        //     return ReadRspParsers.parse(msgObj, data);
        default:
            return {
                hex: bytesToHex(data),
                description: "未知消息类型数据"
            };
    }
}
