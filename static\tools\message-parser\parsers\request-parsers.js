
// 请求数据解析
class RequestParsers {
    // 插座继电器故障上报请求 (0x01)
    static parsePlugRelayFaultReport(data) {
        return {
            plugId: data[0],
            faultCode: data[1],
            description: `插座${data[0]}故障，故障码: 0x${data[1].toString(16).toUpperCase()}`
        };
    }

    // 心跳请求 (0x02)
    static parseHeart(data) {
        const heartSeq = (data[0] << 8) | data[1];
        const plugNum = data[2];
        
        const result = {
            heartSeq: heartSeq,
            plugNum: plugNum,
            plugStatus: [],
            description: `心跳序号: ${heartSeq}, 插座数量: ${plugNum}`
        };
        
        // 解析插座状态
        for (let i = 0; i < Math.min(plugNum, data.length - 3); i++) {
            result.plugStatus.push(data[3 + i]);
        }
        
        return result;
    }

    // 开始无线充电的请求 (0x03)
    static parseStartWirelessCharge(data) {
        const plugId = data[0];
        const power = (data[1] << 8) | data[2];
        
        return {
            plugId: plugId,
            power: power,
            description: `无线插座${plugId}启动充电，功率设置: ${power}W`
        };
    }

    // 无线充电发射模块充电口编号映射获取请求 (0x04)
    static parseWirelessPlugIdMap(data) {
        const plugId = data[0];
        const mapData = data.slice(1);
        
        return {
            plugId: plugId,
            mapData: bytesToHex(mapData),
            description: `无线插座${plugId}编号映射`
        };
    }

    // 根据请求类型解析数据
    static parse(reqType, data) {
        switch (reqType) {
            case REQ_TYPE.PLUG_RELAY_FAULT_REPORT:
                return this.parsePlugRelayFaultReport(data);
            case REQ_TYPE.HEART:
                return this.parseHeart(data);
            case REQ_TYPE.START_WIRELESS_CHARGE:
                return this.parseStartWirelessCharge(data);
            case REQ_TYPE.WIRELESS_PLUG_ID_MAP:
                return this.parseWirelessPlugIdMap(data);
            default:
                return {
                    hex: bytesToHex(data),
                    description: "未知请求类型数据"
                };
        }
    }
} 

// 请求回复数据解析
class RequestRspParsers {
    // 插座继电器故障上报请求 (0x01)
    static parsePlugRelayFaultReport(data) {
        return {
            plugId: data[0],
            faultCode: data[1],
            description: `插座${data[0]}故障，故障码: 0x${data[1].toString(16).toUpperCase()}`
        };
    }

    // 心跳请求 (0x02)
    static parseHeart(data) {
        const heartSeq = (data[0] << 8) | data[1];
        const plugNum = data[2];
        
        const result = {
            heartSeq: heartSeq,
            plugNum: plugNum,
            plugStatus: [],
            description: `心跳序号: ${heartSeq}, 插座数量: ${plugNum}`
        };
        
        // 解析插座状态
        for (let i = 0; i < Math.min(plugNum, data.length - 3); i++) {
            result.plugStatus.push(data[3 + i]);
        }
        
        return result;
    }

    // 开始无线充电的请求 (0x03)
    static parseStartWirelessCharge(data) {
        const plugId = data[0];
        const power = (data[1] << 8) | data[2];
        
        return {
            plugId: plugId,
            power: power,
            description: `无线插座${plugId}启动充电，功率设置: ${power}W`
        };
    }

    // 无线充电发射模块充电口编号映射获取请求 (0x04)
    static parseWirelessPlugIdMap(data) {
        const plugId = data[0];
        const mapData = data.slice(1);
        
        return {
            plugId: plugId,
            mapData: bytesToHex(mapData),
            description: `无线插座${plugId}编号映射`
        };
    }

    // 根据请求类型解析数据
    static parse(reqType, data) {
        switch (reqType) {
            case REQ_TYPE.PLUG_RELAY_FAULT_REPORT:
                return this.parsePlugRelayFaultReport(data);
            case REQ_TYPE.HEART:
                return this.parseHeart(data);
            case REQ_TYPE.START_WIRELESS_CHARGE:
                return this.parseStartWirelessCharge(data);
            case REQ_TYPE.WIRELESS_PLUG_ID_MAP:
                return this.parseWirelessPlugIdMap(data);
            default:
                return {
                    hex: bytesToHex(data),
                    description: "未知请求类型数据"
                };
        }
    }
} 