// 协议常量定义
const PROTOCOL = {
    MAGIC_HEADER: 0x55AA,
    PROTOCOL_VERSION_MIN: 0x0003,
    PROTOCOL_VERSION_CURR: 0x0003,
    PROTOCOL_VERSION_MAX: 0x0004,
    HEA<PERSON>R_LEN: 17,
    CRC_LEN: 2,
    CURR_VERSION_FIXED_PART_LEN: 2,
    FIXED_PART_LEN: 21, // HEADER_LEN + CRC_LEN + CURR_VERSION_FIXED_PART_LEN
    MAX_DATA_LEN: 406,
    MAX_MSG_LEN: 427, // MAX_DATA_LEN + FIXED_PART_LEN
};

// 消息类型定义
const MSG_TYPE = {
    REQ: 0x00,
    REQ_RSP: 0x01,
    CMD: 0x02,
    CMD_RSP: 0x03,
    EVT: 0x04,
    EVT_RSP: 0x05,
    SET: 0x06,
    SET_RSP: 0x07,
    READ: 0x08,
    READ_RSP: 0x09,
};

// 请求类型定义
const REQ_TYPE = {
    PLUG_RELAY_FAULT_REPORT: 0x01,
    HEART: 0x02,
    START_WIRELESS_CHARGE: 0x03,
    WIRELESS_PLUG_ID_MAP: 0x04,
};

// 命令类型定义
const CMD_TYPE = {
    HEARTBEAT: 0x01,
    START_CHARGE: 0x02,
    RESTART_CHARGE_SESSION: 0x03,
    BILLING_TIME_SLICE: 0x04,
    STOP_CHARGE: 0x05,
    HEARTBEAT_AND_BILLING: 0x06,
    OTA_START: 0x07,
    OTA_ABORT: 0x08,
    OTA_RESULT_QUERY: 0x09,
    DEVICE_FORCE_REBOOT: 0x0A,
    OTA_DATA_TRANSFER: 0x0B,
    START_WIRELESS_CHARGE: 0x81,
    USER_STOP_WIRELESS_CHARGE: 0x82,
};

// 事件类型定义
const EVT_TYPE = {
    USER_TIMEOUT_NO_PLUG_IN: 0x01,
    USER_PULL_OUT_PLUG: 0x02,
    USER_CHARGE_OK: 0x03,
    PLUG_POWER_OVER_LIMIT: 0x04,
    PLUG_POWER_OVER_SAFE_LIMIT: 0x05,
    MAIN_POWER_OVER_TOTAL_LIMIT: 0x06,
    MAIN_BOARD_TEMP_OVER_LIMIT: 0x07,
    MAIN_POWER_OFF_SUSPECTED: 0x08,
    RELAY_OPEN_CIRCUIT: 0x09,
    WIRELESS_CHARGE_FINISH: 0x81,
    USER_RECYCLE_WIRELESS_CHARGER_STOP_CHARGE: 0x82,
};

// 无线充电状态码定义
const WCT_STATUS = {
    IDLE: 0x00,
    READY: 0x41,
    FAULT: 0x61,
    CHARGING: 0x62,
};

// 无线充电功能码定义
const WCT_FUNC = {
    QUERY_STATUS: 0x00,
    STOP_OUTPUT: 0x80,
    ENABLE_OUTPUT: 0xA0,
};

// 获取消息类型字符串
function getMsgTypeString(type) {
    const types = {
        [MSG_TYPE.REQ]: "请求",
        [MSG_TYPE.REQ_RSP]: "请求回复",
        [MSG_TYPE.CMD]: "命令",
        [MSG_TYPE.CMD_RSP]: "命令回复",
        [MSG_TYPE.EVT]: "事件",
        [MSG_TYPE.EVT_RSP]: "事件回复",
        [MSG_TYPE.SET]: "设置",
        [MSG_TYPE.SET_RSP]: "设置回复",
        [MSG_TYPE.READ]: "读取",
        [MSG_TYPE.READ_RSP]: "读取回复",
    };
    return types[type] || "未知消息类型";
}

// 获取请求类型字符串
function getReqTypeString(type) {
    const types = {
        [REQ_TYPE.PLUG_RELAY_FAULT_REPORT]: "插座继电器故障上报",
        [REQ_TYPE.HEART]: "心跳请求",
        [REQ_TYPE.START_WIRELESS_CHARGE]: "开始无线充电",
        [REQ_TYPE.WIRELESS_PLUG_ID_MAP]: "无线充电发射模块充电口编号映射获取",
    };
    return types[type] || "未知请求类型";
}

// 获取命令类型字符串
function getCmdTypeString(type) {
    const types = {
        [CMD_TYPE.HEARTBEAT]: "设备心跳",
        [CMD_TYPE.START_CHARGE]: "启动充电",
        [CMD_TYPE.RESTART_CHARGE_SESSION]: "充电会话重启",
        [CMD_TYPE.BILLING_TIME_SLICE]: "计费时间片",
        [CMD_TYPE.STOP_CHARGE]: "停止充电",
        [CMD_TYPE.HEARTBEAT_AND_BILLING]: "设备心跳及计费时间片",
        [CMD_TYPE.OTA_START]: "OTA启动",
        [CMD_TYPE.OTA_ABORT]: "OTA中止",
        [CMD_TYPE.OTA_RESULT_QUERY]: "OTA结果查询",
        [CMD_TYPE.DEVICE_FORCE_REBOOT]: "设备强制重启",
        [CMD_TYPE.OTA_DATA_TRANSFER]: "OTA数据传输",
        [CMD_TYPE.START_WIRELESS_CHARGE]: "启动无线充电",
        [CMD_TYPE.USER_STOP_WIRELESS_CHARGE]: "停止无线充电",
    };
    return types[type] || "未知命令类型";
}

// 获取事件类型字符串
function getEvtTypeString(type) {
    const types = {
        [EVT_TYPE.USER_TIMEOUT_NO_PLUG_IN]: "用户超时未接入充电器",
        [EVT_TYPE.USER_PULL_OUT_PLUG]: "用户主动拔出充电器",
        [EVT_TYPE.USER_CHARGE_OK]: "电动车充满",
        [EVT_TYPE.PLUG_POWER_OVER_LIMIT]: "插座功率长期过高",
        [EVT_TYPE.PLUG_POWER_OVER_SAFE_LIMIT]: "插座功率超过安全限制",
        [EVT_TYPE.MAIN_POWER_OVER_TOTAL_LIMIT]: "系统总功率超过限制被切断",
        [EVT_TYPE.MAIN_BOARD_TEMP_OVER_LIMIT]: "主板温度超过限制被断开",
        [EVT_TYPE.MAIN_POWER_OFF_SUSPECTED]: "中控疑似被断电",
        [EVT_TYPE.RELAY_OPEN_CIRCUIT]: "充电过程中继电器异常开路",
        [EVT_TYPE.WIRELESS_CHARGE_FINISH]: "无线充电完成",
        [EVT_TYPE.USER_RECYCLE_WIRELESS_CHARGER_STOP_CHARGE]: "用户回收无线充电器停止充电",
    };
    return types[type] || "未知事件类型";
}

// 获取设置类型字符串
function getSetTypeString(type) {
    const types = {
        [SET_TYPE.SET]: "设置",
    };
    return types[type] || "未知设置类型";
}

// 获取读取类型字符串
function getReadTypeString(type) {
    const types = {
        [READ_TYPE.READ]: "读取",
    };
    return types[type] || "未知读取类型";
}

// 获取无线充电状态字符串
function getWCTStatusString(status) {
    const statuses = {
        [WCT_STATUS.IDLE]: "空闲，无接收设备接入",
        [WCT_STATUS.READY]: "就绪",
        [WCT_STATUS.FAULT]: "错误",
        [WCT_STATUS.CHARGING]: "充电中",
    };
    return statuses[status] || "未知状态";
}

// 获取无线充电功能码字符串
function getWCTFuncString(func) {
    const funcs = {
        [WCT_FUNC.QUERY_STATUS]: "状态查询",
        [WCT_FUNC.STOP_OUTPUT]: "停止输出",
        [WCT_FUNC.ENABLE_OUTPUT]: "使能输出",
    };
    return funcs[func] || "状态查询";
}

// CRC16 Modbus计算函数 - 与C代码实现一致
function CRC16_Modbus(data) {
    // CRC 高位字节值表
    const s_CRCHi = [
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0,
        0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0,
        0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1,
        0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1,
        0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0,
        0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40,
        0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1,
        0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0,
        0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40,
        0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0,
        0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0,
        0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0,
        0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
        0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0,
        0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40,
        0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1,
        0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0,
        0x80, 0x41, 0x00, 0xC1, 0x81, 0x40
    ];
    
    // CRC 低位字节值表
    const s_CRCLo = [
        0x00, 0xC0, 0xC1, 0x01, 0xC3, 0x03, 0x02, 0xC2, 0xC6, 0x06,
        0x07, 0xC7, 0x05, 0xC5, 0xC4, 0x04, 0xCC, 0x0C, 0x0D, 0xCD,
        0x0F, 0xCF, 0xCE, 0x0E, 0x0A, 0xCA, 0xCB, 0x0B, 0xC9, 0x09,
        0x08, 0xC8, 0xD8, 0x18, 0x19, 0xD9, 0x1B, 0xDB, 0xDA, 0x1A,
        0x1E, 0xDE, 0xDF, 0x1F, 0xDD, 0x1D, 0x1C, 0xDC, 0x14, 0xD4,
        0xD5, 0x15, 0xD7, 0x17, 0x16, 0xD6, 0xD2, 0x12, 0x13, 0xD3,
        0x11, 0xD1, 0xD0, 0x10, 0xF0, 0x30, 0x31, 0xF1, 0x33, 0xF3,
        0xF2, 0x32, 0x36, 0xF6, 0xF7, 0x37, 0xF5, 0x35, 0x34, 0xF4,
        0x3C, 0xFC, 0xFD, 0x3D, 0xFF, 0x3F, 0x3E, 0xFE, 0xFA, 0x3A,
        0x3B, 0xFB, 0x39, 0xF9, 0xF8, 0x38, 0x28, 0xE8, 0xE9, 0x29,
        0xEB, 0x2B, 0x2A, 0xEA, 0xEE, 0x2E, 0x2F, 0xEF, 0x2D, 0xED,
        0xEC, 0x2C, 0xE4, 0x24, 0x25, 0xE5, 0x27, 0xE7, 0xE6, 0x26,
        0x22, 0xE2, 0xE3, 0x23, 0xE1, 0x21, 0x20, 0xE0, 0xA0, 0x60,
        0x61, 0xA1, 0x63, 0xA3, 0xA2, 0x62, 0x66, 0xA6, 0xA7, 0x67,
        0xA5, 0x65, 0x64, 0xA4, 0x6C, 0xAC, 0xAD, 0x6D, 0xAF, 0x6F,
        0x6E, 0xAE, 0xAA, 0x6A, 0x6B, 0xAB, 0x69, 0xA9, 0xA8, 0x68,
        0x78, 0xB8, 0xB9, 0x79, 0xBB, 0x7B, 0x7A, 0xBA, 0xBE, 0x7E,
        0x7F, 0xBF, 0x7D, 0xBD, 0xBC, 0x7C, 0xB4, 0x74, 0x75, 0xB5,
        0x77, 0xB7, 0xB6, 0x76, 0x72, 0xB2, 0xB3, 0x73, 0xB1, 0x71,
        0x70, 0xB0, 0x50, 0x90, 0x91, 0x51, 0x93, 0x53, 0x52, 0x92,
        0x96, 0x56, 0x57, 0x97, 0x55, 0x95, 0x94, 0x54, 0x9C, 0x5C,
        0x5D, 0x9D, 0x5F, 0x9F, 0x9E, 0x5E, 0x5A, 0x9A, 0x9B, 0x5B,
        0x99, 0x59, 0x58, 0x98, 0x88, 0x48, 0x49, 0x89, 0x4B, 0x8B,
        0x8A, 0x4A, 0x4E, 0x8E, 0x8F, 0x4F, 0x8D, 0x4D, 0x4C, 0x8C,
        0x44, 0x84, 0x85, 0x45, 0x87, 0x47, 0x46, 0x86, 0x82, 0x42,
        0x43, 0x83, 0x41, 0x81, 0x80, 0x40
    ];

    let ucCRCHi = 0xFF; /* 高CRC字节初始化 */
    let ucCRCLo = 0xFF; /* 低CRC 字节初始化 */
    let usIndex;       /* CRC循环中的索引 */

    for (let i = 0; i < data.length; i++) {
        usIndex = ucCRCHi ^ data[i]; /* 计算CRC */
        ucCRCHi = ucCRCLo ^ s_CRCHi[usIndex];
        ucCRCLo = s_CRCLo[usIndex];
    }
    
    return ((ucCRCHi << 8) | ucCRCLo);
}

// 将字节数组转换为十六进制字符串
function bytesToHex(bytes) {
    return Array.from(bytes).map(b => b.toString(16).padStart(2, '0').toUpperCase()).join(' ');
}

// 将十六进制字符串转换为字节数组
function hexToBytes(hex) {
    // 移除0x前缀和空格
    hex = hex.replace(/0x/g, '').replace(/\s/g, '');
    return hex.match(/.{1,2}/g).map(byte => parseInt(byte, 16));
}

// 解析设备ID
function parseDeviceId(bytes, offset) {
    return ((bytes[offset] << 32) | 
            (bytes[offset + 1] << 24) | 
            (bytes[offset + 2] << 16) | 
            (bytes[offset + 3] << 8) | 
            bytes[offset + 4]).toString(16).toUpperCase();
}

// 解析时间戳
function parseTimestamp(bytes, offset) {
    return ((bytes[offset] << 40) | 
            (bytes[offset + 1] << 32) | 
            (bytes[offset + 2] << 24) | 
            (bytes[offset + 3] << 16) | 
            (bytes[offset + 4] << 8) | 
            bytes[offset + 5]);
}

// 格式化时间戳
function formatTimestamp(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleString();
}

// 解析无线充电状态
function parseWCTStatus(bytes, offset) {
    return bytes[offset];
}

// 解析无线充电功能码
function parseWCTFunc(bytes, offset) {
    return bytes[offset];
}

// 解析无线充电功率
function parseWCTPower(bytes, offset) {
    return (bytes[offset] << 8) | bytes[offset + 1];
}

// 解析无线充电电压
function parseWCTVoltage(bytes, offset) {
    return (bytes[offset] << 8) | bytes[offset + 1];
}

// 解析无线充电电流
function parseWCTCurrent(bytes, offset) {
    return (bytes[offset] << 8) | bytes[offset + 1];
}

// 解析无线充电温度
function parseWCTTemperature(bytes, offset) {
    return bytes[offset];
}

// 解析无线充电错误码
function parseWCTErrorCode(bytes, offset) {
    return (bytes[offset] << 8) | bytes[offset + 1];
}

// 解析无线充电充电口编号
function parseWCTPlugId(bytes, offset) {
    return bytes[offset];
}

// 解析无线充电充电口映射
function parseWCTPlugMap(bytes, offset, length) {
    const map = [];
    for (let i = 0; i < length; i++) {
        map.push(bytes[offset + i]);
    }
    return map;
} 