{% extends "base.html" %}

{% block title %}AI助手{% endblock %}

{% block styles %}
<!-- 引入Markdown渲染库 -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/github-markdown-css/github-markdown.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/highlight.js@11.7.0/styles/github.min.css">
<!-- 引入字体图标 -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<!-- 引入动画库 -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
<style>
    :root {
        --primary-color: #4e73df;
        --primary-dark: #2e59d9;
        --secondary-color: #6f42c1;
        --success-color: #1cc88a;
        --info-color: #36b9cc;
        --warning-color: #f6c23e;
        --danger-color: #e74a3b;
        --light-color: #f8f9fc;
        --dark-color: #5a5c69;
        --border-radius: 0.5rem;
        --box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        --transition-speed: 0.3s;
    }

    body {
        background-color: #f8f9fc;
    }

    .chat-container {
        height: calc(100vh - 300px);
        min-height: 500px;
        display: flex;
        flex-direction: column;
        transition: all var(--transition-speed);
    }

    .chat-messages {
        flex: 1;
        overflow-y: auto;
        padding: 20px;
        background: #fff;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        margin-bottom: 20px;
        scroll-behavior: smooth;
    }

    .chat-input {
        height: 100px;
        padding: 20px 0;
        background: #fff;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        transition: all var(--transition-speed);
    }

    .message {
        margin-bottom: 20px;
        max-width: 80%;
        animation: fadeIn 0.5s ease-in-out;
        display: flex;
        flex-direction: column;
    }

    .message.user {
        margin-left: auto;
        align-items: flex-end;
    }

    .message.assistant {
        margin-right: auto;
        align-items: flex-start;
    }

    .message .content {
        padding: 15px 20px;
        border-radius: var(--border-radius);
        position: relative;
        box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        transition: all var(--transition-speed);
    }

    .message.user .content {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        border-bottom-right-radius: 0;
    }

    .message.assistant .content {
        background: #f8f9fc;
        color: #333;
        border-bottom-left-radius: 0;
    }

    .message .time {
        font-size: 12px;
        color: #858796;
        margin-top: 5px;
        opacity: 0.8;
    }

    .message.user .time {
        text-align: right;
    }

    .typing-indicator {
        display: none;
        margin-bottom: 20px;
        animation: fadeIn 0.3s ease-in-out;
    }

    .typing-indicator span {
        height: 10px;
        width: 10px;
        float: left;
        margin: 0 1px;
        background-color: #9e9ea1;
        display: block;
        border-radius: 50%;
        opacity: 0.4;
    }

    .typing-indicator span:nth-of-type(1) {
        animation: 1s blink infinite 0.3333s;
    }

    .typing-indicator span:nth-of-type(2) {
        animation: 1s blink infinite 0.6666s;
    }

    .typing-indicator span:nth-of-type(3) {
        animation: 1s blink infinite 0.9999s;
    }

    @keyframes blink {
        50% {
            opacity: 1;
        }
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .system-status {
        background: #fff;
        border-radius: var(--border-radius);
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: var(--box-shadow);
        transition: all var(--transition-speed);
        border-left: 4px solid var(--primary-color);
    }

    .system-status:hover {
        transform: translateY(-2px);
        box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2);
    }

    .system-status h6 {
        color: var(--primary-color);
        margin-bottom: 15px;
        font-weight: 600;
        display: flex;
        align-items: center;
    }

    .system-status h6 i {
        margin-right: 8px;
    }

    .status-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        font-size: 0.9rem;
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;
    }

    .status-item:last-child {
        border-bottom: none;
    }

    .status-item .label {
        color: #858796;
    }

    .status-item .value {
        font-weight: bold;
        color: var(--primary-color);
    }

    .quick-actions {
        display: flex;
        gap: 10px;
        margin-bottom: 20px;
        flex-wrap: wrap;
    }

    .quick-action-btn {
        padding: 8px 15px;
        border-radius: 20px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        border: none;
        cursor: pointer;
        font-size: 0.9rem;
        transition: all var(--transition-speed);
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        display: flex;
        align-items: center;
    }

    .quick-action-btn i {
        margin-right: 8px;
    }

    .quick-action-btn:hover {
        background: linear-gradient(135deg, var(--primary-dark), var(--secondary-color));
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    /* Markdown样式 */
    .markdown-body {
        background-color: transparent !important;
        font-size: 14px;
        line-height: 1.6;
    }

    .message.assistant .markdown-body {
        color: #333;
    }

    .message.user .markdown-body {
        color: white;
    }

    .message.assistant .markdown-body pre {
        background-color: #f6f8fa;
        border-radius: 6px;
        padding: 16px;
        overflow: auto;
        border: 1px solid #e1e4e8;
    }

    .message.assistant .markdown-body code {
        background-color: rgba(27, 31, 35, 0.05);
        border-radius: 3px;
        font-size: 85%;
        margin: 0;
        padding: 0.2em 0.4em;
    }

    .message.assistant .markdown-body pre code {
        background-color: transparent;
        padding: 0;
    }

    .message.assistant .markdown-body table {
        border-collapse: collapse;
        width: 100%;
        margin-bottom: 16px;
        border-radius: 6px;
        overflow: hidden;
    }

    .message.assistant .markdown-body table th,
    .message.assistant .markdown-body table td {
        padding: 8px 13px;
        border: 1px solid #dfe2e5;
    }

    .message.assistant .markdown-body table th {
        background-color: #f6f8fa;
        font-weight: 600;
    }

    .message.assistant .markdown-body table tr {
        background-color: #fff;
        border-top: 1px solid #c6cbd1;
    }

    .message.assistant .markdown-body table tr:nth-child(2n) {
        background-color: #f6f8fa;
    }

    .message.assistant .markdown-body blockquote {
        padding: 0 1em;
        color: #6a737d;
        border-left: 0.25em solid #dfe2e5;
        margin: 0 0 16px 0;
        background-color: #f6f8fa;
        border-radius: 0 6px 6px 0;
    }

    .message.assistant .markdown-body ul,
    .message.assistant .markdown-body ol {
        padding-left: 2em;
        margin-top: 0;
        margin-bottom: 16px;
    }

    .message.assistant .markdown-body h1,
    .message.assistant .markdown-body h2,
    .message.assistant .markdown-body h3,
    .message.assistant .markdown-body h4,
    .message.assistant .markdown-body h5,
    .message.assistant .markdown-body h6 {
        margin-top: 24px;
        margin-bottom: 16px;
        font-weight: 600;
        line-height: 1.25;
        color: var(--primary-color);
    }

    .message.assistant .markdown-body h1 {
        font-size: 2em;
        border-bottom: 1px solid #eaecef;
        padding-bottom: 0.3em;
    }

    .message.assistant .markdown-body h2 {
        font-size: 1.5em;
        border-bottom: 1px solid #eaecef;
        padding-bottom: 0.3em;
    }

    .message.assistant .markdown-body h3 {
        font-size: 1.25em;
    }

    .message.assistant .markdown-body h4 {
        font-size: 1em;
    }

    .message.assistant .markdown-body h5 {
        font-size: 0.875em;
    }

    .message.assistant .markdown-body h6 {
        font-size: 0.85em;
        color: #6a737d;
    }

    /* 输入框样式 */
    .input-group {
        box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        border-radius: var(--border-radius);
        overflow: hidden;
    }

    .form-control {
        border: none;
        padding: 15px 20px;
        font-size: 1rem;
        transition: all var(--transition-speed);
    }

    .form-control:focus {
        box-shadow: none;
        border-color: var(--primary-color);
    }

    .btn-primary {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        border: none;
        padding: 0 25px;
        font-weight: 500;
        transition: all var(--transition-speed);
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, var(--primary-dark), var(--secondary-color));
        transform: translateY(-2px);
    }

    /* 按钮样式 */
    .btn {
        border-radius: var(--border-radius);
        padding: 8px 15px;
        font-weight: 500;
        transition: all var(--transition-speed);
    }

    .btn-info {
        background: linear-gradient(135deg, var(--info-color), #2c9faf);
        border: none;
        color: white;
    }

    .btn-info:hover {
        background: linear-gradient(135deg, #2c9faf, var(--info-color));
        transform: translateY(-2px);
    }

    .btn-danger {
        background: linear-gradient(135deg, var(--danger-color), #d52a1a);
        border: none;
        color: white;
    }

    .btn-danger:hover {
        background: linear-gradient(135deg, #d52a1a, var(--danger-color));
        transform: translateY(-2px);
    }

    /* 卡片样式 */
    .card {
        border: none;
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
        transition: all var(--transition-speed);
    }

    .card:hover {
        box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2);
    }

    .card-header {
        background: linear-gradient(135deg, #f8f9fc, #e3e6f0);
        border-bottom: 1px solid rgba(0,0,0,0.05);
        padding: 15px 20px;
    }

    .card-header h6 {
        margin: 0;
        font-weight: 600;
        color: var(--primary-color);
    }

    .card-body {
        padding: 20px;
    }

    /* 滚动条样式 */
    ::-webkit-scrollbar {
        width: 8px;
    }

    ::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 10px;
    }

    ::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 10px;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

    /* 响应式调整 */
    @media (max-width: 768px) {
        .chat-container {
            height: calc(100vh - 250px);
        }
        
        .message {
            max-width: 90%;
        }
        
        .quick-actions {
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">AI助手</h1>

    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-robot mr-2"></i>与AI助手对话
                    </h6>
                    <div>
                        <button id="refreshStatus" class="btn btn-sm btn-info mr-2">
                            <i class="fas fa-sync-alt"></i> 刷新状态
                        </button>
                        <button id="clearHistory" class="btn btn-sm btn-danger">
                            <i class="fas fa-trash"></i> 清除历史
                        </button>
                    </div>
                </div>
                <div class="card-body chat-container">
                    <!-- 系统状态 -->
                    <div class="system-status animate__animated animate__fadeIn">
                        <h6><i class="fas fa-info-circle"></i> 系统状态</h6>
                        <div class="status-item">
                            <span class="label">总设备数：</span>
                            <span class="value" id="totalDevices">-</span>
                        </div>
                        <div class="status-item">
                            <span class="label">最近24小时OTA任务：</span>
                            <span class="value" id="recentOtaTasks">-</span>
                        </div>
                    </div>

                    <!-- 快捷操作 -->
                    <div class="quick-actions animate__animated animate__fadeIn">
                        <button class="quick-action-btn" onclick="askQuestion('查看所有设备')">
                            <i class="fas fa-list"></i> 查看设备
                        </button>
                        <button class="quick-action-btn" onclick="askQuestion('查看最近的OTA任务')">
                            <i class="fas fa-tasks"></i> OTA任务
                        </button>
                        <button class="quick-action-btn" onclick="askQuestion('查看可用固件')">
                            <i class="fas fa-file-code"></i> 固件列表
                        </button>
                        <button class="quick-action-btn" onclick="askQuestion('分析设备健康状况')">
                            <i class="fas fa-heartbeat"></i> 健康分析
                        </button>
                    </div>

                    <div class="chat-messages" id="chatMessages">
                        <!-- 消息将在这里动态添加 -->
                        <div class="message assistant animate__animated animate__fadeIn">
                            <div class="content markdown-body">
                                <h4>👋 您好！我是您的充电桩设备管理助手</h4>
                                <p>我可以帮您：</p>
                                <ul>
                                    <li>查看设备状态和在线情况</li>
                                    <li>监控OTA升级任务</li>
                                    <li>管理固件版本</li>
                                    <li>分析设备健康状况</li>
                                    <li>提供故障诊断建议</li>
                                </ul>
                                <p>请问有什么可以帮您？</p>
                            </div>
                            <div class="time">系统消息</div>
                        </div>

                        <!-- 输入指示器 -->
                        <div class="typing-indicator" id="typingIndicator">
                            <div class="message assistant">
                                <div class="content">
                                    <span></span>
                                    <span></span>
                                    <span></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="chat-input">
                        <div class="input-group">
                            <input type="text" id="messageInput" class="form-control"
                                placeholder="输入您的问题...">
                            <div class="input-group-append">
                                <button class="btn btn-primary" type="button" id="sendMessage">
                                    <i class="fas fa-paper-plane"></i> 发送
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- 引入Markdown渲染库 -->
<script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/highlight.js@11.7.0/lib/highlight.min.js"></script>
<script>
    // 配置Marked
    marked.setOptions({
        renderer: new marked.Renderer(),
        highlight: function(code, lang) {
            if (lang && hljs.getLanguage(lang)) {
                try {
                    return hljs.highlight(code, { language: lang }).value;
                } catch (e) {
                    console.error(e);
                }
            }
            return hljs.highlightAuto(code).value;
        },
        pedantic: false,
        gfm: true,
        breaks: true,
        sanitize: false,
        smartypants: false,
        xhtml: false
    });

    function scrollToBottom() {
        const chatMessages = document.getElementById('chatMessages');
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    function showTypingIndicator() {
        $('#typingIndicator').show();
        scrollToBottom();
    }

    function hideTypingIndicator() {
        $('#typingIndicator').hide();
    }

    function addMessage(content, isUser = false) {
        const time = new Date().toLocaleTimeString();
        let formattedContent = content;
        
        // 如果不是用户消息，则渲染Markdown
        if (!isUser) {
            formattedContent = marked.parse(content);
        }
        
        const messageHtml = `
            <div class="message ${isUser ? 'user' : 'assistant'} animate__animated animate__fadeIn">
                <div class="content ${isUser ? '' : 'markdown-body'}">${formattedContent}</div>
                <div class="time">${time}</div>
            </div>
        `;
        
        const typingIndicator = document.getElementById('typingIndicator');
        $(messageHtml).insertBefore(typingIndicator);
        
        // 对新添加的代码块应用高亮
        if (!isUser) {
            document.querySelectorAll('pre code').forEach((block) => {
                hljs.highlightBlock(block);
            });
        }
        
        scrollToBottom();
    }

    function updateSystemStatus() {
        // 添加加载动画
        $('#totalDevices').html('<i class="fas fa-spinner fa-spin"></i>');
        $('#recentOtaTasks').html('<i class="fas fa-spinner fa-spin"></i>');
        
        $.ajax({
            url: "{{ url_for('ai.get_system_status') }}",
            type: "GET",
            success: function(response) {
                if (response.success) {
                    const status = response.data;
                    $('#totalDevices').text(status.total_devices);
                    $('#recentOtaTasks').text(
                        `总数: ${status.recent_ota_tasks.total}, ` +
                        `成功: ${status.recent_ota_tasks.success}, ` +
                        `失败: ${status.recent_ota_tasks.failed}, ` +
                        `等待中: ${status.recent_ota_tasks.pending}`
                    );
                    
                    // 添加更新动画
                    $('.system-status').addClass('animate__animated animate__pulse');
                    setTimeout(() => {
                        $('.system-status').removeClass('animate__animated animate__pulse');
                    }, 1000);
                }
            },
            error: function() {
                $('#totalDevices').text('-');
                $('#recentOtaTasks').text('-');
            }
        });
    }

    function askQuestion(question) {
        $('#messageInput').val(question);
        sendMessage();
    }

    function sendMessage() {
        const messageInput = document.getElementById('messageInput');
        const message = messageInput.value.trim();
        
        if (message) {
            // 添加用户消息
            addMessage(message, true);
            messageInput.value = '';
            
            // 显示输入指示器
            showTypingIndicator();
            
            // 发送到服务器
            $.ajax({
                url: "{{ url_for('ai.chat_api') }}",
                type: "POST",
                contentType: "application/json",
                data: JSON.stringify({
                    message: message
                }),
                success: function(response) {
                    hideTypingIndicator();
                    if (response.success) {
                        addMessage(response.response);
                        // 更新系统状态
                        updateSystemStatus();
                    } else {
                        addMessage(`抱歉，出现了一些问题：${response.message}`);
                    }
                },
                error: function(xhr, status, error) {
                    hideTypingIndicator();
                    addMessage(`抱歉，请求失败：${error}`);
                }
            });
        }
    }

    $(document).ready(function() {
        // 初始化系统状态
        updateSystemStatus();
        
        // 发送按钮点击事件
        $('#sendMessage').click(sendMessage);
        
        // 输入框回车事件
        $('#messageInput').keypress(function(e) {
            if (e.which == 13) {
                sendMessage();
            }
        });
        
        // 刷新状态按钮点击事件
        $('#refreshStatus').click(function() {
            updateSystemStatus();
        });
        
        // 清除历史按钮点击事件
        $('#clearHistory').click(function() {
            if (confirm('确定要清除所有聊天历史吗？')) {
                $.ajax({
                    url: "{{ url_for('ai.clear_chat_history') }}",
                    type: "POST",
                    success: function(response) {
                        if (response.success) {
                            $('#chatMessages').html(`
                                <div class="message assistant animate__animated animate__fadeIn">
                                    <div class="content markdown-body">
                                        <h4>👋 聊天历史已清除</h4>
                                        <p>有什么可以帮您？</p>
                                    </div>
                                    <div class="time">${new Date().toLocaleTimeString()}</div>
                                </div>
                                <div class="typing-indicator" id="typingIndicator">
                                    <div class="message assistant">
                                        <div class="content">
                                            <span></span>
                                            <span></span>
                                            <span></span>
                                        </div>
                                    </div>
                                </div>
                            `);
                        } else {
                            alert('清除历史失败：' + response.message);
                        }
                    },
                    error: function(xhr, status, error) {
                        alert('清除历史请求失败：' + error);
                    }
                });
            }
        });
        
        // 添加输入框焦点效果
        $('#messageInput').focus(function() {
            $(this).parent().css('box-shadow', '0 0 0 0.2rem rgba(78, 115, 223, 0.25)');
        }).blur(function() {
            $(this).parent().css('box-shadow', '0 2px 5px rgba(0,0,0,0.05)');
        });
    });
</script>
{% endblock %} 