{% extends "base.html" %}

{% block title %}AI智能分析仪表板{% endblock %}

{% block head %}
<style>
    .dashboard-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        overflow: hidden;
    }

    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    }

    .card-gradient-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .card-gradient-success {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
    }

    .card-gradient-warning {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
    }

    .card-gradient-info {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .stat-label {
        font-size: 0.9rem;
        opacity: 0.9;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .stat-icon {
        font-size: 3rem;
        opacity: 0.3;
    }

    .loading-spinner {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 3px solid rgba(255,255,255,0.3);
        border-radius: 50%;
        border-top-color: white;
        animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }

    .chart-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        padding: 20px;
    }

    .quick-action-btn {
        background: linear-gradient(45deg, #3498db, #2980b9);
        border: none;
        border-radius: 10px;
        padding: 15px;
        color: white;
        transition: all 0.3s ease;
        text-decoration: none;
        display: block;
        text-align: center;
    }

    .quick-action-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(52,152,219,0.3);
        color: white;
        text-decoration: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col">
            <h2><i class="fas fa-chart-line text-primary"></i> AI智能分析仪表板</h2>
            <p class="text-muted">实时监控设备状态，智能分析系统健康</p>
        </div>
        <div class="col-auto">
            <button class="btn btn-primary" onclick="refreshDashboard()">
                <i class="fas fa-sync-alt"></i> 刷新数据
            </button>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row mb-4">
        <!-- 设备健康状况卡片 -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card dashboard-card card-gradient-primary">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="stat-label mb-2">设备健康状况</div>
                            <div class="stat-number" id="health-score">
                                <div class="loading-spinner"></div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-heartbeat stat-icon"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 在线设备数量卡片 -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card dashboard-card card-gradient-success">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="stat-label mb-2">在线设备</div>
                            <div class="stat-number" id="online-devices">
                                <div class="loading-spinner"></div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-plug stat-icon"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 待升级设备卡片 -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card dashboard-card card-gradient-warning">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="stat-label mb-2">待升级设备</div>
                            <div class="stat-number" id="upgrade-pending">
                                <div class="loading-spinner"></div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-upload stat-icon"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 故障预警卡片 -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card dashboard-card card-gradient-info">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="stat-label mb-2">故障预警</div>
                            <div class="stat-number" id="failure-alerts">
                                <div class="loading-spinner"></div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle stat-icon"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 快速操作和图表 -->
    <div class="row mb-4">
        <!-- 快速操作 -->
        <div class="col-lg-4">
            <div class="chart-container">
                <h5 class="mb-4"><i class="fas fa-bolt text-primary"></i> 快速操作</h5>
                <div class="d-grid gap-3">
                    <a href="{{ url_for('ai.chat') }}" class="quick-action-btn">
                        <i class="fas fa-comments mb-2"></i><br>
                        AI助手对话
                    </a>
                    <a href="{{ url_for('device.devices') }}" class="quick-action-btn">
                        <i class="fas fa-list mb-2"></i><br>
                        设备管理
                    </a>
                    <a href="{{ url_for('device_location.device_map') }}" class="quick-action-btn">
                        <i class="fas fa-map mb-2"></i><br>
                        设备地图
                    </a>
                    <button class="quick-action-btn" onclick="generateSystemReport()">
                        <i class="fas fa-file-alt mb-2"></i><br>
                        生成系统报告
                    </button>
                </div>
            </div>
        </div>

        <!-- 设备状态分布图 -->
        <div class="col-lg-8">
            <div class="chart-container">
                <h5 class="mb-4"><i class="fas fa-chart-pie text-primary"></i> 设备状态分布</h5>
                <canvas id="deviceStatusChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- 设备列表 -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">设备智能分析</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="deviceTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>设备ID</th>
                            <th>状态</th>
                            <th>健康度</th>
                            <th>固件版本</th>
                            <th>最后在线时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for device in devices %}
                        <tr>
                            <td>{{ device.device_id }}</td>
                            <td>
                                {% if device.status == 'online' %}
                                <span class="badge badge-success">在线</span>
                                {% else %}
                                <span class="badge badge-danger">离线</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="progress">
                                    <div class="progress-bar bg-info" role="progressbar" style="width: 85%"
                                        aria-valuenow="85" aria-valuemin="0" aria-valuemax="100">85%</div>
                                </div>
                            </td>
                            <td>{{ device.firmware_version }}</td>
                            <td>{{ device.last_online_time }}</td>
                            <td>
                                <a href="{{ url_for('ai.device_analysis', device_id=device.id) }}"
                                    class="btn btn-primary btn-sm">
                                    <i class="fas fa-chart-line"></i> 分析
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    let deviceStatusChart;

    // 初始化页面
    $(document).ready(function() {
        // 初始化DataTables
        $('#deviceTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.21/i18n/Chinese.json"
            },
            "order": [[0, "asc"]],
            "pageLength": 10,
            "responsive": true
        });

        // 加载仪表板数据
        loadDashboardData();

        // 初始化图表
        initDeviceStatusChart();

        // 设置定时刷新
        setInterval(loadDashboardData, 30000); // 30秒刷新一次
    });

    // 加载仪表板数据
    function loadDashboardData() {
        // 加载设备统计
        fetch('/api/devices/stats')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const stats = data.stats;
                    document.getElementById('online-devices').textContent = `${stats.online}/${stats.total}`;

                    // 计算健康分数（在线设备比例）
                    const healthScore = stats.total > 0 ? Math.round((stats.online / stats.total) * 100) : 0;
                    document.getElementById('health-score').textContent = `${healthScore}%`;

                    // 更新图表
                    updateDeviceStatusChart(stats.online, stats.offline);
                }
            })
            .catch(error => {
                console.error('加载设备统计失败:', error);
            });

        // 模拟其他数据（实际项目中应该从API获取）
        setTimeout(() => {
            document.getElementById('upgrade-pending').textContent = Math.floor(Math.random() * 10);
            document.getElementById('failure-alerts').textContent = Math.floor(Math.random() * 5);
        }, 500);
    }

    // 初始化设备状态图表
    function initDeviceStatusChart() {
        const ctx = document.getElementById('deviceStatusChart').getContext('2d');
        deviceStatusChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['在线设备', '离线设备'],
                datasets: [{
                    data: [0, 0],
                    backgroundColor: [
                        'rgba(75, 192, 192, 0.8)',
                        'rgba(255, 99, 132, 0.8)'
                    ],
                    borderColor: [
                        'rgba(75, 192, 192, 1)',
                        'rgba(255, 99, 132, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }

    // 更新设备状态图表
    function updateDeviceStatusChart(online, offline) {
        if (deviceStatusChart) {
            deviceStatusChart.data.datasets[0].data = [online, offline];
            deviceStatusChart.update();
        }
    }

    // 刷新仪表板
    function refreshDashboard() {
        // 显示加载状态
        ['health-score', 'online-devices', 'upgrade-pending', 'failure-alerts'].forEach(id => {
            document.getElementById(id).innerHTML = '<div class="loading-spinner"></div>';
        });

        // 重新加载数据
        loadDashboardData();

        // 显示提示
        showToast('仪表板数据已刷新', 'success');
    }

    // 生成系统报告
    function generateSystemReport() {
        showToast('正在生成系统报告...', 'info');

        // 模拟报告生成
        setTimeout(() => {
            const reportContent = `系统智能分析报告

生成时间: ${new Date().toLocaleString()}

=== 系统概览 ===
设备总数: ${document.getElementById('online-devices').textContent.split('/')[1] || '未知'}
在线设备: ${document.getElementById('online-devices').textContent.split('/')[0] || '未知'}
系统健康分数: ${document.getElementById('health-score').textContent || '未知'}
待升级设备: ${document.getElementById('upgrade-pending').textContent || '未知'}
故障预警: ${document.getElementById('failure-alerts').textContent || '未知'}

=== 建议 ===
1. 定期检查离线设备状态
2. 及时处理故障预警
3. 按计划进行固件升级
4. 保持系统监控
            `;

            // 创建下载链接
            const blob = new Blob([reportContent], { type: 'text/plain;charset=utf-8' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `系统分析报告_${new Date().toISOString().split('T')[0]}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            showToast('系统报告已生成并下载', 'success');
        }, 2000);
    }

    // 显示提示消息
    function showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 6px;
            color: white;
            font-size: 14px;
            z-index: 10000;
            max-width: 300px;
            word-wrap: break-word;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
        `;

        // 根据类型设置背景色
        switch(type) {
            case 'success':
                toast.style.background = 'linear-gradient(45deg, #27ae60, #2ecc71)';
                break;
            case 'error':
                toast.style.background = 'linear-gradient(45deg, #e74c3c, #c0392b)';
                break;
            case 'warning':
                toast.style.background = 'linear-gradient(45deg, #f39c12, #e67e22)';
                break;
            default:
                toast.style.background = 'linear-gradient(45deg, #3498db, #2980b9)';
        }

        toast.textContent = message;
        document.body.appendChild(toast);

        // 3秒后自动移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }
        }, 3000);

        return toast;
    }
</script>
{% endblock %}