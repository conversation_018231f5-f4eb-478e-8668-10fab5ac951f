{% extends "base.html" %}

{% block title %}设备智能分析 - {{ device.device_id }}{% endblock %}

{% block head %}
<style>
    .device-info-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .info-item {
        background: rgba(255,255,255,0.1);
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 15px;
        backdrop-filter: blur(10px);
    }

    .info-item h6 {
        color: rgba(255,255,255,0.8);
        font-size: 0.9rem;
        margin-bottom: 5px;
    }

    .info-item .value {
        font-size: 1.1rem;
        font-weight: 600;
    }

    .status-online {
        background: linear-gradient(45deg, #27ae60, #2ecc71);
        color: white;
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
    }

    .status-offline {
        background: linear-gradient(45deg, #e74c3c, #c0392b);
        color: white;
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
    }

    .analysis-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .analysis-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    }

    .analysis-card .card-header {
        background: linear-gradient(45deg, #3498db, #2980b9);
        color: white;
        border-radius: 15px 15px 0 0;
        border: none;
    }

    .loading-spinner {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 3px solid rgba(0,0,0,0.1);
        border-radius: 50%;
        border-top-color: #3498db;
        animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }

    .analysis-result {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-top: 15px;
        border-left: 4px solid #3498db;
    }

    .refresh-btn {
        background: linear-gradient(45deg, #3498db, #2980b9);
        border: none;
        border-radius: 20px;
        padding: 8px 16px;
        color: white;
        transition: all 0.3s ease;
    }

    .refresh-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(52,152,219,0.3);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col">
            <h2><i class="fas fa-brain text-primary"></i> 设备智能分析</h2>
            <p class="text-muted">基于AI技术的设备健康状况分析与预测</p>
        </div>
    </div>

    <!-- 设备基本信息卡片 -->
    <div class="card device-info-card mb-4">
        <div class="card-body">
            <h5 class="card-title mb-4">
                <i class="fas fa-microchip"></i> {{ device.device_remark or device.device_id }}
            </h5>
            <div class="row">
                <div class="col-md-3">
                    <div class="info-item">
                        <h6><i class="fas fa-fingerprint"></i> 设备ID</h6>
                        <div class="value">{{ device.device_id }}</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="info-item">
                        <h6><i class="fas fa-circle"></i> 状态</h6>
                        <div class="value">
                            {% if device_data.status == '在线' %}
                            <span class="status-online">在线</span>
                            {% else %}
                            <span class="status-offline">离线</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="info-item">
                        <h6><i class="fas fa-code-branch"></i> 固件版本</h6>
                        <div class="value">{{ device.firmware_version or '未知' }}</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="info-item">
                        <h6><i class="fas fa-map-marker-alt"></i> 位置</h6>
                        <div class="value">{{ device_data.location }}</div>
                    </div>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-6">
                    <div class="info-item">
                        <h6><i class="fas fa-clock"></i> 最后在线时间</h6>
                        <div class="value">{{ device_data.last_online_time }}</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="info-item">
                        <h6><i class="fas fa-key"></i> 产品密钥</h6>
                        <div class="value">{{ device_data.product_key }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 设备健康分析 -->
        <div class="col-lg-6">
            <div class="card analysis-card mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-heartbeat"></i> 设备健康分析
                    </h6>
                    <button class="refresh-btn btn-sm" onclick="loadHealthAnalysis()">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                </div>
                <div class="card-body">
                    <div id="healthAnalysis">
                        <div class="text-center py-4">
                            <div class="loading-spinner"></div>
                            <p class="mt-2 text-muted">正在分析设备健康状况...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 故障预测 -->
        <div class="col-lg-6">
            <div class="card analysis-card mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-exclamation-triangle"></i> 故障预测
                    </h6>
                    <button class="refresh-btn btn-sm" onclick="loadFailurePrediction()">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                </div>
                <div class="card-body">
                    <div id="failurePrediction">
                        <div class="text-center py-4">
                            <div class="loading-spinner"></div>
                            <p class="mt-2 text-muted">正在预测潜在故障...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 固件升级建议 -->
        <div class="col-lg-8">
            <div class="card analysis-card mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-upload"></i> 固件升级建议
                    </h6>
                    <button class="refresh-btn btn-sm" onclick="loadUpgradeRecommendation()">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                </div>
                <div class="card-body">
                    <div id="upgradeRecommendation">
                        <div class="text-center py-4">
                            <div class="loading-spinner"></div>
                            <p class="mt-2 text-muted">正在分析升级建议...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="col-lg-4">
            <div class="card analysis-card mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-tools"></i> 快速操作
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary" onclick="refreshAllAnalysis()">
                            <i class="fas fa-sync-alt"></i> 刷新所有分析
                        </button>
                        <button class="btn btn-info" onclick="exportAnalysisReport()">
                            <i class="fas fa-download"></i> 导出分析报告
                        </button>
                        <button class="btn btn-success" onclick="scheduleOTA()">
                            <i class="fas fa-rocket"></i> 计划OTA升级
                        </button>
                        <a href="{{ url_for('device.devices') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> 返回设备列表
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 历史任务记录 -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">历史任务记录</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="historyTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>时间</th>
                            <th>状态</th>
                            <th>事件</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for task in task_history %}
                        <tr>
                            <td>{{ task.timestamp }}</td>
                            <td>{{ task.status }}</td>
                            <td>{{ task.event }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 显示加载状态
    function showLoading(elementId, message) {
        $(`#${elementId}`).html(`
            <div class="text-center py-4">
                <div class="loading-spinner"></div>
                <p class="mt-2 text-muted">${message}</p>
            </div>
        `);
    }

    // 显示分析结果
    function showAnalysisResult(elementId, content) {
        $(`#${elementId}`).html(`
            <div class="analysis-result">
                <div class="text-wrap">${content}</div>
            </div>
        `);
    }

    // 显示错误信息
    function showError(elementId, message) {
        $(`#${elementId}`).html(`
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> ${message}
            </div>
        `);
    }

    // 加载设备健康分析
    function loadHealthAnalysis() {
        showLoading('healthAnalysis', '正在分析设备健康状况...');

        $.ajax({
            url: "{{ url_for('ai.analyze_device') }}",
            type: "POST",
            contentType: "application/json",
            data: JSON.stringify({
                device_id: "{{ device.id }}"
            }),
            success: function(response) {
                if (response.success) {
                    showAnalysisResult('healthAnalysis', response.analysis);
                } else {
                    showError('healthAnalysis', response.message);
                }
            },
            error: function(xhr, status, error) {
                showError('healthAnalysis', `加载失败: ${error}`);
            }
        });
    }

    // 加载故障预测
    function loadFailurePrediction() {
        showLoading('failurePrediction', '正在预测潜在故障...');

        $.ajax({
            url: "{{ url_for('ai.predict_failure') }}",
            type: "POST",
            contentType: "application/json",
            data: JSON.stringify({
                device_id: "{{ device.id }}"
            }),
            success: function(response) {
                if (response.success) {
                    showAnalysisResult('failurePrediction', response.prediction);
                } else {
                    showError('failurePrediction', response.message);
                }
            },
            error: function(xhr, status, error) {
                showError('failurePrediction', `加载失败: ${error}`);
            }
        });
    }

    // 加载升级建议
    function loadUpgradeRecommendation() {
        showLoading('upgradeRecommendation', '正在分析升级建议...');

        $.ajax({
            url: "{{ url_for('ai.recommend_upgrade') }}",
            type: "POST",
            contentType: "application/json",
            data: JSON.stringify({
                device_id: "{{ device.id }}"
            }),
            success: function(response) {
                if (response.success) {
                    showAnalysisResult('upgradeRecommendation', response.recommendation);
                } else {
                    showError('upgradeRecommendation', response.message);
                }
            },
            error: function(xhr, status, error) {
                showError('upgradeRecommendation', `加载失败: ${error}`);
            }
        });
    }

    // 刷新所有分析
    function refreshAllAnalysis() {
        loadHealthAnalysis();
        loadFailurePrediction();
        loadUpgradeRecommendation();

        // 显示提示
        showToast('正在刷新所有分析...', 'info');
    }

    // 导出分析报告
    function exportAnalysisReport() {
        // 获取所有分析结果
        const healthAnalysis = $('#healthAnalysis .analysis-result .text-wrap').text() || '暂无数据';
        const failurePrediction = $('#failurePrediction .analysis-result .text-wrap').text() || '暂无数据';
        const upgradeRecommendation = $('#upgradeRecommendation .analysis-result .text-wrap').text() || '暂无数据';

        // 构建报告内容
        const reportContent = `设备智能分析报告

设备ID: {{ device.device_id }}
生成时间: ${new Date().toLocaleString()}

=== 设备健康分析 ===
${healthAnalysis}

=== 故障预测 ===
${failurePrediction}

=== 固件升级建议 ===
${upgradeRecommendation}
        `;

        // 创建下载链接
        const blob = new Blob([reportContent], { type: 'text/plain;charset=utf-8' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `设备分析报告_{{ device.device_id }}_${new Date().toISOString().split('T')[0]}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);

        showToast('分析报告已导出', 'success');
    }

    // 计划OTA升级
    function scheduleOTA() {
        if (confirm('确定要为此设备计划OTA升级吗？')) {
            window.location.href = `/devices?device_id={{ device.device_id }}`;
        }
    }

    // 显示提示消息
    function showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 6px;
            color: white;
            font-size: 14px;
            z-index: 10000;
            max-width: 300px;
            word-wrap: break-word;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
        `;

        // 根据类型设置背景色
        switch(type) {
            case 'success':
                toast.style.background = 'linear-gradient(45deg, #27ae60, #2ecc71)';
                break;
            case 'error':
                toast.style.background = 'linear-gradient(45deg, #e74c3c, #c0392b)';
                break;
            case 'warning':
                toast.style.background = 'linear-gradient(45deg, #f39c12, #e67e22)';
                break;
            default:
                toast.style.background = 'linear-gradient(45deg, #3498db, #2980b9)';
        }

        toast.textContent = message;
        document.body.appendChild(toast);

        // 3秒后自动移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }
        }, 3000);

        return toast;
    }

    $(document).ready(function() {
        // 初始化DataTables
        $('#historyTable').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.21/i18n/Chinese.json"
            },
            "order": [[0, "desc"]],
            "pageLength": 10,
            "responsive": true
        });

        // 延迟加载AI分析数据，避免同时请求过多
        setTimeout(() => {
            loadHealthAnalysis();
        }, 500);

        setTimeout(() => {
            loadFailurePrediction();
        }, 1000);

        setTimeout(() => {
            loadUpgradeRecommendation();
        }, 1500);

        // 显示欢迎提示
        showToast('AI分析系统已启动，正在加载分析数据...', 'info');
    });
</script>
{% endblock %} 