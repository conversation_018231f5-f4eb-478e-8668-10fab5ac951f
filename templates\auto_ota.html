{% extends "base.html" %}

{% block title %}自动OTA升级管理{% endblock %}

{% block styles %}
<style>
    /* 自动OTA页面美化样式 - 参考设备管理页面设计 */

    /* ===== 页面背景和基础样式 ===== */
    .container-fluid {
        padding-bottom: 2rem;
    }

    /* ===== 页面标题区域 ===== */
    .page-header {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        padding: 2rem 0;
        margin: -1.5rem -15px 2rem -15px;
        border-radius: 0 0 1rem 1rem;
        box-shadow: 0 4px 20px rgba(79, 172, 254, 0.3);
    }

    .page-header h3 {
        font-weight: 700;
        text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        margin-bottom: 0.5rem;
    }

    .page-header .subtitle {
        opacity: 0.9;
        font-size: 1.1rem;
        font-weight: 300;
    }

    /* ===== 状态卡片样式 ===== */
    .status-card {
        border: none;
        border-radius: 1rem;
        overflow: hidden;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        height: 100%;
        background: white;
    }

    .status-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .status-card-service {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    }

    .status-card-auto {
        background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;
    }

    .status-card-timeout {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
    }

    .status-card-retry {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
    }

    .status-icon {
        font-size: 2.5rem;
        opacity: 0.8;
        margin-bottom: 0.5rem;
    }

    .status-value {
        font-size: 1.2rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
    }

    .status-label {
        font-size: 0.9rem;
        opacity: 0.9;
        font-weight: 500;
    }

    /* ===== 配置卡片样式 ===== */
    .config-card {
        border: none;
        border-radius: 1rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        background: white;
        overflow: hidden;
    }

    .config-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .config-header {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        padding: 1rem 1.5rem;
        border-bottom: none;
        font-weight: 600;
        font-size: 1.1rem;
    }

    .config-header i {
        margin-right: 0.5rem;
        opacity: 0.9;
    }

    .config-body {
        padding: 2rem;
        background: white;
    }

    /* ===== 表单开关美化 ===== */
    .form-switch .form-check-input {
        width: 3.5em;
        height: 1.8em;
        margin-right: 0.75rem;
        transform: scale(1.1);
        margin-top: 0.1em;
        border: 2px solid #dee2e6;
        transition: all 0.3s ease;
    }

    .form-switch .form-check-input:checked {
        background-color: #4facfe;
        border-color: #4facfe;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
    }

    .form-switch .form-check-input:focus {
        box-shadow: 0 0 0 0.25rem rgba(79, 172, 254, 0.25);
        border-color: #4facfe;
    }

    .form-switch .form-check-label {
        font-weight: 600;
        color: #495057;
        font-size: 1rem;
        margin-left: 0.5rem;
    }

    /* ===== 表单元素美化 ===== */
    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
        font-size: 1rem;
    }

    .form-control {
        border: 2px solid #e9ecef;
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
        font-size: 0.95rem;
    }

    .form-control:focus {
        border-color: #4facfe;
        box-shadow: 0 0 0 0.2rem rgba(79, 172, 254, 0.25);
        transform: translateY(-1px);
    }

    .form-text {
        color: #6c757d;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    /* ===== 按钮样式 ===== */
    .btn {
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        border: none;
    }

    .btn-outline-primary {
        border: 2px solid #4facfe;
        color: #4facfe;
        background: white;
    }

    .btn-outline-primary:hover {
        background: #4facfe;
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
        color: white;
    }

    /* ===== 状态徽章样式 ===== */
    .badge {
        padding: 0.5rem 1rem;
        border-radius: 1rem;
        font-size: 0.875rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .badge.bg-success {
        background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;
        box-shadow: 0 2px 8px rgba(86, 171, 47, 0.3);
    }

    .badge.bg-danger {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
        box-shadow: 0 2px 8px rgba(240, 147, 251, 0.3);
    }

    .badge.bg-warning {
        background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%) !important;
        color: #2d3436 !important;
        box-shadow: 0 2px 8px rgba(255, 234, 167, 0.3);
    }

    .badge.bg-secondary {
        background: linear-gradient(135deg, #636e72 0%, #2d3436 100%) !important;
        box-shadow: 0 2px 8px rgba(99, 110, 114, 0.3);
    }

    /* ===== 配置区域布局 ===== */
    .config-section {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        border: 1px solid #dee2e6;
    }

    .config-section-title {
        font-weight: 700;
        color: #495057;
        margin-bottom: 1rem;
        font-size: 1.1rem;
        display: flex;
        align-items: center;
    }

    .config-section-title i {
        margin-right: 0.5rem;
        color: #4facfe;
    }

    /* ===== 暗黑模式适配 ===== */
    body.dark-mode .container-fluid {
        background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
    }

    body.dark-mode .status-card,
    body.dark-mode .config-card {
        background-color: #2d3748;
        border: 1px solid #4a5568;
    }

    body.dark-mode .config-body {
        background-color: #2d3748;
    }

    body.dark-mode .config-section {
        background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
        border: 1px solid #4a5568;
    }

    body.dark-mode .form-control {
        background-color: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }

    body.dark-mode .form-control:focus {
        border-color: #63b3ed;
        box-shadow: 0 0 0 0.2rem rgba(99, 179, 237, 0.25);
    }

    body.dark-mode .form-label,
    body.dark-mode .form-switch .form-check-label,
    body.dark-mode .config-section-title {
        color: #e2e8f0;
    }

    body.dark-mode .form-text {
        color: #a0aec0;
    }

    body.dark-mode .form-switch .form-check-input:checked {
        background-color: #63b3ed;
        border-color: #63b3ed;
    }

    body.dark-mode .form-switch .form-check-input:focus {
        box-shadow: 0 0 0 0.25rem rgba(99, 179, 237, 0.25);
        border-color: #63b3ed;
    }

    /* ===== 模态框样式 ===== */
    .modal-header-auto {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
    }

    .btn-primary-auto {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        border: none;
        color: white;
    }

    .btn-primary-auto:hover {
        background: linear-gradient(135deg, #3d8bfe 0%, #00d4fe 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- 页面标题区域 -->
    <div class="page-header">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center flex-wrap gap-3">
                <div>
                    <h3 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>自动OTA升级管理
                    </h3>
                    <p class="subtitle mb-0">智能化设备固件升级管理，支持自动检测和批量升级</p>
                </div>
                <a href="{{ url_for('firmware.latest_firmware_management') }}" class="btn btn-outline-primary">
                    <i class="fas fa-cog me-2"></i>最新固件配置
                </a>
            </div>
        </div>
    </div>

    <!-- 服务状态卡片 -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card status-card status-card-service">
                <div class="card-body text-center">
                    <div class="status-icon">
                        <i class="fas fa-server"></i>
                    </div>
                    <div class="status-value">
                        <span id="service-status" class="badge bg-secondary">检查中...</span>
                    </div>
                    <div class="status-label">服务状态</div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card status-card status-card-auto">
                <div class="card-body text-center">
                    <div class="status-icon">
                        <i class="fas fa-toggle-on"></i>
                    </div>
                    <div class="status-value">
                        <span id="auto-upgrade-status" class="badge bg-secondary">检查中...</span>
                    </div>
                    <div class="status-label">自动升级</div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card status-card status-card-timeout">
                <div class="card-body text-center">
                    <div class="status-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="status-value">
                        <span id="current-timeout" class="fw-bold">-</span> 秒
                    </div>
                    <div class="status-label">超时时间</div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card status-card status-card-retry">
                <div class="card-body text-center">
                    <div class="status-icon">
                        <i class="fas fa-redo"></i>
                    </div>
                    <div class="status-value">
                        <span id="current-retries" class="fw-bold">-</span> 次
                    </div>
                    <div class="status-label">最大重试</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 配置设置 -->
    <div class="card config-card">
        <div class="config-header">
            <i class="fas fa-sliders-h"></i>配置设置
        </div>
        <div class="config-body">
            <div class="row g-4">
                <!-- 基础开关配置 -->
                <div class="col-md-6">
                    <div class="config-section">
                        <div class="config-section-title">
                            <i class="fas fa-toggle-on"></i>功能开关
                        </div>
                        <div class="row g-4">
                            <div class="col-12">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="enabled" name="enabled">
                                    <label class="form-check-label" for="enabled">
                                        <i class="fas fa-power-off me-2"></i>启用自动OTA升级
                                    </label>
                                </div>
                                <div class="form-text">开启后系统将自动检测并升级设备固件</div>
                            </div>
                            <div class="col-12">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="force_update" name="force_update">
                                    <label class="form-check-label" for="force_update">
                                        <i class="fas fa-exclamation-triangle me-2"></i>强制更新模式
                                    </label>
                                </div>
                                <div class="form-text">忽略版本检查，强制执行固件升级</div>
                            </div>
                            <div class="col-12">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="compare_version" name="compare_version">
                                    <label class="form-check-label" for="compare_version">
                                        <i class="fas fa-code-branch me-2"></i>比较版本号
                                    </label>
                                </div>
                                <div class="form-text">升级前比较固件版本号，避免重复升级</div>
                            </div>
                            <div class="col-12">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="simulation_mode" name="simulation_mode">
                                    <label class="form-check-label" for="simulation_mode">
                                        <i class="fas fa-eye me-2"></i>模拟观察模式
                                    </label>
                                </div>
                                <div class="form-text">仅模拟升级过程，不实际执行升级操作</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数值配置 -->
                <div class="col-md-6">
                    <div class="config-section">
                        <div class="config-section-title">
                            <i class="fas fa-cogs"></i>参数配置
                        </div>
                        <div class="row g-4">
                            <div class="col-12">
                                <label for="timeout" class="form-label">
                                    <i class="fas fa-clock me-2"></i>超时时间（秒）
                                </label>
                                <input type="number" class="form-control" id="timeout" name="timeout"
                                       min="60" max="3600" placeholder="300">
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>单次升级操作的最大等待时间，范围：60-3600秒
                                </div>
                            </div>
                            <div class="col-12">
                                <label for="max_retries" class="form-label">
                                    <i class="fas fa-redo me-2"></i>最大重试次数
                                </label>
                                <input type="number" class="form-control" id="max_retries" name="max_retries"
                                       min="0" max="10" placeholder="3">
                                <div class="form-text">
                                    <i class="fas fa-info-circle me-1"></i>升级失败时的最大重试次数，范围：0-10次
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 确认模态框 -->
<div class="modal fade" id="confirmModal" tabindex="-1" aria-labelledby="confirmModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header modal-header-auto">
                <h5 class="modal-title" id="confirmModalLabel">
                    <i class="fas fa-check-circle me-2"></i>确认配置更改
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="关闭"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3">
                    <i class="fas fa-cogs fa-3x text-primary mb-2"></i>
                    <p class="h6">您确定要保存以下配置更改吗？</p>
                </div>

                <div class="card border-info">
                    <div class="card-header bg-info bg-opacity-10">
                        <h6 class="mb-0 text-info">
                            <i class="fas fa-list me-2"></i>配置摘要
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="config-summary"></div>
                    </div>
                </div>

                <div class="alert alert-info border-info mt-3">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>提示：</strong>配置更改将立即生效，请确认无误后保存。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>取消
                </button>
                <button type="button" class="btn btn-primary btn-primary-auto" id="confirm-save">
                    <i class="fas fa-save me-1"></i>确认保存
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    let pendingConfig = null;
    let originalValues = {};

    // 页面加载时获取状态和配置
    loadStatus();
    loadConfig();

    // 开关变化时立即显示确认模态框
    $('#enabled, #force_update, #compare_version, #simulation_mode').change(function() {
        const $this = $(this);
        // 保存当前状态作为待确认状态
        showConfigConfirmModal();
    });

    // 输入框变化时显示确认模态框
    $('#timeout, #max_retries').on('blur', function() {
        if ($(this).val() && $(this).val() !== $(this).data('original-value')) {
            showConfigConfirmModal();
        }
    });

    // 确认保存按钮
    $('#confirm-save').click(function() {
        if (pendingConfig) {
            saveConfig(pendingConfig);
            $('#confirmModal').modal('hide');
        }
    });

    // 取消按钮 - 恢复原状态
    $('#confirmModal .btn-secondary, #confirmModal .btn-close').click(function() {
        restoreOriginalValues();
    });

    // 模态框关闭时恢复原状态（如果没有保存）
    $('#confirmModal').on('hidden.bs.modal', function() {
        if (pendingConfig) {
            restoreOriginalValues();
            pendingConfig = null;
        }
    });

    function loadStatus() {
        $.get('/api/auto_ota/status')
            .done(function(response) {
                if (response.success) {
                    const status = response.status;
                    
                    // 更新服务状态
                    const serviceStatus = status.initialized ? '运行中' : '未初始化';
                    const serviceClass = status.initialized ? 'bg-success' : 'bg-danger';
                    $('#service-status').removeClass().addClass('badge ' + serviceClass).text(serviceStatus);

                    // 更新自动升级状态
                    const autoStatus = status.config.enabled ? '已启用' : '已禁用';
                    const autoClass = status.config.enabled ? 'bg-success' : 'bg-warning';
                    $('#auto-upgrade-status').removeClass().addClass('badge ' + autoClass).text(autoStatus);
                    
                    // 更新当前配置显示
                    $('#current-timeout').text(status.config.timeout);
                    $('#current-retries').text(status.config.max_retries);
                }
            })
            .fail(function() {
                $('#service-status').removeClass().addClass('badge bg-danger').text('获取失败');
                $('#auto-upgrade-status').removeClass().addClass('badge bg-danger').text('获取失败');
            });
    }

    function loadConfig() {
        $.get('/api/auto_ota/config')
            .done(function(response) {
                if (response.success) {
                    const config = response.config;
                    $('#enabled').prop('checked', config.enabled);
                    $('#force_update').prop('checked', config.force_update);
                    $('#compare_version').prop('checked', config.compare_version);
                    $('#simulation_mode').prop('checked', config.simulation_mode);
                    $('#timeout').val(config.timeout).data('original-value', config.timeout);
                    $('#max_retries').val(config.max_retries).data('original-value', config.max_retries);

                    // 保存原始值
                    originalValues = {
                        enabled: config.enabled,
                        force_update: config.force_update,
                        compare_version: config.compare_version,
                        simulation_mode: config.simulation_mode,
                        timeout: config.timeout,
                        max_retries: config.max_retries
                    };
                }
            })
            .fail(function() {
                showAlert('获取配置失败', 'danger');
            });
    }

    function restoreOriginalValues() {
        $('#enabled').prop('checked', originalValues.enabled);
        $('#force_update').prop('checked', originalValues.force_update);
        $('#compare_version').prop('checked', originalValues.compare_version);
        $('#simulation_mode').prop('checked', originalValues.simulation_mode);
        $('#timeout').val(originalValues.timeout);
        $('#max_retries').val(originalValues.max_retries);
    }

    function showConfigConfirmModal() {
        pendingConfig = {
            enabled: $('#enabled').is(':checked'),
            force_update: $('#force_update').is(':checked'),
            compare_version: $('#compare_version').is(':checked'),
            simulation_mode: $('#simulation_mode').is(':checked'),
            timeout: parseInt($('#timeout').val()) || 300,
            max_retries: parseInt($('#max_retries').val()) || 3
        };

        // 验证输入
        if (pendingConfig.timeout < 60 || pendingConfig.timeout > 3600) {
            showAlert('超时时间必须在60-3600秒之间', 'warning');
            return;
        }

        if (pendingConfig.max_retries < 0 || pendingConfig.max_retries > 10) {
            showAlert('最大重试次数必须在0-10次之间', 'warning');
            return;
        }

        // 生成配置摘要
        const summary = `
            <ul class="list-unstyled">
                <li><strong>自动升级：</strong>${pendingConfig.enabled ? '启用' : '禁用'}</li>
                <li><strong>强制更新：</strong>${pendingConfig.force_update ? '启用' : '禁用'}</li>
                <li><strong>比较版本号：</strong>${pendingConfig.compare_version ? '启用' : '禁用'}</li>
                <li><strong>模拟观察模式：</strong>${pendingConfig.simulation_mode ? '启用' : '禁用'}</li>
                <li><strong>超时时间：</strong>${pendingConfig.timeout} 秒</li>
                <li><strong>最大重试：</strong>${pendingConfig.max_retries} 次</li>
            </ul>
        `;
        $('#config-summary').html(summary);
        $('#confirmModal').modal('show');
    }

    function saveConfig(config) {
        $.ajax({
            url: '/api/auto_ota/config',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(config)
        })
        .done(function(response) {
            if (response.success) {
                showAlert('配置保存成功', 'success');
                loadStatus(); // 刷新状态显示
                loadConfig(); // 刷新配置显示
                pendingConfig = null; // 清除待确认配置
            } else {
                showAlert('配置保存失败: ' + response.message, 'danger');
                restoreOriginalValues(); // 保存失败时恢复原值
            }
        })
        .fail(function() {
            showAlert('配置保存失败', 'danger');
            restoreOriginalValues(); // 保存失败时恢复原值
        });
    }


});
</script>
{% endblock %}
