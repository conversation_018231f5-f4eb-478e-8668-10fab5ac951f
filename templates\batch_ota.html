{% extends "base.html" %}

{% block title %}批量OTA升级{% endblock %}

{% block styles %}
<style>
    /* 批量OTA页面美化样式 - 参考设备管理页面设计 */

    /* ===== 页面背景和基础样式 ===== */
    .container-fluid {
        padding-bottom: 2rem;
    }

    .status-card-service {
        color: #667eea;
    }

    /* ===== 页面标题区域 ===== */
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem 0;
        margin: -1.5rem -15px 2rem -15px;
        border-radius: 0 0 1rem 1rem;
        box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
    }

    .page-header h2 {
        font-weight: 700;
        text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        margin-bottom: 0.5rem;
    }

    .page-header .subtitle {
        opacity: 0.9;
        font-size: 1.1rem;
        font-weight: 300;
    }

    /* ===== 统计卡片样式 ===== */
    .stats-card {
        border: none;
        border-radius: 1rem;
        overflow: hidden;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        height: 100%;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .stats-card-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    }

    .stats-card-success {
        background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;
    }

    .stats-card-warning {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
    }

    .stats-card-info {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
    }

    .stats-icon {
        font-size: 2.5rem;
        opacity: 0.8;
        margin-bottom: 0.5rem;
    }

    .stats-number {
        font-size: 2.2rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
    }

    .stats-label {
        font-size: 0.9rem;
        opacity: 0.9;
        font-weight: 500;
    }

    /* ===== 筛选卡片样式 ===== */
    .filter-card {
        border: none;
        border-radius: 1rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        background: white;
        overflow: hidden;
    }

    .filter-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .filter-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1rem 1.5rem;
        border-bottom: none;
        font-weight: 600;
        font-size: 1.1rem;
    }

    .filter-header i {
        margin-right: 0.5rem;
        opacity: 0.9;
    }

    .filter-body {
        padding: 1.5rem;
        background: white;
    }

    /* ===== 表单元素美化 ===== */
    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
    }

    .form-control, .form-select {
        border: 2px solid #e9ecef;
        border-radius: 0.5rem;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
        font-size: 0.95rem;
    }

    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        transform: translateY(-1px);
    }

    .form-check-input {
        border: 2px solid #dee2e6;
        border-radius: 0.25rem;
        transition: all 0.3s ease;
    }

    .form-check-input:checked {
        background-color: #667eea;
        border-color: #667eea;
    }

    .form-check-input:focus {
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .form-check-label {
        font-weight: 500;
        color: #495057;
    }

    /* ===== 按钮样式优化 ===== */
    .btn {
        border-radius: 0.5rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        transition: all 0.3s ease;
        border: none;
    }

    .btn-filter {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .btn-filter:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        color: white;
    }

    .btn-outline-secondary {
        border: 2px solid #6c757d;
        color: #6c757d;
        background: white;
    }

    .btn-outline-secondary:hover {
        background: #6c757d;
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
    }

    .btn-outline-info {
        border: 2px solid #0dcaf0;
        color: #0dcaf0;
        background: white;
    }

    .btn-outline-info:hover {
        background: #0dcaf0;
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(13, 202, 240, 0.3);
    }

    .btn-success {
        background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        box-shadow: 0 4px 15px rgba(86, 171, 47, 0.3);
    }

    .btn-success:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(86, 171, 47, 0.4);
    }

    .selected-count {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 1rem;
        font-size: 0.875rem;
        margin-left: 0.5rem;
        font-weight: 700;
        box-shadow: 0 2px 8px rgba(240, 147, 251, 0.3);
    }

    /* ===== 设备列表卡片 ===== */
    .device-list-card {
        border: none;
        border-radius: 1rem;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        overflow: hidden;
        background: white;
    }

    .device-list-header {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        padding: 1rem 1.5rem;
        border-bottom: none;
    }

    .device-list-header h5 {
        margin: 0;
        font-weight: 600;
        font-size: 1.1rem;
    }

    .device-list-header i {
        margin-right: 0.5rem;
        opacity: 0.9;
    }

    /* ===== 设备表格样式 ===== */
    .table-container {
        max-height: 500px;
        overflow-y: auto;
        border-radius: 0;
        border: none;
    }

    .device-table {
        margin-bottom: 0;
        font-size: 0.9rem;
        background: white;
    }

    .device-table th {
        font-weight: 600;
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        color: #495057;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-bottom: 2px solid #dee2e6;
        position: sticky;
        top: 0;
        z-index: 10;
        padding: 1rem 0.75rem;
    }

    .device-table th i {
        margin-right: 0.5rem;
        color: #667eea;
    }

    .device-table td {
        vertical-align: middle;
        padding: 0.75rem;
        border-bottom: 1px solid #f0f0f0;
        transition: all 0.2s ease;
    }

    .device-table tbody tr {
        transition: all 0.3s ease;
    }

    .device-table tbody tr:hover {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    /* ===== 状态徽章样式 ===== */
    .status-badge {
        padding: 0.4rem 0.8rem;
        border-radius: 1rem;
        font-size: 0.75rem;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border: none;
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    }

    .status-online {
        background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        color: white;
    }

    .status-offline {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
    }

    /* ===== 产品密钥和设备ID样式 ===== */
    .product-key-cell {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 0.8rem;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 0.5rem 0.75rem;
        border-radius: 0.5rem;
        border: 1px solid #dee2e6;
        color: #495057;
        font-weight: 500;
    }

    .device-id-cell {
        font-weight: 700;
        color: #667eea;
        font-size: 0.95rem;
    }

    .device-remark-cell {
        color: #6c757d;
        font-style: italic;
    }

    /* ===== 复选框美化 ===== */
    .device-checkbox {
        transform: scale(1.2);
        border: 2px solid #dee2e6;
        border-radius: 0.25rem;
    }

    .device-checkbox:checked {
        background-color: #667eea;
        border-color: #667eea;
    }

    /* ===== 暗黑模式适配 ===== */
    body.dark-mode .container-fluid {
        background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
    }

    body.dark-mode .filter-card,
    body.dark-mode .device-list-card {
        background-color: #2d3748;
        border: 1px solid #4a5568;
    }

    body.dark-mode .filter-body {
        background-color: #2d3748;
    }

    body.dark-mode .device-table {
        background-color: #2d3748;
        color: #e2e8f0;
    }

    body.dark-mode .device-table th {
        background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
        color: #a0aec0;
        border-bottom: 2px solid #4a5568;
    }

    body.dark-mode .device-table td {
        border-bottom: 1px solid #4a5568;
    }

    body.dark-mode .device-table tbody tr:hover {
        background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
        box-shadow: 0 4px 15px rgba(0,0,0,0.3);
    }

    body.dark-mode .product-key-cell {
        background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
        border: 1px solid #4a5568;
        color: #e2e8f0;
    }

    body.dark-mode .device-id-cell {
        color: #63b3ed;
    }

    body.dark-mode .form-control,
    body.dark-mode .form-select {
        background-color: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }

    body.dark-mode .form-control:focus,
    body.dark-mode .form-select:focus {
        border-color: #63b3ed;
        box-shadow: 0 0 0 0.2rem rgba(99, 179, 237, 0.25);
    }

    body.dark-mode .form-label {
        color: #e2e8f0;
    }

    body.dark-mode .form-check-label {
        color: #e2e8f0;
    }

    /* ===== 模态框样式 ===== */
    .modal-header-gradient {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .btn-primary-gradient {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
    }

    .btn-primary-gradient:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- 页面标题区域 -->
    <div class="page-header">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center flex-wrap gap-3">
                <div>
                    <h2 class="mb-0">
                        <i class="fas fa-rocket me-2"></i>批量OTA升级
                    </h2>
                    <p class="subtitle mb-0">高级设备筛选器，支持多维度筛选并批量执行OTA升级任务</p>
                </div>
                <div class="d-flex gap-2 flex-wrap">
                    <button type="button" class="btn btn-outline-secondary" onclick="resetFilters()">
                        <i class="fas fa-undo me-1"></i>重置筛选
                    </button>
                    <button type="button" class="btn btn-outline-info" onclick="refreshDeviceList()">
                        <i class="fas fa-sync-alt me-1"></i>刷新设备
                    </button>
                    <button type="button" class="btn btn-success" id="startBatchOtaBtn" disabled>
                        <i class="fas fa-play me-1"></i>启动批量升级
                        <span class="selected-count" id="selectedCount">0</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row mb-4" id="statsCards">
        <div class="col-md-3 mb-3">
            <div class="card stats-card stats-card-primary">
                <div class="card-body text-center">
                    <div class="stats-icon">
                        <i class="fas fa-server"></i>
                    </div>
                    <div class="stats-number">{{ stats.total_devices }}</div>
                    <div class="stats-label">设备总数</div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card stats-card-info">
                <div class="card-body text-center">
                    <div class="stats-icon">
                        <i class="fas fa-layer-group"></i>
                    </div>
                    <div class="stats-number">{{ stats.device_types|length }}</div>
                    <div class="stats-label">设备类型</div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card stats-card-warning">
                <div class="card-body text-center">
                    <div class="stats-icon">
                        <i class="fas fa-key"></i>
                    </div>
                    <div class="stats-number">{{ stats.product_keys|length }}</div>
                    <div class="stats-label">产品密钥</div>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card stats-card-success">
                <div class="card-body text-center">
                    <div class="stats-icon">
                        <i class="fas fa-filter"></i>
                    </div>
                    <div class="stats-number" id="filteredCount">{{ stats.total_devices }}</div>
                    <div class="stats-label">筛选结果</div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 筛选条件 -->
        <div class="col-md-4 mb-4 w-100">
            <div class="filter-card">
                <div class="filter-header">
                    <i class="fas fa-filter"></i>筛选条件
                </div>
                <div class="filter-body">
                    <!-- 设备类型筛选 -->
                    <div class="mb-4">
                        <label class="form-label">
                            <i class="fas fa-tag me-2"></i>设备类型
                        </label>
                        <div class="form-check-group">
                            {% for type_name, count in stats.device_types.items() %}
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" name="device_types" value="{{ type_name }}" id="type_{{ loop.index }}">
                                <label class="form-check-label" for="type_{{ loop.index }}">
                                    {{ type_name }} <span class="badge bg-secondary ms-2">{{ count }}</span>
                                </label>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- 产品密钥筛选 -->
                    <div class="mb-4">
                        <label class="form-label" for="product_keys">
                            <i class="fas fa-key me-2"></i>产品密钥
                        </label>
                        <select class="form-select" name="product_keys" id="product_keys" multiple size="4" title="选择产品密钥">
                            {% for key in stats.product_keys %}
                            <option value="{{ key }}">{{ key }}</option>
                            {% endfor %}
                        </select>
                        <small class="form-text text-muted">按住Ctrl可多选</small>
                    </div>

                    <!-- 固件版本筛选 -->
                    <div class="mb-4">
                        <label class="form-label" for="firmwareVersionSelect">
                            <i class="fas fa-code-branch me-2"></i>固件版本
                        </label>
                        <select class="form-select" name="firmware_versions" multiple size="3" id="firmwareVersionSelect" title="选择固件版本">
                            <option value="">加载中...</option>
                        </select>
                        <small class="form-text text-muted">按住Ctrl可多选</small>
                    </div>

                    <!-- 关键字筛选 -->
                    <div class="mb-4">
                        <label class="form-label">
                            <i class="fas fa-search me-2"></i>包含关键字
                        </label>
                        <input type="text" class="form-control" name="keywords" placeholder="设备ID或备注，多个用逗号分隔">
                        <small class="form-text text-muted">匹配设备ID或设备备注</small>
                    </div>

                    <!-- 排除关键字 -->
                    <div class="mb-4">
                        <label class="form-label">
                            <i class="fas fa-times-circle me-2"></i>排除关键字
                        </label>
                        <input type="text" class="form-control" name="exclude_keywords" placeholder="设备ID或备注，多个用逗号分隔">
                        <small class="form-text text-muted">排除包含这些关键字的设备</small>
                    </div>

                    <!-- 筛选按钮 -->
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-filter" id="filterBtn">
                            <i class="fas fa-search me-2"></i>应用筛选
                        </button>
                        <button type="button" class="btn btn-outline-secondary" id="resetBtn">
                            <i class="fas fa-undo me-2"></i>重置筛选
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设备列表 -->
        <div class="col-md-8 mb-4 w-100">
            <div class="card device-list-card">
                <div class="device-list-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-list"></i>设备列表
                    </h5>
                    <div>
                        <button type="button" class="btn btn-sm btn-light" id="selectAllBtn">
                            <i class="fas fa-check-square me-1"></i>全选
                        </button>
                        <button type="button" class="btn btn-sm btn-light" id="selectNoneBtn">
                            <i class="fas fa-square me-1"></i>取消全选
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive table-container">
                        <table class="table device-table mb-0">
                            <thead>
                                <tr>
                                    <th width="50" class="text-center">
                                        <input type="checkbox" class="device-checkbox" id="selectAllCheckbox" title="全选/取消全选">
                                    </th>
                                    <th><i class="fas fa-microchip"></i>设备ID</th>
                                    <th><i class="fas fa-comment"></i>设备备注</th>
                                    <th><i class="fas fa-tag"></i>设备类型</th>
                                    <th><i class="fas fa-key"></i>产品密钥</th>
                                    <th><i class="fas fa-code-branch"></i>固件版本</th>
                                    <th><i class="fas fa-clock"></i>上次升级</th>
                                    <th><i class="fas fa-signal"></i>设备状态</th>
                                </tr>
                            </thead>
                            <tbody id="deviceTableBody">
                                <tr>
                                    <td colspan="8" class="text-center text-muted py-5">
                                        <div class="d-flex flex-column align-items-center">
                                            <i class="fas fa-info-circle fa-2x mb-2 text-info"></i>
                                            <span>请使用左侧筛选条件来查找设备</span>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 批量升级确认模态框 -->
<div class="modal fade" id="batchOtaModal" tabindex="-1" aria-labelledby="batchOtaModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header modal-header-gradient">
                <h5 class="modal-title" id="batchOtaModalLabel">
                    <i class="fas fa-rocket me-2"></i>确认批量OTA升级
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="关闭"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3">
                    <i class="fas fa-upload fa-3x text-primary mb-2"></i>
                    <p class="h6">您即将对 <strong class="text-primary" id="confirmDeviceCount">0</strong> 台设备执行批量OTA升级</p>
                </div>

                <div class="card border-info">
                    <div class="card-header bg-info bg-opacity-10">
                        <h6 class="mb-0 text-info">
                            <i class="fas fa-info-circle me-2"></i>系统将执行以下操作
                        </h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>检查IoT管理器状态</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>根据设备类型自动选择最新固件</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>为每台设备创建OTA升级任务</li>
                            <li class="mb-0"><i class="fas fa-check text-success me-2"></i>按顺序执行升级，避免系统负载过高</li>
                        </ul>
                    </div>
                </div>

                <div class="alert alert-warning border-warning mt-3">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>重要提醒：</strong>请确保设备在线且网络连接稳定，升级过程中请勿关闭页面。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>取消
                </button>
                <button type="button" class="btn btn-primary btn-primary-gradient" id="confirmBatchOtaBtn">
                    <i class="fas fa-rocket me-1"></i>确认启动
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let allDevices = [];
let filteredDevices = [];
let selectedDevices = [];

$(document).ready(function() {
    // 加载固件版本列表
    loadFirmwareVersions();

    // 绑定事件
    $('#filterBtn').click(filterDevices);
    $('#resetBtn').click(resetFilters);
    $('#selectAllBtn').click(selectAllDevices);
    $('#selectNoneBtn').click(selectNoneDevices);
    $('#selectAllCheckbox').change(toggleAllDevices);
    $('#startBatchOtaBtn').click(showBatchOtaModal);
    $('#confirmBatchOtaBtn').click(startBatchOta);

    // 初始加载所有设备
    filterDevices();
});

function loadFirmwareVersions() {
    $.get('/api/batch_ota/get_firmware_versions')
        .done(function(response) {
            if (response.success) {
                const select = $('#firmwareVersionSelect');
                select.empty();
                response.versions.forEach(function(version) {
                    select.append(`<option value="${version}">${version}</option>`);
                });
            }
        })
        .fail(function() {
            $('#firmwareVersionSelect').html('<option value="">加载失败</option>');
        });
}

function filterDevices() {
    const filterData = {
        device_types: $('input[name="device_types"]:checked').map(function() {
            return $(this).val();
        }).get(),
        product_keys: $('select[name="product_keys"]').val() || [],
        firmware_versions: $('select[name="firmware_versions"]').val() || [],
        keywords: $('input[name="keywords"]').val(),
        exclude_keywords: $('input[name="exclude_keywords"]').val()
    };

    $.ajax({
        url: '/api/batch_ota/filter_devices',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(filterData),
        success: function(response) {
            if (response.success) {
                filteredDevices = response.devices;
                updateDeviceTable();
                updateFilteredCount(response.total);
            } else {
                showAlert('筛选失败: ' + response.message, 'danger');
            }
        },
        error: function() {
            showAlert('筛选请求失败，请重试', 'danger');
        }
    });
}

function updateDeviceTable() {
    const tbody = $('#deviceTableBody');
    tbody.empty();

    if (filteredDevices.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="8" class="text-center text-muted py-5">
                    <div class="d-flex flex-column align-items-center">
                        <i class="fas fa-search fa-2x mb-2 text-warning"></i>
                        <span>没有找到符合条件的设备</span>
                        <small class="text-muted mt-1">请调整筛选条件后重试</small>
                    </div>
                </td>
            </tr>
        `);
        return;
    }

    filteredDevices.forEach(function(device) {
        const isSelected = selectedDevices.includes(device.id);
        const row = `
            <tr>
                <td class="text-center">
                    <input type="checkbox" class="device-checkbox" value="${device.id}" ${isSelected ? 'checked' : ''}>
                </td>
                <td class="device-id-cell">${device.device_id}</td>
                <td class="device-remark-cell">${device.device_remark || '-'}</td>
                <td>
                    <span class="badge bg-gradient bg-info text-white">${device.device_type_name}</span>
                </td>
                <td>
                    <code class="product-key-cell">${device.product_key}</code>
                </td>
                <td>
                    <span class="badge bg-gradient bg-secondary">${device.firmware_version || '-'}</span>
                </td>
                <td class="text-muted">
                    <small><i class="fas fa-clock me-1"></i>${device.last_ota_time || '-'}</small>
                </td>
                <td>
                    <span class="status-badge ${device.last_ota_status === '成功' ? 'status-online' :
                                               device.last_ota_status === '失败' ? 'status-offline' : 'bg-secondary'}">
                        ${device.last_ota_status || '未知'}
                    </span>
                </td>
            </tr>
        `;
        tbody.append(row);
    });

    // 绑定复选框事件
    $('.device-checkbox').change(updateSelectedDevices);
}

function updateSelectedDevices() {
    selectedDevices = $('.device-checkbox:checked').map(function() {
        return parseInt($(this).val());
    }).get();

    updateSelectedCount();
    updateSelectAllCheckbox();
}

function updateSelectedCount() {
    const count = selectedDevices.length;
    $('#selectedCount').text(count);
    $('#startBatchOtaBtn').prop('disabled', count === 0);
}

function updateFilteredCount(count) {
    $('#filteredCount').text(count);
}

function updateSelectAllCheckbox() {
    const totalCheckboxes = $('.device-checkbox').length;
    const checkedCheckboxes = $('.device-checkbox:checked').length;

    if (checkedCheckboxes === 0) {
        $('#selectAllCheckbox').prop('indeterminate', false).prop('checked', false);
    } else if (checkedCheckboxes === totalCheckboxes) {
        $('#selectAllCheckbox').prop('indeterminate', false).prop('checked', true);
    } else {
        $('#selectAllCheckbox').prop('indeterminate', true);
    }
}

function selectAllDevices() {
    $('.device-checkbox').prop('checked', true);
    updateSelectedDevices();
}

function selectNoneDevices() {
    $('.device-checkbox').prop('checked', false);
    updateSelectedDevices();
}

function toggleAllDevices() {
    const isChecked = $('#selectAllCheckbox').prop('checked');
    $('.device-checkbox').prop('checked', isChecked);
    updateSelectedDevices();
}

function resetFilters() {
    $('input[name="device_types"]').prop('checked', false);
    $('select[name="product_keys"]').val([]);
    $('select[name="firmware_versions"]').val([]);
    $('input[name="keywords"]').val('');
    $('input[name="exclude_keywords"]').val('');
    filterDevices();
}

function showBatchOtaModal() {
    $('#confirmDeviceCount').text(selectedDevices.length);
    $('#batchOtaModal').modal('show');
}

function startBatchOta() {
    if (selectedDevices.length === 0) {
        showAlert('请选择要升级的设备', 'warning');
        return;
    }

    $('#confirmBatchOtaBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>启动中...');

    $.ajax({
        url: '/api/batch_ota/start',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            device_ids: selectedDevices
        }),
        success: function(response) {
            $('#batchOtaModal').modal('hide');
            if (response.success) {
                showAlert(response.message, 'success');
                if (response.details && response.details.length > 0) {
                    console.log('批量OTA详情:', response.details);
                }
            } else {
                showAlert(response.message, 'danger');
            }
        },
        error: function() {
            showAlert('启动批量OTA失败，请重试', 'danger');
        },
        complete: function() {
            $('#confirmBatchOtaBtn').prop('disabled', false).html('<i class="fas fa-rocket me-1"></i>确认启动');
        }
    });
}

function showAlert(message, type) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // 移除现有的alert
    $('.alert').remove();

    // 添加新的alert到页面顶部
    $('body').prepend(alertHtml);

    // 3秒后自动消失
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 3000);
}
</script>
{% endblock %}
