<!-- 调试脚本模态框 -->
<div class="modal fade" id="debugScriptModal" tabindex="-1" aria-labelledby="debugScriptModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="debugScriptModalLabel">
                    <i class="fas fa-code text-primary me-2"></i>调试脚本管理
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="scriptLoading" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在获取脚本状态，请稍候...</p>
                </div>
                
                <div id="scriptContent" class="d-none">
                    <!-- 脚本状态 -->
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white">
                            <h6 class="mb-0">脚本状态</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>状态:</strong> <span id="scriptStatus" class="badge bg-secondary">未启动</span></p>
                                    <p><strong>频率:</strong> <span id="scriptFrequency">60</span> 秒</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>总执行次数:</strong> <span id="scriptTotalExecutions">0</span></p>
                                    <p><strong>成功执行次数:</strong> <span id="scriptSuccessfulExecutions">0</span></p>
                                </div>
                            </div>
                            <p><strong>最后执行时间:</strong> <span id="scriptLastExecutionTime">--</span></p>
                            <p><strong>最后执行状态:</strong> <span id="scriptLastExecutionStatus">--</span></p>
                        </div>
                    </div>
                    
                    <!-- 脚本配置 -->
                    <div id="scriptConfigForm" class="card">
                        <div class="card-header bg-info text-white">
                            <h6 class="mb-0">脚本配置</h6>
                        </div>
                        <div class="card-body">
                            <form id="scriptForm">
                                <div class="mb-3">
                                    <label for="scriptFrequencyInput" class="form-label">执行频率（秒）</label>
                                    <input type="number" class="form-control" id="scriptFrequencyInput" min="5" value="60" required>
                                    <div class="form-text">设置脚本执行的时间间隔，最小5秒</div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <div id="scriptError" class="alert alert-danger d-none">
                    获取脚本状态失败，请重试。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-danger" id="stopScriptBtn" onclick="stopScript()">停止脚本</button>
                <button type="button" class="btn btn-success" id="startScriptBtn" onclick="startScript()">启动脚本</button>
            </div>
        </div>
    </div>
</div>

<script>
    // 获取脚本状态
    function getScriptStatus() {
        // 显示加载中状态
        document.getElementById('scriptLoading').classList.remove('d-none');
        document.getElementById('scriptContent').classList.add('d-none');
        document.getElementById('scriptError').classList.add('d-none');
        
        // 发送请求获取脚本状态
        fetch(`/debug_script/status/{{ device.id }}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('网络请求失败');
                }
                return response.json();
            })
            .then(data => {
                // 隐藏加载中状态
                document.getElementById('scriptLoading').classList.add('d-none');
                
                if (data.error) {
                    // 显示错误信息
                    document.getElementById('scriptError').textContent = '获取脚本状态失败: ' + data.error;
                    document.getElementById('scriptError').classList.remove('d-none');
                    return;
                }
                
                // 更新脚本状态
                updateScriptStatus(data);
                
                // 显示内容区域
                document.getElementById('scriptContent').classList.remove('d-none');
            })
            .catch(error => {
                console.error('获取脚本状态失败:', error);
                // 隐藏加载中状态，显示错误信息
                document.getElementById('scriptLoading').classList.add('d-none');
                document.getElementById('scriptError').textContent = '获取脚本状态失败: ' + error.message;
                document.getElementById('scriptError').classList.remove('d-none');
            });
    }
    
    // 更新脚本状态显示
    function updateScriptStatus(data) {
        // 更新状态标签
        const statusElement = document.getElementById('scriptStatus');
        if (data.is_running) {
            statusElement.textContent = '运行中';
            statusElement.className = 'badge bg-success';
            document.getElementById('startScriptBtn').disabled = true;
            document.getElementById('stopScriptBtn').disabled = false;
        } else {
            statusElement.textContent = '已停止';
            statusElement.className = 'badge bg-secondary';
            document.getElementById('startScriptBtn').disabled = false;
            document.getElementById('stopScriptBtn').disabled = true;
        }
        
        // 更新其他信息
        document.getElementById('scriptFrequency').textContent = data.frequency;
        document.getElementById('scriptTotalExecutions').textContent = data.total_executions;
        document.getElementById('scriptSuccessfulExecutions').textContent = data.successful_executions;
        document.getElementById('scriptLastExecutionTime').textContent = data.last_execution_time || '--';
        document.getElementById('scriptLastExecutionStatus').textContent = data.last_execution_status || '--';
        
        // 更新表单值
        document.getElementById('scriptFrequencyInput').value = data.frequency;
    }
    
    // 启动脚本
    function startScript() {
        // 获取频率值
        const frequency = document.getElementById('scriptFrequencyInput').value;
        if (!frequency || frequency < 5) {
            alert('频率不能小于5秒');
            return;
        }
        
        // 禁用按钮
        document.getElementById('startScriptBtn').disabled = true;
        
        // 发送请求启动脚本
        fetch(`/debug_script/start/{{ device.id }}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                frequency: parseInt(frequency)
            }),
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 刷新脚本状态
                getScriptStatus();
                alert(data.message || '脚本启动成功');
            } else {
                alert(data.message || '脚本启动失败');
                document.getElementById('startScriptBtn').disabled = false;
            }
        })
        .catch(error => {
            console.error('启动脚本失败:', error);
            alert('启动脚本失败，请重试');
            document.getElementById('startScriptBtn').disabled = false;
        });
    }
    
    // 停止脚本
    function stopScript() {
        // 禁用按钮
        document.getElementById('stopScriptBtn').disabled = true;
        
        // 发送请求停止脚本
        fetch(`/debug_script/stop/{{ device.id }}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({}),
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 刷新脚本状态
                getScriptStatus();
                alert(data.message || '脚本停止成功');
            } else {
                alert(data.message || '脚本停止失败');
                document.getElementById('stopScriptBtn').disabled = false;
            }
        })
        .catch(error => {
            console.error('停止脚本失败:', error);
            alert('停止脚本失败，请重试');
            document.getElementById('stopScriptBtn').disabled = false;
        });
    }
    
    // 显示模态框时获取脚本状态
    document.getElementById('debugScriptModal').addEventListener('show.bs.modal', function (event) {
        getScriptStatus();
    });
</script>
