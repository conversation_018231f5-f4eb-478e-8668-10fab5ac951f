<!-- 全局通知组件 -->
<div class="toast-container position-fixed bottom-0 end-0 p-3" style="z-index: 1055;">
    <div id="globalNotificationToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <i id="globalNotificationIcon" class="fas fa-info-circle me-2"></i>
            <strong id="globalNotificationTitle" class="me-auto">通知</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="globalNotificationMessage">
            <!-- 通知内容 -->
        </div>
    </div>
</div>

<script>
// 全局通知组件
window.GlobalNotification = (function() {
    let notificationToast = null;
    
    // 初始化通知组件
    function init() {
        const toastEl = document.getElementById('globalNotificationToast');
        if (toastEl && !notificationToast) {
            notificationToast = new bootstrap.Toast(toastEl, {
                autohide: true,
                delay: 3000
            });
        }
    }
    
    // 显示通知
    function show(message, type = 'info', title = '通知') {
        // 确保组件已初始化
        if (!notificationToast) {
            init();
        }
        
        const toast = document.getElementById('globalNotificationToast');
        const icon = document.getElementById('globalNotificationIcon');
        const titleEl = document.getElementById('globalNotificationTitle');
        const messageEl = document.getElementById('globalNotificationMessage');
        
        if (!toast || !icon || !titleEl || !messageEl) {
            console.error('通知组件元素未找到');
            return;
        }
        
        // 重置样式
        toast.className = 'toast';
        
        // 设置图标和样式
        switch (type) {
            case 'success':
                toast.classList.add('bg-success', 'text-white');
                icon.className = 'fas fa-check-circle me-2';
                break;
            case 'error':
            case 'danger':
                toast.classList.add('bg-danger', 'text-white');
                icon.className = 'fas fa-exclamation-circle me-2';
                break;
            case 'warning':
                toast.classList.add('bg-warning', 'text-dark');
                icon.className = 'fas fa-exclamation-triangle me-2';
                break;
            case 'info':
            default:
                toast.classList.add('bg-info', 'text-white');
                icon.className = 'fas fa-info-circle me-2';
                break;
        }
        
        titleEl.textContent = title;
        messageEl.textContent = message;
        
        notificationToast.show();
    }
    
    // 隐藏通知
    function hide() {
        if (notificationToast) {
            notificationToast.hide();
        }
    }
    
    // 公开接口
    return {
        init: init,
        show: show,
        hide: hide,
        // 兼容性别名
        showAlert: show,
        showNotification: show
    };
})();

// DOM加载完成后自动初始化
document.addEventListener('DOMContentLoaded', function() {
    GlobalNotification.init();
});

// 为了向后兼容，提供全局函数
window.showAlert = function(message, type, title) {
    GlobalNotification.show(message, type, title);
};

window.showNotification = function(message, type, title) {
    GlobalNotification.show(message, type, title);
};
</script>
