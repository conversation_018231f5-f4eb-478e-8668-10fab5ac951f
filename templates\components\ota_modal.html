<!-- OTA升级模态框 -->
<div class="modal fade" id="otaModal" tabindex="-1" aria-labelledby="otaModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="otaModalLabel">
                    <i class="fas fa-sync-alt text-primary me-2"></i>固件升级
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="otaForm">
                <div class="modal-body">
                    <input type="hidden" id="otaDeviceId" name="device_ids[]">

                    <!-- 设备信息显示 -->
                    <div class="mb-3">
                        <label class="form-label">设备信息</label>
                        <div class="card bg-light">
                            <div class="card-body py-2">
                                <div id="deviceInfo" class="text-muted">
                                    <i class="fas fa-spinner fa-spin me-1"></i>正在加载设备信息...
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 设备类型选择 -->
                    <div class="mb-3">
                        <label for="deviceTypeSelect" class="form-label">设备类型</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-microchip"></i></span>
                            <select class="form-select" id="deviceTypeSelect">
                                <option value="">请选择设备类型...</option>
                                <option value="10">V2 (旧版霍尔传感器版本，黑色PCB)</option>
                                <option value="50">V5 (新版BL0910 10通道版本)</option>
                                <option value="51">V51 (新版BL0939 2通道版本)</option>
                            </select>
                        </div>
                        <div class="form-text">
                            <span id="deviceTypeHint">如果设备类型已知，将自动选择；如果未知，请手动选择</span>
                        </div>
                    </div>

                    <!-- 固件选择 -->
                    <div class="mb-3">
                        <label for="firmwareSelect" class="form-label">选择固件</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-file-code"></i></span>
                            <select class="form-select" id="firmwareSelect" name="firmware_id" required disabled>
                                <option value="">请先选择设备类型...</option>
                            </select>
                        </div>
                        <div class="form-text">
                            <span id="firmwareHint">选择设备类型后将显示对应的固件列表</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="button" class="btn btn-success" id="upgradeLatestBtn" disabled>
                        <i class="fas fa-rocket me-1"></i>升级最新固件
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload me-1"></i>开始升级
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- OTA相关的JavaScript代码 -->
<script>
// 全局变量
let currentDeviceInfo = null;
let availableFirmwares = [];

async function startOta(deviceId) {
    // 设置设备ID
    document.getElementById('otaDeviceId').value = deviceId;

    // 重置表单状态
    resetOtaModal();

    // 加载设备信息
    await loadDeviceInfo(deviceId);

    // 显示模态框
    var otaModal = new bootstrap.Modal(document.getElementById('otaModal'));
    otaModal.show();
}

function resetOtaModal() {
    // 重置所有选择
    document.getElementById('deviceTypeSelect').value = '';
    document.getElementById('firmwareSelect').innerHTML = '<option value="">请先选择设备类型...</option>';
    document.getElementById('firmwareSelect').disabled = true;
    document.getElementById('upgradeLatestBtn').disabled = true;

    // 重置提示信息
    document.getElementById('deviceInfo').innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>正在加载设备信息...';
    document.getElementById('deviceTypeHint').textContent = '如果设备类型已知，将自动选择；如果未知，请手动选择';
    document.getElementById('firmwareHint').textContent = '选择设备类型后将显示对应的固件列表';
}

async function loadDeviceInfo(deviceId) {
    try {
        // 获取设备信息
        const response = await fetch(`/api/devices/batch_info`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                device_ids: [deviceId]
            })
        });

        if (response.ok) {
            const data = await response.json();
            if (data.success && data.devices && data.devices.length > 0) {
                currentDeviceInfo = data.devices[0];
                updateDeviceInfoDisplay();

                // 如果设备有类型信息，自动选择
                if (currentDeviceInfo.device_type) {
                    document.getElementById('deviceTypeSelect').value = currentDeviceInfo.device_type;
                    await loadFirmwaresByDeviceType(currentDeviceInfo.device_type);
                    document.getElementById('deviceTypeHint').innerHTML =
                        '<i class="fas fa-check text-success me-1"></i>设备类型已自动识别';
                } else {
                    document.getElementById('deviceTypeHint').innerHTML =
                        '<i class="fas fa-exclamation-triangle text-warning me-1"></i>设备类型未知，请手动选择';
                }
            }
        }
    } catch (error) {
        console.error('加载设备信息失败:', error);
        document.getElementById('deviceInfo').innerHTML =
            '<i class="fas fa-exclamation-triangle text-danger me-1"></i>加载设备信息失败';
    }
}

function updateDeviceInfoDisplay() {
    if (currentDeviceInfo) {
        const deviceTypeText = currentDeviceInfo.device_type_name || '未设置';
        document.getElementById('deviceInfo').innerHTML = `
            <div class="row">
                <div class="col-6">
                    <strong>设备ID:</strong> ${currentDeviceInfo.device_id}
                </div>
                <div class="col-6">
                    <strong>设备类型:</strong> ${deviceTypeText}
                </div>
            </div>
            <div class="row mt-1">
                <div class="col-6">
                    <strong>固件版本:</strong> ${currentDeviceInfo.firmware_version || '未知'}
                </div>
                <div class="col-6">
                    <strong>备注:</strong> ${currentDeviceInfo.device_remark || '无'}
                </div>
            </div>
        `;
    }
}

async function loadFirmwaresByDeviceType(deviceType) {
    try {
        const response = await fetch(`/api/firmwares/by_device_type?device_type=${deviceType}`);
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                availableFirmwares = data.firmwares;
                updateFirmwareSelect();

                // 启用升级最新固件按钮
                document.getElementById('upgradeLatestBtn').disabled = false;

                document.getElementById('firmwareHint').innerHTML =
                    `<i class="fas fa-check text-success me-1"></i>已加载 ${data.firmwares.length} 个固件`;
            }
        }
    } catch (error) {
        console.error('加载固件列表失败:', error);
        document.getElementById('firmwareHint').innerHTML =
            '<i class="fas fa-exclamation-triangle text-danger me-1"></i>加载固件列表失败';
    }
}

function updateFirmwareSelect() {
    const firmwareSelect = document.getElementById('firmwareSelect');
    firmwareSelect.innerHTML = '<option value="">请选择固件...</option>';

    availableFirmwares.forEach(firmware => {
        const option = document.createElement('option');
        option.value = firmware.id;
        option.textContent = `${firmware.name} (v${firmware.version})`;
        option.dataset.version = firmware.version;
        option.dataset.description = firmware.description;
        firmwareSelect.appendChild(option);
    });

    firmwareSelect.disabled = false;
}

// 设备类型选择事件
document.getElementById('deviceTypeSelect').addEventListener('change', async function() {
    const deviceType = this.value;
    if (deviceType) {
        document.getElementById('firmwareSelect').innerHTML =
            '<option value=""><i class="fas fa-spinner fa-spin"></i> 正在加载固件...</option>';
        document.getElementById('firmwareSelect').disabled = true;
        document.getElementById('upgradeLatestBtn').disabled = true;

        await loadFirmwaresByDeviceType(deviceType);
    } else {
        document.getElementById('firmwareSelect').innerHTML = '<option value="">请先选择设备类型...</option>';
        document.getElementById('firmwareSelect').disabled = true;
        document.getElementById('upgradeLatestBtn').disabled = true;
        document.getElementById('firmwareHint').textContent = '选择设备类型后将显示对应的固件列表';
    }
});

// 升级最新固件按钮事件
document.getElementById('upgradeLatestBtn').addEventListener('click', async function() {
    const deviceType = document.getElementById('deviceTypeSelect').value;
    if (!deviceType) {
        showNotification('请先选择设备类型', 'warning');
        return;
    }

    try {
        // 获取最新固件
        const response = await fetch(`/api/latest_firmware/${deviceType}`);
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                // 自动选择最新固件并开始升级
                document.getElementById('firmwareSelect').value = data.firmware.id;
                await startOtaUpgrade(data.firmware.id);
            } else {
                showNotification(data.message || '获取最新固件失败', 'error');
            }
        }
    } catch (error) {
        console.error('升级最新固件失败:', error);
        showNotification('升级最新固件失败，请重试', 'error');
    }
});

// 处理OTA表单提交
document.getElementById('otaForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const firmwareId = document.getElementById('firmwareSelect').value;
    if (!firmwareId) {
        showNotification('请选择要升级的固件', 'warning');
        return;
    }

    await startOtaUpgrade(firmwareId);
});

async function startOtaUpgrade(firmwareId) {
    const deviceId = document.getElementById('otaDeviceId').value;

    try {
        const response = await fetch('/ota/start', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                device_ids: [deviceId],
                firmware_id: firmwareId
            })
        });

        const data = await response.json();

        if (data.success) {
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('otaModal'));
            modal.hide();

            // 显示成功消息
            showNotification('OTA升级任务已创建', 'success');

            // 可选：刷新当前页面的设备列表（如果已实现分页）
            if (typeof refreshDeviceList === 'function') {
                refreshDeviceList();
            }
        } else {
            showNotification(data.message || '创建OTA任务失败', 'error');
        }
    } catch (error) {
        console.error('Error:', error);
        showNotification('创建OTA任务失败，请重试', 'error');
    }
}

// 通知函数
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    // 添加到页面
    document.body.appendChild(notification);

    // 3秒后自动移除
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}
</script> 