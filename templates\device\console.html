{% extends "base.html" %}

{% block title %}设备控制台 - {{ device.device_id }}{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- 设备基本信息卡片 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-primary shadow-sm">
                <div class="card-header bg-primary bg-opacity-10 border-primary">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0 text-primary">
                            <i class="fas fa-desktop me-2"></i>设备控制台
                        </h4>
                        <div class="d-flex gap-2">
                            <button class="btn btn-outline-primary btn-sm" onclick="refreshDeviceStatus()">
                                <i class="fas fa-sync-alt me-1"></i>刷新状态
                            </button>
                            <a href="{{ url_for('device.devices') }}" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-arrow-left me-1"></i>返回列表
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="device-info-section">
                                <h6 class="text-muted mb-3">
                                    <i class="fas fa-info-circle me-1"></i>基本信息
                                </h6>
                                <div class="row g-3">
                                    <div class="col-sm-6">
                                        <label class="form-label small fw-medium">设备ID</label>
                                        <div class="form-control-plaintext bg-light rounded px-3 py-2">
                                            <code>{{ device.device_id }}</code>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <label class="form-label small fw-medium">设备备注</label>
                                        <div class="form-control-plaintext bg-light rounded px-3 py-2">
                                            {{ device.device_remark or '无备注' }}
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <label class="form-label small fw-medium">产品密钥</label>
                                        <div class="form-control-plaintext bg-light rounded px-3 py-2">
                                            <code>{{ device.product_key }}</code>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <label class="form-label small fw-medium">固件版本</label>
                                        <div class="form-control-plaintext bg-light rounded px-3 py-2">
                                            {{ device.firmware_version or '未知' }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="device-status-section">
                                <h6 class="text-muted mb-3">
                                    <i class="fas fa-signal me-1"></i>设备状态
                                </h6>
                                <div class="row g-3">
                                    <div class="col-sm-6">
                                        <label class="form-label small fw-medium">在线状态</label>
                                        <div id="deviceOnlineStatus" class="form-control-plaintext">
                                            <span class="badge bg-secondary">
                                                <i class="fas fa-circle me-1"></i>检查中...
                                            </span>
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <label class="form-label small fw-medium">最后检查</label>
                                        <div id="deviceLastCheck" class="form-control-plaintext bg-light rounded px-3 py-2 small">
                                            检查中...
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <label class="form-label small fw-medium">最后在线</label>
                                        <div id="deviceLastOnline" class="form-control-plaintext bg-light rounded px-3 py-2 small">
                                            检查中...
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <label class="form-label small fw-medium">创建时间</label>
                                        <div class="form-control-plaintext bg-light rounded px-3 py-2 small">
                                            {{ device.created_at.strftime('%Y-%m-%d %H:%M:%S') if device.created_at else '未知' }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 功能模块区域 -->
    <div class="row g-4">
        <!-- 设备升级模块 -->
        <div class="col-lg-6">
            <div class="card h-100 border-info shadow-sm">
                <div class="card-header bg-info bg-opacity-10 border-info">
                    <h5 class="mb-0 text-info">
                        <i class="fas fa-rocket me-2"></i>设备升级 (OTA)
                    </h5>
                </div>
                <div class="card-body">
                    <div id="otaStatusSection" class="mb-3">
                        <h6 class="text-muted mb-2">当前升级状态</h6>
                        <div id="otaCurrentStatus" class="alert alert-secondary">
                            <i class="fas fa-info-circle me-1"></i>暂无升级任务
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="firmwareSelect" class="form-label">
                            <i class="fas fa-microchip me-1"></i>选择固件版本
                        </label>
                        <select class="form-select" id="firmwareSelect">
                            <option value="">请选择固件...</option>
                            {% for firmware in firmwares %}
                            <option value="{{ firmware.id }}"
                                    data-version="{{ firmware.version }}"
                                    data-crc="{{ firmware.crc32 }}"
                                    data-name="{{ firmware.name }}"
                                    data-size="{{ firmware.size }}"
                                    data-upload-time="{{ firmware.upload_time.strftime('%Y-%m-%d') }}">
                                {{ firmware.name }} - v{{ firmware.version }} ({{ firmware.crc32 }})
                            </option>
                            {% endfor %}
                        </select>
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            选择要升级的固件版本，系统将自动验证兼容性
                        </div>
                    </div>

                    <!-- 固件详细信息显示 -->
                    <div id="firmwareInfo" class="mb-3" style="display: none;">
                        <div class="card border-info">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">
                                    <i class="fas fa-info-circle text-info me-1"></i>固件详细信息
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p class="mb-1"><strong>固件名称:</strong> <span id="infoName">-</span></p>
                                        <p class="mb-1"><strong>版本号:</strong> <span id="infoVersion">-</span></p>
                                        <p class="mb-1"><strong>文件大小:</strong> <span id="infoSize">-</span></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p class="mb-1"><strong>CRC32:</strong> <code id="infoCrc32">-</code></p>
                                        <p class="mb-1"><strong>上传时间:</strong> <span id="infoUploadTime">-</span></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid">
                        <button class="btn btn-info" onclick="startDeviceOta()">
                            <i class="fas fa-rocket me-1"></i>开始升级
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设备编辑模块 -->
        <div class="col-lg-6">
            <div class="card h-100 border-warning shadow-sm">
                <div class="card-header bg-warning bg-opacity-10 border-warning">
                    <h5 class="mb-0 text-warning">
                        <i class="fas fa-edit me-2"></i>设备编辑
                    </h5>
                </div>
                <div class="card-body">
                    <form id="deviceEditForm">
                        <div class="mb-3">
                            <label for="editDeviceRemark" class="form-label">设备备注</label>
                            <input type="text" class="form-control" id="editDeviceRemark" 
                                   value="{{ device.device_remark or '' }}" placeholder="输入设备备注...">
                        </div>
                        <div class="mb-3">
                            <label for="editProductKey" class="form-label">产品密钥</label>
                            <input type="text" class="form-control" id="editProductKey" 
                                   value="{{ device.product_key }}" placeholder="输入产品密钥...">
                        </div>
                        <div class="d-grid">
                            <button type="button" class="btn btn-warning" onclick="saveDeviceEdit()">
                                <i class="fas fa-save me-1"></i>保存修改
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 设备配置模块 -->
        <div class="col-lg-6">
            <div class="card h-100 border-success shadow-sm">
                <div class="card-header bg-success bg-opacity-10 border-success">
                    <h5 class="mb-0 text-success">
                        <i class="fas fa-cogs me-2"></i>设备配置
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="text-muted mb-2">服务器配置</h6>
                        <button class="btn btn-outline-success btn-sm w-100 mb-2" onclick="configDeviceServer()">
                            <i class="fas fa-server me-1"></i>配置服务器参数
                        </button>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="text-muted mb-2">参数管理</h6>
                        <a href="/device/{{ device.id }}/parameters" class="btn btn-outline-success btn-sm w-100 mb-2">
                            <i class="fas fa-list me-1"></i>查看设备参数
                        </a>
                        <button class="btn btn-outline-success btn-sm w-100" onclick="setDeviceParameters()">
                            <i class="fas fa-wrench me-1"></i>设置设备参数
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 配置二维码模块 -->
        <div class="col-lg-6">
            <div class="card h-100 border-dark shadow-sm">
                <div class="card-header bg-dark bg-opacity-10 border-dark">
                    <h5 class="mb-0 text-dark">
                        <i class="fas fa-qrcode me-2"></i>配置二维码
                    </h5>
                </div>
                <div class="card-body text-center">
                    <div id="qrCodeContainer" class="mb-3">
                        <div class="text-muted">
                            <i class="fas fa-qrcode fa-3x mb-2"></i>
                            <p>点击生成设备配置二维码</p>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button class="btn btn-dark" onclick="generateDeviceQR()">
                            <i class="fas fa-qrcode me-1"></i>生成二维码
                        </button>
                        <button class="btn btn-outline-dark btn-sm" onclick="downloadQR()" id="downloadQRBtn" style="display: none;">
                            <i class="fas fa-download me-1"></i>下载二维码
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 加载状态模态框 -->
<div class="modal fade" id="loadingModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center py-4">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <div id="loadingMessage">处理中...</div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 全局变量
const deviceId = {{ device.id }};
let currentQRImage = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 加载设备状态
    loadDeviceStatus();

    // 设置定时刷新状态（每30秒）
    setInterval(loadDeviceStatus, 30000);

    // 绑定固件选择事件
    const firmwareSelect = document.getElementById('firmwareSelect');
    if (firmwareSelect) {
        firmwareSelect.addEventListener('change', handleFirmwareSelection);
    }
});

// 加载设备状态
function loadDeviceStatus() {
    fetch(`/device/${deviceId}/console/status`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateDeviceStatus(data.device_status);
                updateOtaStatus(data.ota_status);
            }
        })
        .catch(error => {
            console.error('加载设备状态失败:', error);
        });
}

// 更新设备状态显示
function updateDeviceStatus(status) {
    const onlineStatusEl = document.getElementById('deviceOnlineStatus');
    const lastCheckEl = document.getElementById('deviceLastCheck');
    const lastOnlineEl = document.getElementById('deviceLastOnline');
    
    if (status.is_online) {
        onlineStatusEl.innerHTML = '<span class="badge bg-success"><i class="fas fa-circle me-1"></i>在线</span>';
    } else {
        onlineStatusEl.innerHTML = '<span class="badge bg-danger"><i class="fas fa-circle me-1"></i>离线</span>';
    }
    
    lastCheckEl.textContent = status.last_check;
    lastOnlineEl.textContent = status.last_online;
}

// 更新OTA状态显示
function updateOtaStatus(otaStatus) {
    const statusEl = document.getElementById('otaCurrentStatus');
    
    if (otaStatus) {
        let statusClass = 'alert-info';
        let statusIcon = 'fas fa-info-circle';
        
        switch (otaStatus.status) {
            case '成功':
                statusClass = 'alert-success';
                statusIcon = 'fas fa-check-circle';
                break;
            case '失败':
                statusClass = 'alert-danger';
                statusIcon = 'fas fa-times-circle';
                break;
            case '进行中':
                statusClass = 'alert-warning';
                statusIcon = 'fas fa-spinner fa-spin';
                break;
        }
        
        statusEl.className = `alert ${statusClass}`;
        statusEl.innerHTML = `
            <i class="${statusIcon} me-1"></i>
            ${otaStatus.status} - ${otaStatus.firmware_version} (${otaStatus.progress}%)
            <br><small>创建时间: ${otaStatus.created_at}</small>
        `;
    } else {
        statusEl.className = 'alert alert-secondary';
        statusEl.innerHTML = '<i class="fas fa-info-circle me-1"></i>暂无升级任务';
    }
}

// 刷新设备状态
function refreshDeviceStatus() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>刷新中...';
    btn.disabled = true;
    
    fetch(`/device/${deviceId}/console/refresh`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('状态刷新成功', 'success');
                loadDeviceStatus();
            } else {
                showToast('状态刷新失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('刷新状态失败:', error);
            showToast('刷新状态失败', 'error');
        })
        .finally(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
        });
}

// 处理固件选择变化
function handleFirmwareSelection() {
    const firmwareSelect = document.getElementById('firmwareSelect');
    const firmwareInfo = document.getElementById('firmwareInfo');
    const selectedOption = firmwareSelect.options[firmwareSelect.selectedIndex];

    if (firmwareSelect.value && selectedOption) {
        // 显示固件详细信息
        const name = selectedOption.getAttribute('data-name') || '-';
        const version = selectedOption.getAttribute('data-version') || '-';
        const crc32 = selectedOption.getAttribute('data-crc') || '-';
        const size = selectedOption.getAttribute('data-size') || '0';
        const uploadTime = selectedOption.getAttribute('data-upload-time') || '-';

        // 格式化文件大小
        const sizeInKB = (parseInt(size) / 1024).toFixed(2);
        const formattedSize = sizeInKB + ' KB';

        // 更新显示内容
        document.getElementById('infoName').textContent = name;
        document.getElementById('infoVersion').textContent = 'v' + version;
        document.getElementById('infoCrc32').textContent = crc32;
        document.getElementById('infoSize').textContent = formattedSize;
        document.getElementById('infoUploadTime').textContent = uploadTime;

        // 显示信息卡片
        firmwareInfo.style.display = 'block';
        firmwareInfo.classList.add('animate__animated', 'animate__fadeIn');
    } else {
        // 隐藏信息卡片
        firmwareInfo.style.display = 'none';
    }
}

// 开始OTA升级
function startDeviceOta() {
    const firmwareSelect = document.getElementById('firmwareSelect');
    const firmwareId = firmwareSelect.value;
    
    if (!firmwareId) {
        showToast('请选择固件版本', 'warning');
        return;
    }
    
    if (!confirm('确定要开始升级吗？升级过程中设备可能会重启。')) {
        return;
    }
    
    showLoadingModal('正在启动升级...');
    
    // 这里调用现有的OTA升级接口
    fetch('/ota/start', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            device_ids: [deviceId],
            firmware_id: firmwareId
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoadingModal();
        if (data.success) {
            showToast('升级任务已启动', 'success');
            loadDeviceStatus();
        } else {
            showToast('启动升级失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        hideLoadingModal();
        console.error('启动升级失败:', error);
        showToast('启动升级失败', 'error');
    });
}

// 保存设备编辑
function saveDeviceEdit() {
    const remark = document.getElementById('editDeviceRemark').value;
    const productKey = document.getElementById('editProductKey').value;
    
    if (!productKey.trim()) {
        showToast('产品密钥不能为空', 'warning');
        return;
    }
    
    showLoadingModal('正在保存...');
    
    // 调用现有的设备编辑接口
    fetch(`/device/${deviceId}/edit`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            device_remark: remark,
            product_key: productKey
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoadingModal();
        if (data.success) {
            showToast('设备信息已更新', 'success');
            // 刷新页面以显示最新信息
            setTimeout(() => location.reload(), 1000);
        } else {
            showToast('保存失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        hideLoadingModal();
        console.error('保存设备信息失败:', error);
        showToast('保存失败', 'error');
    });
}

// 配置设备服务器
function configDeviceServer() {
    // 调用现有的服务器配置功能
    if (typeof configDeviceServer !== 'undefined') {
        configDeviceServer(deviceId, '{{ device.device_id }}', '{{ device.product_key }}');
    } else {
        showToast('服务器配置功能暂不可用', 'warning');
    }
}

// 设置设备参数
function setDeviceParameters() {
    // 调用现有的参数设置功能
    showToast('参数设置功能开发中...', 'info');
}

// 生成设备二维码
function generateDeviceQR() {
    // showLoadingModal('正在生成二维码...');
    
    fetch(`/device/${deviceId}/console/qr`)
        .then(response => response.json())
        .then(data => {
            hideLoadingModal();
            if (data.success) {
                currentQRImage = data.qr_image;
                document.getElementById('qrCodeContainer').innerHTML = `
                    <img src="${data.qr_image}" alt="设备配置二维码" class="img-fluid" style="max-width: 200px;">
                    <p class="small text-muted mt-2">扫描二维码获取设备配置信息</p>
                `;
                document.getElementById('downloadQRBtn').style.display = 'block';
                // showToast('二维码生成成功', 'success');
            } else {
                showToast('生成二维码失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            hideLoadingModal();
            console.error('生成二维码失败:', error);
            showToast('生成二维码失败', 'error');
        });
}

// 下载二维码
function downloadQR() {
    if (!currentQRImage) {
        showToast('请先生成二维码', 'warning');
        return;
    }
    
    const link = document.createElement('a');
    link.href = currentQRImage;
    link.download = `device_${deviceId}_qr.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 显示加载模态框
function showLoadingModal(message) {
    document.getElementById('loadingMessage').textContent = message;
    const modalElement = document.getElementById('loadingModal');
    modalElement.classList.add('show');
    modalElement.style.display = 'block';
    document.body.classList.add('modal-open');
    
    // 添加背景
    if (!document.querySelector('.modal-backdrop')) {
        const backdrop = document.createElement('div');
        backdrop.className = 'modal-backdrop fade show';
        document.body.appendChild(backdrop);
    }
}

// 隐藏加载模态框
function hideLoadingModal() {
    const modalElement = document.getElementById('loadingModal');
    modalElement.classList.remove('show');
    modalElement.style.display = 'none';
    document.body.classList.remove('modal-open');
    
    // 移除背景
    const backdrop = document.querySelector('.modal-backdrop');
    if (backdrop) {
        backdrop.remove();
    }
}

// 显示提示消息
function showToast(message, type = 'info') {
    // 这里可以使用现有的提示系统
    console.log(`[${type.toUpperCase()}] ${message}`);
    
    // 简单的alert替代，实际项目中应该使用更好的提示组件
    if (type === 'error') {
        alert('错误: ' + message);
    } else if (type === 'success') {
        alert('成功: ' + message);
    } else if (type === 'warning') {
        alert('警告: ' + message);
    } else {
        alert('信息: ' + message);
    }
}

generateDeviceQR();
</script>
{% endblock %}
