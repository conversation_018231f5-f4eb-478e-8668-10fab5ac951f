{% extends "base.html" %}

{% block title %}设备地图{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="mb-0">
                    <i class="fas fa-map-marked-alt text-primary"></i> 设备地图
                </h2>
                <div class="d-flex gap-2">
                    <button id="refresh-btn" class="btn btn-primary">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                    <button id="center-map-btn" class="btn btn-info">
                        <i class="fas fa-crosshairs"></i> 居中
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 状态统计卡片 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-primary">
                <div class="card-body text-center">
                    <h5 class="card-title text-primary">总设备数</h5>
                    <h3 id="total-devices" class="mb-0">0</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-success">
                <div class="card-body text-center">
                    <h5 class="card-title text-success">在线设备</h5>
                    <h3 id="online-devices" class="mb-0">0</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-danger">
                <div class="card-body text-center">
                    <h5 class="card-title text-danger">离线设备</h5>
                    <h3 id="offline-devices" class="mb-0">0</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-warning">
                <div class="card-body text-center">
                    <h5 class="card-title text-warning">未知状态</h5>
                    <h3 id="unknown-devices" class="mb-0">0</h3>
                </div>
            </div>
        </div>
    </div>

    <!-- 搜索控制面板 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center gap-3">
                        <div class="input-group" style="max-width: 400px;">
                            <input type="text" id="device-search" class="form-control" placeholder="搜索设备ID或名称...">
                            <button id="search-btn" class="btn btn-outline-primary">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                        <button id="clear-search-btn" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> 清除
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 地图容器 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body p-0">
                    <div id="map-container" style="height: 600px; width: 100%;"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 加载提示模态框 -->
<div class="modal fade" id="loadingModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center py-4">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <div id="loading-message">正在加载地图数据...</div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- 高德地图API -->
<script src="https://webapi.amap.com/maps?v=1.4.15&key=6f025e700cbacbb0bb866712d20bb35c&plugin=AMap.Scale,AMap.ToolBar"></script>

<script>
// 设备地图管理器
class DeviceMapManager {
    constructor() {
        this.map = null;
        this.markers = [];
        this.deviceLocations = [];
        this.deviceStatusCache = {};
        this.isLoading = false;
        this.loadingModalInstance = null; // 保存模态框实例

        // 配置
        this.config = {
            defaultCenter: [116.397428, 39.90923],
            defaultZoom: 11,
            onlineIcon: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png',
            offlineIcon: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_r.png',
            unknownIcon: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_g.png'
        };
        
        this.init();
    }
    
    /**
     * 初始化
     */
    init() {
        this.initMap();
        this.bindEvents();
    }
    
    /**
     * 初始化地图
     */
    initMap() {
        try {
            this.map = new AMap.Map('map-container', {
                resizeEnable: true,
                zoom: this.config.defaultZoom,
                center: this.config.defaultCenter,
                mapStyle: 'amap://styles/normal'
            });
            
            // 添加控件
            this.map.addControl(new AMap.Scale());
            this.map.addControl(new AMap.ToolBar({
                position: 'RB'
            }));
            
            // 地图加载完成后加载设备数据
            this.map.on('complete', () => {
                console.log('地图初始化完成');
                this.loadDeviceData();
            });
            
        } catch (error) {
            console.error('地图初始化失败:', error);
            this.showError('地图初始化失败');
        }
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        document.getElementById('refresh-btn').addEventListener('click', () => {
            this.loadDeviceData(true);
        });
        
        document.getElementById('center-map-btn').addEventListener('click', () => {
            this.centerMap();
        });
        
        document.getElementById('search-btn').addEventListener('click', () => {
            this.searchDevice();
        });
        
        document.getElementById('clear-search-btn').addEventListener('click', () => {
            this.clearSearch();
        });
        
        document.getElementById('device-search').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.searchDevice();
            }
        });

        // 添加实时搜索的防抖处理
        document.getElementById('device-search').addEventListener('input', this.debounce((e) => {
            const searchTerm = e.target.value.trim();
            if (searchTerm.length >= 2) {
                this.searchDevice();
            } else if (searchTerm.length === 0) {
                this.clearSearch();
            }
        }, 500));
    }

    /**
     * 防抖函数
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * 节流函数
     */
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        }
    }

    /**
     * 加载设备数据
     */
    async loadDeviceData(forceRefresh = false) {
        if (this.isLoading) return;
        
        this.isLoading = true;
        // this.showLoading(true);
        
        try {
            // 并行获取位置和状态数据
            const [locations, statusData] = await Promise.all([
                this.fetchDeviceLocations(),
                this.fetchDeviceStatus()
            ]);
            
            this.deviceLocations = locations;
            this.deviceStatusCache = statusData;
            
            // 渲染设备标记
            this.renderDeviceMarkers();
            
            console.log(`数据加载完成: ${locations.length} 个位置, ${Object.keys(statusData).length} 个状态`);
            
        } catch (error) {
            console.error('加载设备数据失败:', error);
            this.showError('加载设备数据失败: ' + error.message);
        } finally {
            this.isLoading = false;
            // this.showLoading(false);
        }
    }
    
    /**
     * 获取设备位置数据
     */
    async fetchDeviceLocations() {
        const response = await fetch('/api/device/locations');
        if (!response.ok) {
            throw new Error(`获取位置数据失败: ${response.status}`);
        }
        const data = await response.json();
        if (data.error) {
            throw new Error(data.error);
        }
        return data;
    }
    
    /**
     * 获取设备状态数据
     */
    async fetchDeviceStatus() {
        const response = await fetch('/api/device_status');
        if (!response.ok) {
            throw new Error(`获取状态数据失败: ${response.status}`);
        }
        const data = await response.json();
        if (data.error) {
            throw new Error(data.error);
        }
        return data;
    }
    
    /**
     * 渲染设备标记（优化版本）
     */
    renderDeviceMarkers() {
        // 清除现有标记
        this.clearMarkers();

        if (!this.deviceLocations || this.deviceLocations.length === 0) {
            this.updateStatistics(0, 0, 0, 0);
            return;
        }

        let totalCount = 0;
        let onlineCount = 0;
        let offlineCount = 0;
        let unknownCount = 0;

        // 批量处理设备标记，提高性能
        const batchSize = 50; // 每批处理50个设备
        const batches = [];

        for (let i = 0; i < this.deviceLocations.length; i += batchSize) {
            batches.push(this.deviceLocations.slice(i, i + batchSize));
        }

        // 使用 requestAnimationFrame 分批渲染，避免阻塞UI
        let batchIndex = 0;
        const processBatch = () => {
            if (batchIndex >= batches.length) {
                // 所有批次处理完成
                this.updateStatistics(totalCount, onlineCount, offlineCount, unknownCount);
                this.fitMapView();
                console.log(`标记渲染完成: 在线 ${onlineCount}, 离线 ${offlineCount}, 未知 ${unknownCount}`);
                return;
            }

            const batch = batches[batchIndex];
            batch.forEach(location => {
                const lat = parseFloat(location.latitude);
                const lng = parseFloat(location.longitude);

                if (isNaN(lat) || isNaN(lng) || lat === 0 || lng === 0) {
                    return;
                }

                totalCount++;

                // 获取设备状态
                const deviceStatus = this.getDeviceStatus(location.id);
                const isOnline = deviceStatus ? deviceStatus.is_online : null;

                // 统计状态
                if (isOnline === true) {
                    onlineCount++;
                } else if (isOnline === false) {
                    offlineCount++;
                } else {
                    unknownCount++;
                }

                // 选择图标
                let iconUrl = this.config.unknownIcon;
                if (isOnline === true) {
                    iconUrl = this.config.onlineIcon;
                } else if (isOnline === false) {
                    iconUrl = this.config.offlineIcon;
                }

                // 创建标记
                const marker = new AMap.Marker({
                    position: [lng, lat],
                    title: location.device_name || location.device_id,
                    icon: new AMap.Icon({
                        size: new AMap.Size(32, 32),
                        image: iconUrl,
                        imageSize: new AMap.Size(32, 32)
                    })
                });

                marker.setMap(this.map);
                this.markers.push(marker);

                // 创建信息窗口
                this.createInfoWindow(marker, location, deviceStatus);
            });

            // 处理下一批
            batchIndex++;
            requestAnimationFrame(processBatch);
        };

        // 开始处理第一批
        processBatch();
    }
    
    /**
     * 获取设备状态
     */
    getDeviceStatus(deviceId) {
        return this.deviceStatusCache[String(deviceId)] || this.deviceStatusCache[deviceId] || null;
    }
    
    /**
     * 创建信息窗口
     */
    createInfoWindow(marker, location, deviceStatus) {
        const statusText = deviceStatus ? 
            (deviceStatus.is_online ? '在线' : '离线') : '未知';
        const statusColor = deviceStatus ? 
            (deviceStatus.is_online ? '#28a745' : '#dc3545') : '#6c757d';
        const lastCheck = deviceStatus ? deviceStatus.last_check : '未知';
        
        const content = `
            <div style="padding: 10px; min-width: 200px;">
                <h6 style="margin: 0 0 10px 0; color: #333;">
                    ${location.device_name || location.device_id}
                </h6>
                <p style="margin: 5px 0; font-size: 12px;">
                    <i class="fas fa-fingerprint"></i> 设备ID: ${location.device_id}
                </p>
                <p style="margin: 5px 0; font-size: 12px;">
                    <i class="fas fa-circle" style="color: ${statusColor}"></i> 
                    状态: ${statusText}
                </p>
                <p style="margin: 5px 0; font-size: 12px;">
                    <i class="fas fa-clock"></i> 最后检查: ${lastCheck}
                </p>
                <p style="margin: 5px 0; font-size: 12px;">
                    <i class="fas fa-map-marker-alt"></i> 
                    地址: ${location.address || '暂无地址信息'}
                </p>
            </div>
        `;
        
        const infoWindow = new AMap.InfoWindow({
            content: content,
            offset: new AMap.Pixel(0, -30)
        });
        
        marker.on('click', () => {
            infoWindow.open(this.map, marker.getPosition());
        });
    }
    
    /**
     * 更新统计数据
     */
    updateStatistics(total, online, offline, unknown) {
        document.getElementById('total-devices').textContent = total;
        document.getElementById('online-devices').textContent = online;
        document.getElementById('offline-devices').textContent = offline;
        document.getElementById('unknown-devices').textContent = unknown;
    }
    
    /**
     * 清除标记
     */
    clearMarkers() {
        this.markers.forEach(marker => {
            marker.setMap(null);
        });
        this.markers = [];
    }
    
    /**
     * 自适应地图视野
     */
    fitMapView() {
        if (this.markers.length === 0) return;
        
        if (this.markers.length === 1) {
            this.map.setCenter(this.markers[0].getPosition());
            this.map.setZoom(15);
        } else {
            this.map.setFitView(this.markers);
        }
    }
    
    /**
     * 居中地图
     */
    centerMap() {
        this.fitMapView();
    }
    
    /**
     * 搜索设备（优化版本）
     */
    searchDevice() {
        const searchTerm = document.getElementById('device-search').value.trim().toLowerCase();
        if (!searchTerm) {
            this.renderDeviceMarkers(); // 显示所有设备
            return;
        }

        // 使用更高效的搜索算法
        const matchedDevices = [];
        const searchTerms = searchTerm.split(' ').filter(term => term.length > 0);

        for (const location of this.deviceLocations) {
            const deviceId = (location.device_id || '').toLowerCase();
            const deviceName = (location.device_name || '').toLowerCase();
            const searchText = `${deviceId} ${deviceName}`;

            // 检查是否所有搜索词都匹配
            const allMatch = searchTerms.every(term => searchText.includes(term));

            if (allMatch) {
                matchedDevices.push(location);
            }
        }

        if (matchedDevices.length === 0) {
            this.showInfo('未找到匹配的设备');
            return;
        }

        console.log(`搜索 "${searchTerm}" 找到 ${matchedDevices.length} 个设备`);

        // 高亮匹配的设备
        this.highlightDevices(matchedDevices);
    }
    
    /**
     * 高亮设备
     */
    highlightDevices(devices) {
        // 清除现有标记
        this.clearMarkers();
        
        // 只显示匹配的设备
        devices.forEach(location => {
            const lat = parseFloat(location.latitude);
            const lng = parseFloat(location.longitude);
            
            if (isNaN(lat) || isNaN(lng)) return;
            
            const deviceStatus = this.getDeviceStatus(location.id);
            const isOnline = deviceStatus ? deviceStatus.is_online : null;
            
            let iconUrl = this.config.unknownIcon;
            if (isOnline === true) {
                iconUrl = this.config.onlineIcon;
            } else if (isOnline === false) {
                iconUrl = this.config.offlineIcon;
            }
            
            const marker = new AMap.Marker({
                position: [lng, lat],
                title: location.device_name || location.device_id,
                icon: new AMap.Icon({
                    size: new AMap.Size(40, 40), // 稍大一些以突出显示
                    image: iconUrl,
                    imageSize: new AMap.Size(40, 40)
                })
            });
            
            marker.setMap(this.map);
            this.markers.push(marker);
            
            this.createInfoWindow(marker, location, deviceStatus);
        });
        
        // 自适应视野
        this.fitMapView();
    }
    
    /**
     * 清除搜索
     */
    clearSearch() {
        document.getElementById('device-search').value = '';
        this.renderDeviceMarkers();
    }
    
     /**
     * 显示加载状态 - 修复版本
     */
    showLoading(show, message = '正在加载地图数据...') {
        const modal = document.getElementById('loadingModal');
        const messageElement = document.getElementById('loading-message');

        if (show) {
            // 显示模态框
            if (messageElement) {
                messageElement.textContent = message;
            }
            
            // 如果已有实例，先清理
            if (this.loadingModalInstance) {
                try {
                    this.loadingModalInstance.hide();
                } catch (e) {
                    console.warn('清理旧实例失败:', e);
                }
            }
            
            // 创建新实例并保存引用
            this.loadingModalInstance = new bootstrap.Modal(modal);
            this.loadingModalInstance.show();
            
        } else {
            // 隐藏模态框
            if (this.loadingModalInstance) {
                try {
                    this.loadingModalInstance.hide();
                    // 强制隐藏
                    this.forceHideModal(modal);
                } catch (e) {
                    console.error('隐藏模态框失败:', e);
                    // 强制隐藏
                    this.forceHideModal(modal);
                }
            } else {
                // 尝试获取现有实例
                const existingInstance = bootstrap.Modal.getInstance(modal);
                if (existingInstance) {
                    existingInstance.hide();
                } else {
                    // 如果都没有，强制隐藏
                    this.forceHideModal(modal);
                }
            }
        }
    }
    
    /**
     * 强制隐藏模态框
     */
    forceHideModal(modal) {
        // 移除模态框相关的类和样式
        modal.classList.remove('show');
        modal.style.display = 'none';
        modal.setAttribute('aria-hidden', 'true');
        
        // 移除backdrop
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => backdrop.remove());
        
        // 恢复body样式
        document.body.classList.remove('modal-open');
        document.body.style.removeProperty('padding-right');
        document.body.style.removeProperty('overflow');
        
        // 清理实例引用
        this.loadingModalInstance = null;
    }

    /**
     * 显示信息提示
     */
    showInfo(message) {
        console.info(message);
        // 可以在这里添加更友好的提示方式
        const toast = document.createElement('div');
        toast.className = 'toast align-items-center text-white bg-info border-0';
        toast.style.position = 'fixed';
        toast.style.top = '20px';
        toast.style.right = '20px';
        toast.style.zIndex = '9999';
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        document.body.appendChild(toast);
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();

        // 3秒后自动移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    }
    
    /**
     * 显示错误信息
     */
    showError(message) {
        console.error(message);
        alert('错误: ' + message);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    window.deviceMapManager = new DeviceMapManager();
});
</script>
{% endblock %}
