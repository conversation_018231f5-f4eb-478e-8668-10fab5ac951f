{% extends "base.html" %}

{% block title %}{{ '编辑设备' if device else '添加设备' }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2>{{ '编辑设备' if device else '添加设备' }}</h2>
    
    <div class="card mt-4">
        <div class="card-body">
            <form method="POST">
                <div class="mb-3">
                    <label for="device_id" class="form-label">设备ID</label>
                    <input type="text" class="form-control" id="device_id" name="device_id" value="{{ device.device_id if device else '' }}" required>
                    <div class="form-text">设备的唯一标识符</div>
                </div>
                
                <div class="mb-3">
                    <label for="device_remark" class="form-label">设备备注</label>
                    <textarea class="form-control" id="device_remark" name="device_remark" rows="3">{{ device.device_remark if device else '' }}</textarea>
                    <div class="form-text">设备的备注信息</div>
                </div>
                
                <div class="mb-3">
                    <label for="product_key" class="form-label">产品密钥</label>
                    <input type="text" class="form-control" id="product_key" name="product_key" value="{{ device.product_key if device else '' }}" required>
                    <div class="form-text">设备的产品密钥</div>
                </div>

                <div class="mb-3">
                    <label for="device_type" class="form-label">设备类型</label>
                    <select class="form-select" id="device_type" name="device_type">
                        <option value="">请选择设备类型</option>
                        <option value="10" {% if device and device.device_type == 10 %}selected{% endif %}>V2 (旧版霍尔传感器版本，黑色PCB)</option>
                        <option value="50" {% if device and device.device_type == 50 %}selected{% endif %}>V5 (新版BL0910 10通道版本)</option>
                        <option value="51" {% if device and device.device_type == 51 %}selected{% endif %}>V51 (新版BL0939 2通道版本)</option>
                    </select>
                    <div class="form-text">可选，用于批量升级时的固件匹配</div>
                </div>

                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">保存</button>
                    <a href="{{ url_for('device.devices') }}" class="btn btn-secondary">返回</a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %} 