{% extends "base.html" %}

{% block title %}错误 - OTA设备管理系统{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>系统错误
                    </h4>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <i class="fas fa-exclamation-circle text-danger" style="font-size: 4rem;"></i>
                    </div>
                    
                    <h5 class="text-center mb-3">抱歉，系统遇到了一个错误</h5>
                    
                    {% if error_message %}
                    <div class="alert alert-danger" role="alert">
                        <strong>错误详情：</strong>
                        <p class="mb-0">{{ error_message }}</p>
                    </div>
                    {% endif %}
                    
                    <div class="text-center mt-4">
                        <a href="{{ url_for('main.index') }}" class="btn btn-primary me-2">
                            <i class="fas fa-home me-1"></i>返回首页
                        </a>
                        <button onclick="history.back()" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>返回上页
                        </button>
                    </div>
                    
                    <hr class="my-4">
                    
                    <div class="text-muted small">
                        <p><strong>可能的解决方案：</strong></p>
                        <ul>
                            <li>刷新页面重试</li>
                            <li>检查网络连接</li>
                            <li>联系系统管理员</li>
                        </ul>
                        
                        <p class="mt-3">
                            <strong>时间：</strong>{{ moment().format('YYYY-MM-DD HH:mm:ss') if moment else '' }}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
