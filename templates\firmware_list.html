{% extends "base.html" %}

{% block title %}固件管理{% endblock %}

{% block styles %}
<style>
    /* 固件列表页面优化样式 */
    .firmware-table-container {
        min-width: 1000px; /* 确保表格有足够的宽度 */
    }

    .firmware-table th,
    .firmware-table td {
        white-space: nowrap; /* 防止内容换行 */
        vertical-align: middle;
        padding: 0.75rem 0.5rem;
    }

    /* 列宽优化 */
    .firmware-table th:nth-child(1), .firmware-table td:nth-child(1) { /* 名称 */
        width: 300px;
        min-width: 500px;
        white-space: normal; /* 名称可以换行 */
        word-break: break-word;
    }

    .firmware-table th:nth-child(2), .firmware-table td:nth-child(2) { /* 版本 */
        width: 100px;
        min-width: 100px;
        text-align: center;
    }

    .firmware-table th:nth-child(3), .firmware-table td:nth-child(3) { /* 大小 */
        width: 100px;
        min-width: 100px;
        text-align: right;
    }

    .firmware-table th:nth-child(4), .firmware-table td:nth-child(4) { /* CRC32 */
        width: 120px;
        min-width: 120px;
        font-family: 'Courier New', monospace;
        font-size: 0.85rem;
    }

    .firmware-table th:nth-child(5), .firmware-table td:nth-child(5) { /* 上传时间 */
        width: 150px;
        min-width: 150px;
        text-align: center;
    }

    .firmware-table th:nth-child(6), .firmware-table td:nth-child(6) { /* 下载次数 */
        width: 100px;
        min-width: 100px;
        text-align: center;
        font-weight: 600;
    }

    .firmware-table th:nth-child(7), .firmware-table td:nth-child(7) { /* 操作 */
        width: 150px;
        min-width: 150px;
        text-align: center;
    }

    /* 下载次数样式优化 */
    .download-count {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        color: #1976d2;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.85rem;
        font-weight: 600;
        min-width: 40px;
    }

    /* 操作按钮组优化 */
    .firmware-actions {
        display: flex;
        gap: 4px;
        justify-content: center;
        flex-wrap: nowrap;
    }

    .firmware-actions .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        border-radius: 4px;
        min-width: 60px;
    }

    /* 响应式优化 */
    @media (max-width: 1200px) {
        .firmware-table th:nth-child(4), .firmware-table td:nth-child(4) { /* 隐藏CRC32列 */
            display: none;
        }
    }

    @media (max-width: 992px) {
        .firmware-table th:nth-child(5), .firmware-table td:nth-child(5) { /* 隐藏上传时间列 */
            display: none;
        }
    }

    @media (max-width: 768px) {
        .firmware-actions {
            flex-direction: column;
            gap: 2px;
        }

        .firmware-actions .btn {
            min-width: auto;
            padding: 0.2rem 0.4rem;
            font-size: 0.8rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">固件管理</h2>
        <a href="{{ url_for('firmware.latest_firmware_management') }}" class="btn btn-outline-warning">
            <i class="fas fa-star me-1"></i>最新固件管理
        </a>
    </div>
    
    <!-- 上传固件表单 -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">上传新固件</h5>
        </div>
        <div class="card-body">
            <form id="uploadForm" enctype="multipart/form-data">
                <div class="row">
                    <div class="col-md-12">
                        <div class="mb-3">
                            <label for="firmware_name" class="form-label">固件名称</label>
                            <input type="text" class="form-control" id="firmware_name" name="firmware_name" required>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="mb-3">
                            <label for="firmware_file" class="form-label">固件文件</label>
                            <input type="file" class="form-control" id="firmware_file" name="firmware_file" required accept=".bin">
                            <div class="form-text">支持.bin格式的固件文件，系统将自动从固件中读取版本信息</div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="device_type" class="form-label">设备类型 <span class="text-danger">*</span></label>
                            <select class="form-select" id="device_type" name="device_type" required>
                                <option value="">请选择设备类型</option>
                                <option value="10">V2 (旧版霍尔传感器版本，黑色PCB)</option>
                                <option value="50">V5 (新版BL0910 10通道版本)</option>
                                <option value="51">V51 (新版BL0939 2通道版本)</option>
                            </select>
                            <div class="form-text">请选择此固件适用的设备类型</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="firmware_description" class="form-label">描述</label>
                            <textarea class="form-control" id="firmware_description" name="firmware_description" rows="2"></textarea>
                        </div>
                    </div>
                </div>
                <button type="submit" class="btn btn-primary" id="uploadBtn">
                    <i class="fas fa-upload me-1"></i>上传固件
                </button>
                <div class="mt-3">
                    <div class="progress" id="uploadProgress" style="display: none;">
                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                    <div id="uploadStatus" class="mt-2"></div>
                </div>
            </form>
        </div>
    </div>

    <!-- 固件列表 -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">固件列表</h5>
        </div>
        <div class="card-body p-0">
            <!-- 响应式表格容器 -->
            <div class="table-responsive firmware-table-container">
                <style>
                    /* 固件表格容器样式 */
                    .firmware-table-container {
                        position: relative;
                        overflow-x: auto;
                        overflow-y: hidden;
                        border-radius: 0;
                        /* 自定义滚动条样式 */
                        scrollbar-width: thin;
                        scrollbar-color: #ccc transparent;
                    }

                    .firmware-table-container::-webkit-scrollbar {
                        height: 8px;
                    }

                    .firmware-table-container::-webkit-scrollbar-track {
                        background: #f1f1f1;
                        border-radius: 4px;
                    }

                    .firmware-table-container::-webkit-scrollbar-thumb {
                        background: #c1c1c1;
                        border-radius: 4px;
                    }

                    .firmware-table-container::-webkit-scrollbar-thumb:hover {
                        background: #a8a8a8;
                    }

                    /* 固件表格样式 */
                    .firmware-table {
                        margin-bottom: 0;
                        min-width: 900px; /* 确保表格有最小宽度 */
                    }

                    .firmware-table th,
                    .firmware-table td {
                        white-space: nowrap;
                        vertical-align: middle;
                        padding: 0.75rem;
                        border-bottom: 1px solid #dee2e6;
                    }

                    /* 表头固定样式 */
                    .firmware-table thead th {
                        position: sticky;
                        top: 0;
                        background-color: #f8f9fa;
                        z-index: 10;
                        box-shadow: 0 2px 2px -1px rgba(0, 0, 0, 0.1);
                    }

                    /* 暗黑模式下的表头 */
                    body.dark-mode .firmware-table thead th {
                        background-color: #2d3748;
                        color: #e2e8f0;
                    }

                    /* 暗黑模式下的滚动条样式 */
                    body.dark-mode .firmware-table-container::-webkit-scrollbar-track {
                        background: #2d3748;
                    }

                    body.dark-mode .firmware-table-container::-webkit-scrollbar-thumb {
                        background: #4a5568;
                    }

                    body.dark-mode .firmware-table-container::-webkit-scrollbar-thumb:hover {
                        background: #718096;
                    }

                    /* 响应式优化 */
                    @media (max-width: 1200px) {
                        .firmware-table {
                            min-width: 800px;
                        }
                    }

                    @media (max-width: 992px) {
                        .firmware-table {
                            min-width: 750px;
                            font-size: 0.9rem;
                        }

                        .firmware-table th,
                        .firmware-table td {
                            padding: 0.6rem;
                        }
                    }

                    @media (max-width: 768px) {
                        .firmware-table {
                            min-width: 700px;
                            font-size: 0.85rem;
                        }

                        .firmware-table th,
                        .firmware-table td {
                            padding: 0.5rem;
                        }

                        /* 隐藏不重要的列 */
                        .firmware-table .hide-mobile {
                            display: none;
                        }
                    }

                    @media (max-width: 576px) {
                        .firmware-table {
                            min-width: 600px;
                            font-size: 0.8rem;
                        }

                        .firmware-table th,
                        .firmware-table td {
                            padding: 0.4rem 0.3rem;
                        }
                    }

                    /* 表格行悬停效果优化 */
                    .firmware-table tbody tr:hover {
                        background-color: rgba(0, 123, 255, 0.1);
                        transform: translateY(-1px);
                        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                        transition: all 0.2s ease;
                    }

                    body.dark-mode .firmware-table tbody tr:hover {
                        background-color: rgba(66, 153, 225, 0.2);
                    }
                </style>

                <table class="table table-striped table-hover firmware-table">
                    <thead class="table-light">
                        <tr>
                            <th><i class="fas fa-file-code me-1"></i>名称</th>
                            <th><i class="fas fa-tag me-1"></i>版本</th>
                            <th><i class="fas fa-microchip me-1"></i>设备类型</th>
                            <th><i class="fas fa-weight me-1"></i>大小</th>
                            <th><i class="fas fa-fingerprint me-1"></i>CRC32</th>
                            <th><i class="fas fa-clock me-1"></i>上传时间</th>
                            <th><i class="fas fa-download me-1"></i>下载次数</th>
                            <th><i class="fas fa-cogs me-1"></i>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for firmware in firmwares %}
                        <tr>
                            <td class="align-middle">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-microchip text-primary me-2"></i>
                                    <span title="{{ firmware.name }}">{{ firmware.name }}</span>
                                </div>
                            </td>
                            <td class="align-middle">
                                <span class="badge bg-primary">v{{ firmware.version }}</span>
                            </td>
                            <td class="align-middle">
                                {% if firmware.device_type_name %}
                                <span class="badge bg-primary-subtle text-primary">
                                    <i class="fas fa-microchip me-1"></i>{{ firmware.device_type_name }}
                                </span>
                                {% else %}
                                <span class="badge bg-secondary">未设置</span>
                                {% endif %}
                            </td>
                            <td class="align-middle">
                                <span class="text-muted">{{ (firmware.size / 1024)|round(2) }} KB</span>
                            </td>
                            <td class="align-middle">
                                <code class="text-muted">{{ firmware.crc32 }}</code>
                            </td>
                            <td class="align-middle">
                                <small class="text-muted">{{ firmware.upload_time.strftime('%Y-%m-%d %H:%M:%S') }}</small>
                            </td>
                            <td class="align-middle">
                                <span class="download-count">
                                    <i class="fas fa-download me-1"></i>{{ firmware.download_count }}
                                </span>
                            </td>
                            <td class="align-middle">
                                <div class="firmware-actions">
                                    <a href="{{ url_for('firmware.download_firmware', id=firmware.id) }}"
                                       class="btn btn-sm btn-primary" title="下载固件">
                                        <i class="fas fa-download"></i> 下载
                                    </a>
                                    <a href="{{ url_for('firmware.delete_firmware', id=firmware.id) }}"
                                       class="btn btn-sm btn-danger" title="删除固件"
                                       onclick="return confirm('确定要删除这个固件吗？')">
                                        <i class="fas fa-trash"></i> 删除
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('uploadForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const form = this;
    const formData = new FormData(form);
    const uploadBtn = document.getElementById('uploadBtn');
    const uploadProgress = document.getElementById('uploadProgress');
    const uploadStatus = document.getElementById('uploadStatus');
    const progressBar = uploadProgress.querySelector('.progress-bar');

    // 验证表单
    if (!form.firmware_name.value || !form.firmware_file.files[0] || !form.device_type.value) {
        showUploadMessage('请填写所有必填字段', 'error');
        return;
    }

    // 显示上传状态
    uploadBtn.disabled = true;
    uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>上传中...';
    uploadProgress.style.display = 'block';
    progressBar.style.width = '0%';
    uploadStatus.innerHTML = '';

    // 创建XMLHttpRequest以支持进度显示
    const xhr = new XMLHttpRequest();

    // 上传进度
    xhr.upload.addEventListener('progress', function(e) {
        if (e.lengthComputable) {
            const percentComplete = (e.loaded / e.total) * 100;
            progressBar.style.width = percentComplete + '%';
            progressBar.textContent = Math.round(percentComplete) + '%';
        }
    });

    // 上传完成
    xhr.addEventListener('load', function() {
        uploadBtn.disabled = false;
        uploadBtn.innerHTML = '<i class="fas fa-upload me-1"></i>上传固件';

        try {
            const response = JSON.parse(xhr.responseText);
            if (response.success) {
                showUploadMessage('固件上传成功！', 'success');
                form.reset();
                uploadProgress.style.display = 'none';

                // 3秒后刷新页面
                setTimeout(() => {
                    window.location.reload();
                }, 3000);
            } else {
                showUploadMessage('上传失败: ' + response.message, 'error');
                uploadProgress.style.display = 'none';
            }
        } catch (e) {
            showUploadMessage('上传失败: 服务器响应格式错误', 'error');
            uploadProgress.style.display = 'none';
        }
    });

    // 上传错误
    xhr.addEventListener('error', function() {
        uploadBtn.disabled = false;
        uploadBtn.innerHTML = '<i class="fas fa-upload me-1"></i>上传固件';
        showUploadMessage('上传失败: 网络错误', 'error');
        uploadProgress.style.display = 'none';
    });

    // 发送请求
    xhr.open('POST', '{{ url_for("firmware.upload_firmware") }}');
    xhr.send(formData);
});

function showUploadMessage(message, type) {
    const uploadStatus = document.getElementById('uploadStatus');
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';

    uploadStatus.innerHTML = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
}
</script>
{% endblock %}