{% extends "base.html" %}

{% block title %}固件管理 - OTA设备管理系统{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="mb-0"><i class="fas fa-file-code text-primary me-2"></i>固件管理</h2>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadFirmwareModal">
                    <i class="fas fa-upload me-1"></i>上传固件
                </button>
            </div>
        </div>
    </div>

    <!-- 固件列表 -->
    <div class="card shadow-sm">
        <div class="card-header bg-white py-3">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0"><i class="fas fa-list text-primary"></i> 固件列表</h5>
                </div>
                <div class="col-auto">
                    <div class="input-group">
                        <input type="text" class="form-control" id="searchInput" placeholder="搜索固件...">
                        <button class="btn btn-outline-secondary" type="button">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>固件名称</th>
                            <th>版本</th>
                            <th>大小</th>
                            <th>上传时间</th>
                            <th class="text-end">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for firmware in firmwares %}
                        <tr>
                            <td class="align-middle">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-file-code text-primary me-2"></i>
                                    {{ firmware.name }}
                                </div>
                            </td>
                            <td class="align-middle">
                                <span class="badge bg-primary-subtle text-primary">
                                    v{{ firmware.version }}
                                </span>
                            </td>
                            <td class="align-middle">
                                <span class="text-muted">
                                    {{ firmware.size|filesizeformat }}
                                </span>
                            </td>
                            <td class="align-middle">
                                <span class="text-muted">
                                    <i class="far fa-clock me-1"></i>
                                    {{ firmware.upload_time.strftime('%Y-%m-%d %H:%M:%S') }}
                                </span>
                            </td>
                            <td class="align-middle text-end">
                                <div class="btn-group">
                                    <a href="{{ url_for('firmware.download_firmware', id=firmware.id) }}" class="btn btn-sm btn-info" title="下载">
                                        <i class="fas fa-download"></i>
                                    </a>
                                    <a href="{{ url_for('firmware.delete_firmware', id=firmware.id) }}" class="btn btn-sm btn-danger" onclick="return confirm('确定要删除此固件吗？')" title="删除">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- 上传固件模态框 -->
<div class="modal fade" id="uploadFirmwareModal" tabindex="-1" aria-labelledby="uploadFirmwareModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="uploadFirmwareModalLabel">
                    <i class="fas fa-upload text-primary me-2"></i>上传固件
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('firmware.upload_firmware') }}" method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="firmware_name" class="form-label">固件名称</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-file-code"></i></span>
                            <input type="text" class="form-control" id="firmware_name" name="firmware_name" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="firmware_version" class="form-label">固件版本</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-code-branch"></i></span>
                            <input type="text" class="form-control" id="firmware_version" name="firmware_version" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="firmware_file" class="form-label">固件文件</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-file"></i></span>
                            <input type="file" class="form-control" id="firmware_file" name="firmware_file" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="firmware_description" class="form-label">固件描述</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-info-circle"></i></span>
                            <textarea class="form-control" id="firmware_description" name="firmware_description" rows="3"></textarea>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>取消
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload me-1"></i>上传
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 搜索功能
document.getElementById('searchInput').addEventListener('keyup', function() {
    var input = this.value.toLowerCase();
    var rows = document.querySelectorAll('tbody tr');
    
    rows.forEach(function(row) {
        var firmwareName = row.cells[0].textContent.toLowerCase();
        var firmwareVersion = row.cells[1].textContent.toLowerCase();
        if (firmwareName.includes(input) || firmwareVersion.includes(input)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
});

// 确保Bootstrap模态框正常工作
document.addEventListener('DOMContentLoaded', function() {
    // 初始化所有模态框
    var uploadModalEl = document.getElementById('uploadFirmwareModal');
    var uploadModal = new bootstrap.Modal(uploadModalEl);
});
</script>
{% endblock %} 