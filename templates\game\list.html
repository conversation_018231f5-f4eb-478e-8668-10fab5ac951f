{% extends "base.html" %}

{% block title %}小游戏中心{% endblock %}

{% block content %}
<div class="container py-4">
    <h1 class="mb-4">小游戏中心</h1>
    <div class="row">
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <h5 class="card-title">贪吃蛇</h5>
                    <p class="card-text">经典的贪吃蛇游戏，使用方向键控制蛇的移动，吃到食物可以增加长度和分数。</p>
                    <a href="{{ url_for('game.snake_game') }}" class="btn btn-primary">开始游戏</a>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <h5 class="card-title">3D贪吃蛇</h5>
                    <p class="card-text">炫酷的3D版贪吃蛇，支持WASD和方向键控制，鼠标可以自由旋转视角，带来全新的游戏体验！</p>
                    <a href="{{ url_for('game.snake3d_game') }}" class="btn btn-primary">开始游戏</a>
                </div>
            </div>
        </div>
        <!-- 可以在这里添加更多游戏卡片 -->
    </div>
</div>
{% endblock %} 