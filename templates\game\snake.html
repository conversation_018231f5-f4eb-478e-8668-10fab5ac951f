{% extends "base.html" %}

{% block title %}贪吃蛇游戏{% endblock %}

{% block head %}
<style>
    :root {
        --primary-color: #4CAF50;
        --secondary-color: #2196F3;
        --danger-color: #f44336;
        --background-color: #f8f9fa;
        --text-color: #333;
        --border-color: #ddd;
    }

    #gameCanvas {
        border: 2px solid var(--border-color);
        background: var(--background-color);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        border-radius: 12px;
        transition: all 0.3s ease;
    }

    .game-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20px;
        max-width: 1000px;
        margin: 0 auto;
    }

    .game-info {
        margin: 20px 0;
        text-align: center;
        background: white;
        padding: 20px;
        border-radius: 12px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        width: 100%;
        transition: all 0.3s ease;
    }

    .game-controls {
        margin: 20px 0;
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
        justify-content: center;
    }

    .score-board {
        display: flex;
        justify-content: space-around;
        margin-bottom: 20px;
    }

    .score-item {
        text-align: center;
        padding: 15px 25px;
        background: var(--background-color);
        border-radius: 12px;
        min-width: 150px;
        transition: all 0.3s ease;
    }

    .score-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .game-status {
        font-weight: bold;
        color: var(--primary-color);
        margin: 10px 0;
        transition: color 0.3s ease;
    }

    .controls-info {
        margin-top: 20px;
        padding: 20px;
        background: var(--background-color);
        border-radius: 12px;
        font-size: 0.9em;
        transition: all 0.3s ease;
    }

    .difficulty-select {
        margin: 10px 0;
        padding: 8px 15px;
        border-radius: 8px;
        border: 1px solid var(--border-color);
        background: white;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .difficulty-select:hover {
        border-color: var(--primary-color);
    }

    .btn-game {
        min-width: 120px;
        transition: all 0.3s ease;
        border-radius: 8px;
        padding: 10px 20px;
    }

    .btn-game:disabled {
        opacity: 0.6;
        cursor: not-allowed;
    }

    .btn-game:not(:disabled):hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    @keyframes foodPulse {
        0% { transform: scale(1); opacity: 1; }
        50% { transform: scale(1.2); opacity: 0.8; }
        100% { transform: scale(1); opacity: 1; }
    }

    @keyframes snakeMove {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    .modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.5);
        z-index: 1000;
    }

    .modal-content {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: white;
        padding: 20px;
        border-radius: 12px;
        text-align: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="game-container">
    <h1 class="mb-4">贪吃蛇游戏</h1>
    
    <div class="game-info">
        <div class="score-board">
            <div class="score-item">
                <h4>当前分数</h4>
                <span id="score" class="h3">0</span>
            </div>
            <div class="score-item">
                <h4>最高分数</h4>
                <span id="highScore" class="h3">0</span>
            </div>
        </div>
        
        <div class="game-status" id="gameStatus">准备开始</div>
        
        <select class="difficulty-select" id="difficultySelect">
            <option value="easy">简单 (慢速)</option>
            <option value="medium" selected>中等</option>
            <option value="hard">困难 (快速)</option>
        </select>
    </div>
    
    <canvas id="gameCanvas" width="800" height="600"></canvas>
    
    <div class="game-controls">
        <button class="btn btn-primary btn-game" id="startBtn" onclick="startGame()">
            <i class="fas fa-play"></i> 开始游戏
        </button>
        <button class="btn btn-secondary btn-game" id="pauseBtn" onclick="pauseGame()" disabled>
            <i class="fas fa-pause"></i> 暂停
        </button>
        <button class="btn btn-danger btn-game" id="resetBtn" onclick="confirmReset()" disabled>
            <i class="fas fa-redo"></i> 重新开始
        </button>
    </div>
    
    <div class="controls-info">
        <h5><i class="fas fa-keyboard"></i> 控制方式：</h5>
        <p class="mb-2">↑ ↓ ← → 方向键或 WASD 键控制移动</p>
        <p class="mb-2">空格键 - 暂停/继续</p>
        <p class="mb-0">ESC键 - 重新开始</p>
    </div>
</div>

<div id="confirmModal" class="modal">
    <div class="modal-content">
        <h4>确认重新开始？</h4>
        <p>当前游戏进度将丢失</p>
        <div class="mt-3">
            <button class="btn btn-secondary me-2" onclick="closeModal()">取消</button>
            <button class="btn btn-danger" onclick="resetGame()">确认</button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 游戏常量配置（Game Constants）
const CANVAS_WIDTH = 800;
const CANVAS_HEIGHT = 600;
const GRID_SIZE = 20;
const INITIAL_SPEED = {
    easy: 150,
    medium: 100,
    hard: 60
};

// 游戏实体（Game Entities）
const canvas = document.getElementById('gameCanvas');
const ctx = canvas.getContext('2d');
const tileCount = {
    x: CANVAS_WIDTH / GRID_SIZE,
    y: CANVAS_HEIGHT / GRID_SIZE
};

// 游戏状态（Game State）
const gameState = {
    snake: [{x: 10, y: 10}],  // 蛇的身体段落（Snake Segments）
    food: {x: 15, y: 15},     // 食物位置（Food Position）
    direction: {x: 1, y: 0},  // 移动方向（Movement Direction）
    score: 0,                 // 当前分数（Current Score）
    highScore: localStorage.getItem('snakeHighScore') || 0,  // 最高分（High Score）
    isRunning: false,        // 游戏运行状态（Game Running State）
    isPaused: false,         // 暂停状态（Pause State）
    gameLoop: null,          // 游戏循环（Game Loop）
    difficulty: 'medium'     // 游戏难度（Game Difficulty）
};

// 游戏控制器（Game Controller）
const controller = {
    keyMap: {
        'ArrowUp': {x: 0, y: -1},
        'ArrowDown': {x: 0, y: 1},
        'ArrowLeft': {x: -1, y: 0},
        'ArrowRight': {x: 1, y: 0},
        'w': {x: 0, y: -1},
        'W': {x: 0, y: -1},
        's': {x: 0, y: 1},
        'S': {x: 0, y: 1},
        'a': {x: -1, y: 0},
        'A': {x: -1, y: 0},
        'd': {x: 1, y: 0},
        'D': {x: 1, y: 0}
    },
    
    handleInput(key) {
        const newDirection = this.keyMap[key];
        if (newDirection) {
            // 防止180度转向
            if (!(gameState.direction.x + newDirection.x === 0 && 
                  gameState.direction.y + newDirection.y === 0)) {
                gameState.direction = newDirection;
            }
        }
    }
};

// 渲染器（Renderer）
const renderer = {
    clear() {
        ctx.fillStyle = '#f8f9fa';
        ctx.fillRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);
        this.drawGrid();
    },
    
    drawGrid() {
        ctx.strokeStyle = '#e9ecef';
        ctx.lineWidth = 0.5;
        
        // 绘制渐变背景
        const gradient = ctx.createLinearGradient(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);
        gradient.addColorStop(0, '#f8f9fa');
        gradient.addColorStop(1, '#e9ecef');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);
        
        // 绘制网格
        for (let i = 0; i < tileCount.x; i++) {
            ctx.beginPath();
            ctx.moveTo(i * GRID_SIZE, 0);
            ctx.lineTo(i * GRID_SIZE, CANVAS_HEIGHT);
            ctx.stroke();
        }
        for (let i = 0; i < tileCount.y; i++) {
            ctx.beginPath();
            ctx.moveTo(0, i * GRID_SIZE);
            ctx.lineTo(CANVAS_WIDTH, i * GRID_SIZE);
            ctx.stroke();
        }
    },
    
    drawFood() {
        const foodX = gameState.food.x * GRID_SIZE + GRID_SIZE/2;
        const foodY = gameState.food.y * GRID_SIZE + GRID_SIZE/2;
        
        // 创建食物渐变
        const gradient = ctx.createRadialGradient(
            foodX, foodY, 0,
            foodX, foodY, GRID_SIZE/2
        );
        gradient.addColorStop(0, '#ff6b6b');
        gradient.addColorStop(1, '#ff4757');
        
        ctx.fillStyle = gradient;
        ctx.shadowColor = 'rgba(255, 71, 87, 0.3)';
        ctx.shadowBlur = 10;
        
        ctx.beginPath();
        ctx.arc(foodX, foodY, GRID_SIZE/2 - 2, 0, Math.PI * 2);
        ctx.fill();
        
        // 添加光晕效果
        ctx.shadowColor = 'transparent';
        ctx.strokeStyle = '#fff';
        ctx.lineWidth = 2;
        ctx.stroke();
    },
    
    drawSnake() {
        gameState.snake.forEach((segment, index) => {
            const x = segment.x * GRID_SIZE;
            const y = segment.y * GRID_SIZE;
            
            // 创建蛇身渐变
            const gradient = ctx.createLinearGradient(x, y, x + GRID_SIZE, y + GRID_SIZE);
            if (index === 0) {
                gradient.addColorStop(0, '#4CAF50');
                gradient.addColorStop(1, '#45a049');
            } else {
                gradient.addColorStop(0, '#66bb6a');
                gradient.addColorStop(1, '#4CAF50');
            }
            
            ctx.fillStyle = gradient;
            ctx.shadowColor = 'rgba(0, 0, 0, 0.2)';
            ctx.shadowBlur = 5;
            
            // 绘制圆角矩形
            const radius = GRID_SIZE/2 - 2;
            ctx.beginPath();
            ctx.moveTo(x + radius, y);
            ctx.lineTo(x + GRID_SIZE - radius, y);
            ctx.quadraticCurveTo(x + GRID_SIZE, y, x + GRID_SIZE, y + radius);
            ctx.lineTo(x + GRID_SIZE, y + GRID_SIZE - radius);
            ctx.quadraticCurveTo(x + GRID_SIZE, y + GRID_SIZE, x + GRID_SIZE - radius, y + GRID_SIZE);
            ctx.lineTo(x + radius, y + GRID_SIZE);
            ctx.quadraticCurveTo(x, y + GRID_SIZE, x, y + GRID_SIZE - radius);
            ctx.lineTo(x, y + radius);
            ctx.quadraticCurveTo(x, y, x + radius, y);
            ctx.closePath();
            ctx.fill();
            
            // 添加眼睛（仅对蛇头）
            if (index === 0) {
                ctx.fillStyle = '#fff';
                const eyeSize = GRID_SIZE/6;
                const eyeOffset = GRID_SIZE/4;
                
                // 根据方向调整眼睛位置
                let leftEyeX, leftEyeY, rightEyeX, rightEyeY;
                
                if (gameState.direction.x === 1) { // 向右
                    leftEyeX = x + GRID_SIZE - eyeOffset;
                    leftEyeY = y + eyeOffset;
                    rightEyeX = x + GRID_SIZE - eyeOffset;
                    rightEyeY = y + GRID_SIZE - eyeOffset;
                } else if (gameState.direction.x === -1) { // 向左
                    leftEyeX = x + eyeOffset;
                    leftEyeY = y + eyeOffset;
                    rightEyeX = x + eyeOffset;
                    rightEyeY = y + GRID_SIZE - eyeOffset;
                } else if (gameState.direction.y === -1) { // 向上
                    leftEyeX = x + eyeOffset;
                    leftEyeY = y + eyeOffset;
                    rightEyeX = x + GRID_SIZE - eyeOffset;
                    rightEyeY = y + eyeOffset;
                } else { // 向下
                    leftEyeX = x + eyeOffset;
                    leftEyeY = y + GRID_SIZE - eyeOffset;
                    rightEyeX = x + GRID_SIZE - eyeOffset;
                    rightEyeY = y + GRID_SIZE - eyeOffset;
                }
                
                ctx.beginPath();
                ctx.arc(leftEyeX, leftEyeY, eyeSize, 0, Math.PI * 2);
                ctx.arc(rightEyeX, rightEyeY, eyeSize, 0, Math.PI * 2);
                ctx.fill();
                
                // 添加瞳孔
                ctx.fillStyle = '#000';
                ctx.beginPath();
                ctx.arc(leftEyeX, leftEyeY, eyeSize/2, 0, Math.PI * 2);
                ctx.arc(rightEyeX, rightEyeY, eyeSize/2, 0, Math.PI * 2);
                ctx.fill();
            }
        });
        
        ctx.shadowColor = 'transparent';
    },
    
    render() {
        this.clear();
        this.drawFood();
        this.drawSnake();
    }
};

// 游戏逻辑（Game Logic）
function updateGame() {
    if (!gameState.isRunning || gameState.isPaused) return;
    
    const head = {
        x: gameState.snake[0].x + gameState.direction.x,
        y: gameState.snake[0].y + gameState.direction.y
    };
    
    // 碰撞检测（Collision Detection）
    if (isCollision(head)) {
        gameOver();
        return;
    }
    
    gameState.snake.unshift(head);
    
    // 食物收集（Food Collection）
    if (head.x === gameState.food.x && head.y === gameState.food.y) {
        collectFood();
    } else {
        gameState.snake.pop();
    }
}

function isCollision(position) {
    // 边界碰撞（Boundary Collision）
    if (position.x < 0 || position.x >= tileCount.x ||
        position.y < 0 || position.y >= tileCount.y) {
        return true;
    }
    
    // 自身碰撞（Self Collision）
    return gameState.snake.some(segment =>
        segment.x === position.x && segment.y === position.y
    );
}

function collectFood() {
    gameState.score += 10;
    document.getElementById('score').textContent = gameState.score;
    
    if (gameState.score > gameState.highScore) {
        gameState.highScore = gameState.score;
        localStorage.setItem('snakeHighScore', gameState.highScore);
        document.getElementById('highScore').textContent = gameState.highScore;
    }
    
    generateFood();
    playEatSound();
    saveScore(gameState.score);
}

function generateFood() {
    gameState.food = {
        x: Math.floor(Math.random() * tileCount.x),
        y: Math.floor(Math.random() * tileCount.y)
    };
    
    // 确保食物不会生成在蛇身上
    gameState.snake.forEach(segment => {
        if (gameState.food.x === segment.x && gameState.food.y === segment.y) {
            generateFood();
        }
    });
}

function gameOver() {
    clearInterval(gameState.gameLoop);
    gameState.isRunning = false;
    updateGameStatus('游戏结束');
    
    // 显示游戏结束对话框
    setTimeout(() => {
        if (confirm(`游戏结束！\n得分：${gameState.score}\n最高分：${gameState.highScore}\n\n是否重新开始？`)) {
            startGame();
        }
    }, 100);
}

function resetGame() {
    closeModal();
    gameState.snake = [{x: 10, y: 10}];
    gameState.food = {x: 15, y: 15};
    gameState.direction = {x: 1, y: 0};
    gameState.score = 0;
    document.getElementById('score').textContent = gameState.score;
    gameState.isRunning = false;
    gameState.isPaused = false;
    updateGameStatus('准备开始');
    updateButtonStates();
    document.getElementById('pauseBtn').innerHTML = '<i class="fas fa-pause"></i> 暂停';
    if (gameState.gameLoop) {
        clearInterval(gameState.gameLoop);
    }
    renderer.render();
}

function startGame() {
    if (gameState.gameLoop) {
        clearInterval(gameState.gameLoop);
    }
    resetGame();
    gameState.isRunning = true;
    updateGameStatus('游戏进行中');
    updateButtonStates();
    
    const difficulty = document.getElementById('difficultySelect').value;
    const speed = INITIAL_SPEED[difficulty];
    
    gameState.gameLoop = setInterval(() => {
        if (!gameState.isPaused) {
            updateGame();
            renderer.render();
        }
    }, speed);
}

function pauseGame() {
    if (!gameState.isRunning) return;
    
    gameState.isPaused = !gameState.isPaused;
    const pauseBtn = document.getElementById('pauseBtn');
    if (gameState.isPaused) {
        updateGameStatus('已暂停');
        pauseBtn.innerHTML = '<i class="fas fa-play"></i> 继续';
    } else {
        updateGameStatus('游戏进行中');
        pauseBtn.innerHTML = '<i class="fas fa-pause"></i> 暂停';
    }
}

function saveScore(score) {
    fetch('/api/game/snake/score', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ score: score })
    })
    .then(response => response.json())
    .then(data => {
        if (!data.success) {
            console.error('保存分数失败:', data.message);
        }
    })
    .catch(error => {
        console.error('保存分数出错:', error);
    });
}

// 事件监听器（Event Listeners）
document.addEventListener('keydown', (event) => {
    if (event.key === 'Escape') {
        confirmReset();
        return;
    }
    
    if (!gameState.isRunning && event.key !== 'Enter') return;
    
    switch(event.key) {
        case ' ':
            event.preventDefault();
            if (gameState.isRunning) {
                pauseGame();
            }
            break;
        case 'Enter':
            if (!gameState.isRunning) {
                startGame();
            }
            break;
        default:
            controller.handleInput(event.key);
    }
});

// 监听难度选择变化
document.getElementById('difficultySelect').addEventListener('change', function() {
    if (gameState.isRunning) {
        startGame();
    }
});

// 播放吃到食物的音效
function playEatSound() {
    // 创建音频上下文
    const audioCtx = new (window.AudioContext || window.webkitAudioContext)();
    const oscillator = audioCtx.createOscillator();
    const gainNode = audioCtx.createGain();
    
    oscillator.connect(gainNode);
    gainNode.connect(audioCtx.destination);
    
    oscillator.type = 'sine';
    oscillator.frequency.setValueAtTime(880, audioCtx.currentTime); // 播放频率
    gainNode.gain.setValueAtTime(0.3, audioCtx.currentTime);
    
    oscillator.start();
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioCtx.currentTime + 0.1);
    oscillator.stop(audioCtx.currentTime + 0.1);
}

function updateGameStatus(status) {
    const gameStatus = document.getElementById('gameStatus');
    gameStatus.textContent = status;
    
    // 更新状态颜色
    switch(status) {
        case '游戏进行中':
            gameStatus.style.color = '#28a745';
            break;
        case '已暂停':
            gameStatus.style.color = '#ffc107';
            break;
        case '游戏结束':
            gameStatus.style.color = '#dc3545';
            break;
        default:
            gameStatus.style.color = '#28a745';
    }
}

// 更新按钮状态
function updateButtonStates() {
    const startBtn = document.getElementById('startBtn');
    const pauseBtn = document.getElementById('pauseBtn');
    const resetBtn = document.getElementById('resetBtn');
    
    startBtn.disabled = gameState.isRunning;
    pauseBtn.disabled = !gameState.isRunning;
    resetBtn.disabled = !gameState.isRunning;
}

function confirmReset() {
    const modal = document.getElementById('confirmModal');
    modal.style.display = 'block';
}

function closeModal() {
    const modal = document.getElementById('confirmModal');
    modal.style.display = 'none';
}

// 初始化游戏
renderer.render();
updateGameStatus('准备开始');
updateButtonStates();
</script>
{% endblock %} 