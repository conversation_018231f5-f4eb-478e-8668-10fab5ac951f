<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>3D贪吃蛇</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/postprocessing/EffectComposer.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/postprocessing/RenderPass.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/postprocessing/ShaderPass.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/postprocessing/UnrealBloomPass.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/shaders/CopyShader.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/shaders/LuminosityHighPassShader.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tween.js/18.6.4/tween.umd.js"></script>
    <style>
        body {
            margin: 0;
            overflow: hidden;
            background: #000;
            font-family: 'Arial', sans-serif;
        }
        #game-container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        #score-board {
            position: absolute;
            top: 20px;
            left: 20px;
            color: #fff;
            font-size: 24px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            z-index: 100;
            background: rgba(0,0,0,0.5);
            padding: 10px 20px;
            border-radius: 10px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        #controls {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            color: #fff;
            text-align: center;
            z-index: 100;
            background: rgba(0,0,0,0.5);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .control-key {
            display: inline-block;
            margin: 5px;
            padding: 10px 20px;
            background: rgba(255,255,255,0.2);
            border-radius: 5px;
            border: 1px solid rgba(255,255,255,0.3);
            transition: all 0.3s;
        }
        .control-key:hover {
            background: rgba(255,255,255,0.3);
            transform: scale(1.05);
        }
        #start-screen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: #fff;
            z-index: 200;
            backdrop-filter: blur(5px);
        }
        #start-button {
            padding: 15px 30px;
            font-size: 24px;
            background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
            border: none;
            border-radius: 30px;
            color: white;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 4px 15px rgba(255,107,107,0.4);
        }
        #start-button:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(255,107,107,0.6);
        }
        #game-over-screen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            display: none;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: #fff;
            z-index: 200;
            backdrop-filter: blur(5px);
        }
        #restart-button {
            padding: 15px 30px;
            font-size: 24px;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            border: none;
            border-radius: 30px;
            color: white;
            cursor: pointer;
            transition: all 0.3s;
            box-shadow: 0 4px 15px rgba(76,175,80,0.4);
            margin-top: 20px;
        }
        #restart-button:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(76,175,80,0.6);
        }
        #level-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            color: #fff;
            font-size: 24px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            z-index: 100;
            background: rgba(0,0,0,0.5);
            padding: 10px 20px;
            border-radius: 10px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        #particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }
        .glow {
            animation: glow 2s ease-in-out infinite alternate;
        }
        @keyframes glow {
            from {
                box-shadow: 0 0 10px #fff, 0 0 20px #fff, 0 0 30px #e60073, 0 0 40px #e60073;
            }
            to {
                box-shadow: 0 0 20px #fff, 0 0 30px #ff4da6, 0 0 40px #ff4da6, 0 0 50px #ff4da6;
            }
        }
    </style>
</head>
<body>
    <div id="game-container">
        <div id="score-board">
            分数: <span id="score">0</span>
        </div>
        <div id="level-indicator">
            等级: <span id="level">1</span>
        </div>
        <div id="controls">
            <div class="control-key">WASD</div>
            <div class="control-key">方向键</div>
            <div class="control-key">鼠标控制视角</div>
        </div>
        <div id="start-screen">
            <h1 style="font-size: 48px; margin-bottom: 20px; text-shadow: 0 0 10px rgba(255,255,255,0.5);">3D贪吃蛇</h1>
            <p style="font-size: 20px; margin-bottom: 30px; text-align: center; max-width: 600px;">
                使用WASD或方向键控制蛇的移动，鼠标可以自由旋转视角。<br>
                吃到食物可以增加分数，每100分提升一个等级，速度会逐渐加快！
            </p>
            <button id="start-button">开始游戏</button>
        </div>
        <div id="game-over-screen">
            <h1 style="font-size: 48px; margin-bottom: 20px; text-shadow: 0 0 10px rgba(255,255,255,0.5);">游戏结束</h1>
            <p style="font-size: 24px; margin-bottom: 10px;">最终得分: <span id="final-score">0</span></p>
            <p style="font-size: 24px; margin-bottom: 30px;">最高等级: <span id="final-level">1</span></p>
            <button id="restart-button">重新开始</button>
        </div>
    </div>
    <div id="particles"></div>

    <script>
        let scene, camera, renderer, snake, food, score = 0, level = 1;
        const snakeBody = [];
        const gridSize = 20;
        const cellSize = 1;
        let direction = new THREE.Vector3(1, 0, 0);
        let gameStarted = false;
        let gameLoop;
        let moveSpeed = 200;
        let lastKeyPressTime = 0;
        const keyPressInterval = 100;
        let composer;
        let bloomPass;
        let particles;
        let particleSystem;
        let foodGlow = false;

        // 初始化场景
        function init() {
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x000000);
            scene.fog = new THREE.FogExp2(0x000000, 0.01);
            
            // 调整相机位置和视角
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.set(0, 20, 0); // 从上方俯视
            camera.lookAt(0, 0, 0);
            camera.up.set(0, 0, -1); // 设置相机的上方向，使WASD操作与视角一致

            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            document.getElementById('game-container').appendChild(renderer.domElement);

            // 添加后期处理效果
            composer = new THREE.EffectComposer(renderer);
            const renderPass = new THREE.RenderPass(scene, camera);
            composer.addPass(renderPass);

            // 添加Bloom效果
            bloomPass = new THREE.UnrealBloomPass(
                new THREE.Vector2(window.innerWidth, window.innerHeight),
                1.5, 0.4, 0.85
            );
            bloomPass.threshold = 0;
            bloomPass.strength = 1.5;
            bloomPass.radius = 0;
            composer.addPass(bloomPass);

            // 添加轨道控制器
            const controls = new THREE.OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;
            controls.dampingFactor = 0.05;
            controls.maxPolarAngle = Math.PI  - 0.1;
            // controls.minPolarAngle = Math.PI / 2 - 0.1; // 限制视角只能水平旋转
            controls.enableZoom = true;
            // controls.enablePan = false; // 禁用平移
            controls.target.set(0, 0, 0);

            // 添加环境光和平行光
            const ambientLight = new THREE.AmbientLight(0x404040, 0.5);
            scene.add(ambientLight);

            const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
            directionalLight.position.set(1, 1, 1);
            directionalLight.castShadow = true;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            scene.add(directionalLight);

            // 添加点光源
            const pointLight = new THREE.PointLight(0xff0000, 1, 100);
            pointLight.position.set(0, 5, 0);
            scene.add(pointLight);

            // 创建网格地面
            const gridHelper = new THREE.GridHelper(gridSize, gridSize);
            scene.add(gridHelper);

            // 创建粒子系统
            createParticleSystem();

            // 创建初始蛇身
            createSnake();
            createFood();

            // 添加事件监听
            window.addEventListener('resize', onWindowResize);
            document.addEventListener('keydown', handleKeyDown);
            document.getElementById('start-button').addEventListener('click', startGame);
            document.getElementById('restart-button').addEventListener('click', restartGame);
        }

        function createParticleSystem() {
            const particleCount = 1000;
            const particles = new THREE.BufferGeometry();
            const positions = new Float32Array(particleCount * 3);
            const colors = new Float32Array(particleCount * 3);

            for (let i = 0; i < particleCount; i++) {
                positions[i * 3] = (Math.random() - 0.5) * gridSize;
                positions[i * 3 + 1] = Math.random() * 10;
                positions[i * 3 + 2] = (Math.random() - 0.5) * gridSize;

                colors[i * 3] = Math.random();
                colors[i * 3 + 1] = Math.random();
                colors[i * 3 + 2] = Math.random();
            }

            particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));
            particles.setAttribute('color', new THREE.BufferAttribute(colors, 3));

            const particleMaterial = new THREE.PointsMaterial({
                size: 0.1,
                vertexColors: true,
                transparent: true,
                opacity: 0.6,
                blending: THREE.AdditiveBlending
            });

            particleSystem = new THREE.Points(particles, particleMaterial);
            scene.add(particleSystem);
        }

        function createSnake() {
            const geometry = new THREE.BoxGeometry(cellSize, cellSize, cellSize);
            const material = new THREE.MeshPhongMaterial({ 
                color: 0x00ff00,
                shininess: 100,
                specular: 0xffffff,
                emissive: 0x00ff00,
                emissiveIntensity: 0.2
            });
            snake = new THREE.Mesh(geometry, material);
            snake.castShadow = true;
            snake.position.set(0, 0.5, 0);
            scene.add(snake);
            snakeBody.push(snake);
        }

        function createFood() {
            const geometry = new THREE.SphereGeometry(0.5, 32, 32);
            const material = new THREE.MeshPhongMaterial({ 
                color: 0xff0000,
                shininess: 100,
                specular: 0xffffff,
                emissive: 0xff0000,
                emissiveIntensity: 0.5
            });
            food = new THREE.Mesh(geometry, material);
            food.castShadow = true;
            placeFood();
            scene.add(food);

            // 添加食物发光动画
            animateFoodGlow();
        }

        function animateFoodGlow() {
            if (!gameStarted) return;
            
            foodGlow = !foodGlow;
            food.material.emissiveIntensity = foodGlow ? 1 : 0.5;
            
            setTimeout(animateFoodGlow, 500);
        }

        function placeFood() {
            const x = Math.floor(Math.random() * gridSize) - gridSize/2;
            const z = Math.floor(Math.random() * gridSize) - gridSize/2;
            food.position.set(x, 0.5, z);
            
            // 添加食物出现特效
            food.scale.set(0, 0, 0);
            new TWEEN.Tween(food.scale)
                .to({ x: 1, y: 1, z: 1 }, 500)
                .easing(TWEEN.Easing.Elastic.Out)
                .start();
        }

        function moveSnake() {
            if (!gameStarted) return;

            // 移动蛇身
            const newHead = snakeBody[0].clone();
            newHead.position.add(direction);
            snakeBody.unshift(newHead);
            scene.add(newHead);

            // 添加蛇身移动动画
            newHead.scale.set(0, 0, 0);
            new TWEEN.Tween(newHead.scale)
                .to({ x: 1, y: 1, z: 1 }, 200)
                .easing(TWEEN.Easing.Back.Out)
                .start();

            // 检查是否吃到食物
            if (newHead.position.distanceTo(food.position) < 1) {
                score += 10;
                document.getElementById('score').textContent = score;
                
                // 添加吃到食物的特效
                const explosionGeometry = new THREE.SphereGeometry(1, 32, 32);
                const explosionMaterial = new THREE.MeshBasicMaterial({
                    color: 0xff0000,
                    transparent: true,
                    opacity: 0.8
                });
                const explosion = new THREE.Mesh(explosionGeometry, explosionMaterial);
                explosion.position.copy(food.position);
                scene.add(explosion);

                new TWEEN.Tween(explosion.scale)
                    .to({ x: 2, y: 2, z: 2 }, 300)
                    .easing(TWEEN.Easing.Quadratic.Out)
                    .onComplete(() => scene.remove(explosion))
                    .start();

                new TWEEN.Tween(explosion.material)
                    .to({ opacity: 0 }, 300)
                    .start();
                
                // 更新等级
                const newLevel = Math.floor(score / 100) + 1;
                if (newLevel > level) {
                    level = newLevel;
                    document.getElementById('level').textContent = level;
                    moveSpeed = Math.max(50, 200 - (level - 1) * 15);
                    clearInterval(gameLoop);
                    gameLoop = setInterval(moveSnake, moveSpeed);
                    
                    // 添加等级提升特效
                    const levelUpGeometry = new THREE.SphereGeometry(5, 32, 32);
                    const levelUpMaterial = new THREE.MeshBasicMaterial({
                        color: 0x00ff00,
                        transparent: true,
                        opacity: 0.5
                    });
                    const levelUp = new THREE.Mesh(levelUpGeometry, levelUpMaterial);
                    levelUp.position.set(0, 0, 0);
                    scene.add(levelUp);

                    new TWEEN.Tween(levelUp.scale)
                        .to({ x: 10, y: 10, z: 10 }, 500)
                        .easing(TWEEN.Easing.Quadratic.Out)
                        .onComplete(() => scene.remove(levelUp))
                        .start();

                    new TWEEN.Tween(levelUp.material)
                        .to({ opacity: 0 }, 500)
                        .start();
                }
                
                placeFood();
            } else {
                const tail = snakeBody.pop();
                scene.remove(tail);
            }

            // 检查碰撞
            if (checkCollision()) {
                gameOver();
            }
        }

        function checkCollision() {
            const head = snakeBody[0];
            // 检查墙壁碰撞
            if (Math.abs(head.position.x) > gridSize/2 || Math.abs(head.position.z) > gridSize/2) {
                return true;
            }
            // 检查自身碰撞
            for (let i = 1; i < snakeBody.length; i++) {
                if (head.position.distanceTo(snakeBody[i].position) < 0.1) {
                    return true;
                }
            }
            return false;
        }

        function handleKeyDown(event) {
            if (!gameStarted) return;
            
            // 防止连续按键
            const now = Date.now();
            if (now - lastKeyPressTime < keyPressInterval) return;
            lastKeyPressTime = now;

            // 强制英文输入法
            if (event.key.length === 1 && /[a-zA-Z]/.test(event.key)) {
                event.preventDefault();
            }
            
            const key = event.key.toLowerCase();
            switch(key) {
                case 'w':
                case 'arrowup':
                    if (direction.z !== 1) direction.set(0, 0, -1); // 向上移动
                    break;
                case 's':
                case 'arrowdown':
                    if (direction.z !== -1) direction.set(0, 0, 1); // 向下移动
                    break;
                case 'a':
                case 'arrowleft':
                    if (direction.x !== 1) direction.set(-1, 0, 0); // 向左移动
                    break;
                case 'd':
                case 'arrowright':
                    if (direction.x !== -1) direction.set(1, 0, 0); // 向右移动
                    break;
            }
        }

        function startGame() {
            document.getElementById('start-screen').style.display = 'none';
            gameStarted = true;
            gameLoop = setInterval(moveSnake, moveSpeed);
        }

        function gameOver() {
            clearInterval(gameLoop);
            gameStarted = false;
            document.getElementById('final-score').textContent = score;
            document.getElementById('final-level').textContent = level;
            document.getElementById('game-over-screen').style.display = 'flex';
        }

        function restartGame() {
            // 重置游戏状态
            score = 0;
            level = 1;
            moveSpeed = 200;
            direction.set(1, 0, 0);
            
            // 清除蛇身
            snakeBody.forEach(segment => scene.remove(segment));
            snakeBody.length = 0;
            
            // 清除食物
            if (food) {
                scene.remove(food);
            }
            
            // 重置UI
            document.getElementById('score').textContent = '0';
            document.getElementById('level').textContent = '1';
            document.getElementById('game-over-screen').style.display = 'none';
            
            // 重新创建蛇和食物
            createSnake();
            createFood();
            
            // 开始新游戏
            startGame();
        }

        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
            composer.setSize(window.innerWidth, window.innerHeight);
        }

        function animate() {
            requestAnimationFrame(animate);
            TWEEN.update();
            
            // 旋转粒子系统
            if (particleSystem) {
                particleSystem.rotation.y += 0.001;
            }
            
            composer.render();
        }

        init();
        animate();
    </script>
</body>
</html> 