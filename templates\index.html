{% extends "base.html" %}

{% block title %}首页 - OTA设备管理系统{% endblock %}

{% block head %}
<!-- Liquid Glass Homepage Styles -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/themes/liquid-glass-homepage.css') }}">
<style>
    .hero-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 20px;
        padding: 40px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .stat-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        overflow: hidden;
        background: white;
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    }

    .stat-card .card-header {
        border: none;
        padding: 20px 20px 10px 20px;
    }

    .stat-number {
        font-size: 3rem;
        font-weight: 700;
        margin: 10px 0;
    }

    .stat-icon {
        font-size: 2.5rem;
        opacity: 0.8;
        margin-bottom: 10px;
    }

    .quick-action-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        background: white;
        text-decoration: none;
        color: inherit;
    }

    .quick-action-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        text-decoration: none;
        color: inherit;
    }

    .quick-action-icon {
        font-size: 3rem;
        margin-bottom: 15px;
    }

    .recent-activity {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        padding: 25px;
    }

    .activity-item {
        padding: 15px;
        border-left: 4px solid #3498db;
        background: #f8f9fa;
        border-radius: 8px;
        margin-bottom: 15px;
    }

    .activity-time {
        font-size: 0.85rem;
        color: #6c757d;
    }

    .system-health {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        border-radius: 15px;
        padding: 25px;
        text-align: center;
    }

    .health-score {
        font-size: 4rem;
        font-weight: 700;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .loading-spinner {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 3px solid rgba(0,0,0,0.1);
        border-radius: 50%;
        border-top-color: #3498db;
        animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 欢迎横幅 -->
    <div class="hero-section">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-4 mb-3">欢迎使用 OTA 设备管理系统</h1>
                <p class="lead mb-4">智能化设备管理，实时监控，AI驱动的故障预测与维护建议</p>
                <div class="d-flex gap-3">
                    <a href="{{ url_for('ai.ai_dashboard') }}" class="btn btn-light btn-lg">
                        <i class="fas fa-chart-line"></i> AI 仪表板
                    </a>
                    <a href="{{ url_for('device.devices') }}" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-cogs"></i> 设备管理
                    </a>
                </div>
            </div>
            <div class="col-lg-4 text-center">
                <i class="fas fa-microchip" style="font-size: 8rem; opacity: 0.3;"></i>
            </div>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stat-card">
                <div class="card-header text-center">
                    <i class="fas fa-microchip stat-icon text-primary"></i>
                    <h5 class="card-title mb-0">设备总数</h5>
                </div>
                <div class="card-body text-center">
                    <div class="stat-number text-primary" id="total-devices">{{ devices|length }}</div>
                    <p class="text-muted mb-0">已注册设备</p>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stat-card">
                <div class="card-header text-center">
                    <i class="fas fa-check-circle stat-icon text-success"></i>
                    <h5 class="card-title mb-0">在线设备</h5>
                </div>
                <div class="card-body text-center">
                    <div class="stat-number text-success" id="online-devices">
                        <div class="loading-spinner"></div>
                    </div>
                    <p class="text-muted mb-0">当前在线</p>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="stat-card">
                <div class="card-header text-center">
                    <i class="fas fa-exclamation-triangle stat-icon text-warning"></i>
                    <h5 class="card-title mb-0">离线设备</h5>
                </div>
                <div class="card-body text-center">
                    <div class="stat-number text-warning" id="offline-devices">
                        <div class="loading-spinner"></div>
                    </div>
                    <p class="text-muted mb-0">当前离线</p>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="system-health">
                <i class="fas fa-heartbeat" style="font-size: 2.5rem; margin-bottom: 15px;"></i>
                <h5 class="mb-3">系统健康度</h5>
                <div class="health-score" id="system-health">
                    <div class="loading-spinner"></div>
                </div>
                <p class="mb-0" style="opacity: 0.9;">综合评分</p>
            </div>
        </div>
    </div>

    <!-- 快速操作 -->
    <div class="row mb-4">
        <div class="col-12">
            <h4 class="mb-4"><i class="fas fa-bolt text-primary"></i> 快速操作</h4>
        </div>

        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{{ url_for('device.devices') }}" class="quick-action-card d-block p-4 text-center">
                <i class="fas fa-list quick-action-icon text-primary"></i>
                <h6 class="mb-0">设备管理</h6>
                <small class="text-muted">管理所有设备</small>
            </a>
        </div>

        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{{ url_for('device_location.device_map') }}" class="quick-action-card d-block p-4 text-center">
                <i class="fas fa-map quick-action-icon text-success"></i>
                <h6 class="mb-0">设备地图</h6>
                <small class="text-muted">地理位置分布</small>
            </a>
        </div>

        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{{ url_for('ai.chat') }}" class="quick-action-card d-block p-4 text-center">
                <i class="fas fa-robot quick-action-icon text-info"></i>
                <h6 class="mb-0">AI助手</h6>
                <small class="text-muted">智能问答</small>
            </a>
        </div>

        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{{ url_for('firmware.firmware_list') }}" class="quick-action-card d-block p-4 text-center">
                <i class="fas fa-microchip quick-action-icon text-warning"></i>
                <h6 class="mb-0">固件管理</h6>
                <small class="text-muted">上传和管理固件</small>
            </a>
        </div>

        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{{ url_for('main.ota_tasks') }}" class="quick-action-card d-block p-4 text-center">
                <i class="fas fa-tasks quick-action-icon text-danger"></i>
                <h6 class="mb-0">OTA任务</h6>
                <small class="text-muted">升级任务管理</small>
            </a>
        </div>

        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{{ url_for('static', filename='tools/message-parser/index.html') }}" target="_blank" class="quick-action-card d-block p-4 text-center">
                <i class="fas fa-tools quick-action-icon text-secondary"></i>
                <h6 class="mb-0">协议工具</h6>
                <small class="text-muted">消息解析工具</small>
            </a>
        </div>

        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <a href="{{ url_for('mqtt_forwarder.forwarder_records') }}" class="quick-action-card d-block p-4 text-center">
                <i class="fas fa-exchange-alt quick-action-icon text-purple"></i>
                <h6 class="mb-0">消息转发</h6>
                <small class="text-muted">MQTT转发记录</small>
            </a>
        </div>
    </div>

    <!-- 最近活动和系统状态 -->
    <div class="row">
        <div class="col-lg-8">
            <div class="recent-activity">
                <h5 class="mb-4"><i class="fas fa-clock text-primary"></i> 最近活动</h5>
                <div id="recent-activities">
                    <div class="text-center py-4">
                        <div class="loading-spinner"></div>
                        <p class="mt-2 text-muted">正在加载最近活动...</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="recent-activity">
                <h5 class="mb-4"><i class="fas fa-info-circle text-primary"></i> 系统信息</h5>
                <div class="mb-3">
                    <strong>系统版本：</strong> v2.0.0
                </div>
                <div class="mb-3">
                    <strong>运行时间：</strong> <span id="uptime">计算中...</span>
                </div>
                <div class="mb-3">
                    <strong>最后更新：</strong> <span id="last-update">加载中...</span>
                </div>
                <div class="mb-3">
                    <strong>固件数量：</strong> {{ firmwares|length }}
                </div>
                <hr>
                <div class="d-grid gap-2">
                    <a href="{{ url_for('ai.ai_dashboard') }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-chart-line"></i> AI 仪表板
                    </a>
                    <button class="btn btn-outline-primary btn-sm" onclick="refreshSystemData()">
                        <i class="fas fa-sync-alt"></i> 刷新数据
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
    // 页面初始化
    $(document).ready(function() {
        // 设置初始时间
        updateLastUpdateTime();

        loadSystemData();
        loadRecentActivities();

        // 设置定时刷新
        setInterval(loadSystemData, 30000); // 30秒刷新一次统计数据
        setInterval(updateUptime, 1000); // 1秒更新一次运行时间
    });

    // 更新最后更新时间
    function updateLastUpdateTime() {
        const now = new Date();
        const timeString = now.getFullYear() + '-' +
                          String(now.getMonth() + 1).padStart(2, '0') + '-' +
                          String(now.getDate()).padStart(2, '0') + ' ' +
                          String(now.getHours()).padStart(2, '0') + ':' +
                          String(now.getMinutes()).padStart(2, '0') + ':' +
                          String(now.getSeconds()).padStart(2, '0');
        document.getElementById('last-update').textContent = timeString;
    }

    // 加载系统数据
    function loadSystemData() {
        // 加载设备统计
        fetch('/api/devices/stats')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('设备统计数据:', data); // 调试信息
                if (data.success) {
                    const stats = data.stats;
                    document.getElementById('online-devices').textContent = stats.online;
                    document.getElementById('offline-devices').textContent = stats.offline;

                    // 计算系统健康度
                    const healthScore = stats.total > 0 ? Math.round((stats.online / stats.total) * 100) : 0;
                    document.getElementById('system-health').textContent = `${healthScore}%`;
                } else {
                    console.error('API返回错误:', data);
                    // 显示错误状态
                    document.getElementById('online-devices').textContent = '错误';
                    document.getElementById('offline-devices').textContent = '错误';
                    document.getElementById('system-health').textContent = '错误';
                }
            })
            .catch(error => {
                console.error('加载设备统计失败:', error);
                // 显示错误状态
                document.getElementById('online-devices').textContent = '加载失败';
                document.getElementById('offline-devices').textContent = '加载失败';
                document.getElementById('system-health').textContent = '加载失败';
            });
    }

    // 加载最近活动
    function loadRecentActivities() {
        // 模拟最近活动数据（实际项目中应该从API获取）
        const activities = [
            {
                time: '2分钟前',
                type: 'success',
                message: '设备 DEV001 成功完成固件升级'
            },
            {
                time: '5分钟前',
                type: 'info',
                message: '新设备 DEV025 已注册到系统'
            },
            {
                time: '10分钟前',
                type: 'warning',
                message: '设备 DEV003 离线超过30分钟'
            },
            {
                time: '15分钟前',
                type: 'success',
                message: 'AI分析完成，生成了3个维护建议'
            },
            {
                time: '20分钟前',
                type: 'info',
                message: '系统自动备份已完成'
            }
        ];

        const activitiesHtml = activities.map(activity => {
            const iconClass = {
                'success': 'fas fa-check-circle text-success',
                'info': 'fas fa-info-circle text-info',
                'warning': 'fas fa-exclamation-triangle text-warning',
                'error': 'fas fa-times-circle text-danger'
            }[activity.type];

            return `
                <div class="activity-item">
                    <div class="d-flex align-items-start">
                        <i class="${iconClass} me-3 mt-1"></i>
                        <div class="flex-grow-1">
                            <div>${activity.message}</div>
                            <div class="activity-time">${activity.time}</div>
                        </div>
                    </div>
                </div>
            `;
        }).join('');

        document.getElementById('recent-activities').innerHTML = activitiesHtml;
    }

    // 更新运行时间
    function updateUptime() {
        // 模拟运行时间（实际项目中应该从服务器获取）
        const startTime = new Date('2025-01-01 00:00:00');
        const now = new Date();
        const diff = now - startTime;

        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

        document.getElementById('uptime').textContent = `${days}天 ${hours}小时 ${minutes}分钟`;
    }

    // 刷新系统数据
    function refreshSystemData() {
        // 显示加载状态
        ['online-devices', 'offline-devices', 'system-health'].forEach(id => {
            document.getElementById(id).innerHTML = '<div class="loading-spinner"></div>';
        });

        // 重新加载数据
        loadSystemData();
        loadRecentActivities();

        // 更新最后更新时间
        updateLastUpdateTime();

        // 显示提示
        showToast('系统数据已刷新', 'success');
    }

    // 显示提示消息
    function showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 6px;
            color: white;
            font-size: 14px;
            z-index: 10000;
            max-width: 300px;
            word-wrap: break-word;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transition: all 0.3s ease;
        `;

        // 根据类型设置背景色
        switch(type) {
            case 'success':
                toast.style.background = 'linear-gradient(45deg, #27ae60, #2ecc71)';
                break;
            case 'error':
                toast.style.background = 'linear-gradient(45deg, #e74c3c, #c0392b)';
                break;
            case 'warning':
                toast.style.background = 'linear-gradient(45deg, #f39c12, #e67e22)';
                break;
            default:
                toast.style.background = 'linear-gradient(45deg, #3498db, #2980b9)';
        }

        toast.textContent = message;
        document.body.appendChild(toast);

        // 3秒后自动移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }
        }, 3000);

        return toast;
    }

</script>
{% endblock %}