{% extends "base.html" %}

{% block title %}IoT客户端控制 - OTA设备管理系统{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="border-bottom pb-2"><i class="fas fa-server"></i> IoT客户端控制</h2>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-6 mx-auto">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0"><i class="fas fa-cogs"></i> IoT客户端状态管理</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-4">
                        <div class="me-3">
                            <span class="fw-bold">当前状态:</span>
                        </div>
                        <div>
                            <span id="status-badge" class="badge rounded-pill 
                                {% if status == '运行中' %}bg-success{% elif status == '已停止' %}bg-danger{% else %}bg-secondary{% endif %}">
                                {{ status }}
                            </span>
                        </div>
                    </div>
                    
                    <div class="alert alert-info" role="alert">
                        <i class="fas fa-info-circle me-2"></i>
                        <span class="fw-bold">提示:</span> IoT客户端必须启动才能执行OTA升级任务。在批量操作前请确保客户端正常运行。
                    </div>
                    
                    <div id="message-container" class="alert alert-success d-none" role="alert"></div>
                    
                    <div class="d-flex justify-content-between">
                        <button id="start-button" class="btn btn-success" {% if status == '运行中' %}disabled{% endif %}>
                            <i class="fas fa-play me-2"></i>启动客户端
                        </button>
                        <button id="stop-button" class="btn btn-danger" {% if status != '运行中' %}disabled{% endif %}>
                            <i class="fas fa-stop me-2"></i>停止客户端
                        </button>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">最后更新时间: <span id="last-update-time">{{ now }}</span></small>
                        <button id="refresh-button" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-sync-alt me-1"></i>刷新状态
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="card mt-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0"><i class="fas fa-question-circle"></i> 帮助说明</h5>
                </div>
                <div class="card-body">
                    <h6 class="fw-bold">使用说明:</h6>
                    <ul>
                        <li>启动IoT客户端后才能执行OTA升级任务</li>
                        <li>批量OTA升级任务需要客户端保持运行</li>
                        <li>不使用时可以停止客户端以释放资源</li>
                        <li>如果客户端状态异常，可以尝试停止后重新启动</li>
                    </ul>
                    
                    <h6 class="fw-bold mt-3">注意事项:</h6>
                    <p>同一时间只能有一个IoT客户端连接到云端。如果有其他应用程序正在使用相同的连接参数，可能会导致连接冲突。</p>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clock text-primary me-2"></i>设备状态查询设置
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">状态查询间隔</label>
                        <select class="form-select" id="statusInterval" onchange="setStatusCheckInterval(this.value)">
                            <option value="10s">10秒</option>
                            <option value="1m" selected>1分钟</option>
                            <option value="30m">30分钟</option>
                            <option value="2h">2小时</option>
                        </select>
                        <div class="form-text">选择设备状态的自动刷新间隔</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const startButton = document.getElementById('start-button');
    const stopButton = document.getElementById('stop-button');
    const refreshButton = document.getElementById('refresh-button');
    const statusBadge = document.getElementById('status-badge');
    const messageContainer = document.getElementById('message-container');
    const lastUpdateTime = document.getElementById('last-update-time');
    
    // 格式化时间函数
    function formatDateTime() {
        const now = new Date();
        return now.toLocaleString();
    }
    
    // 设置当前时间
    lastUpdateTime.textContent = formatDateTime();
    
    // 更新状态UI
    function updateStatusUI(status) {
        statusBadge.textContent = status;
        statusBadge.className = 'badge rounded-pill';
        
        if (status === '运行中') {
            statusBadge.classList.add('bg-success');
            startButton.disabled = true;
            stopButton.disabled = false;
        } else if (status === '已停止') {
            statusBadge.classList.add('bg-danger');
            startButton.disabled = false;
            stopButton.disabled = true;
        } else {
            statusBadge.classList.add('bg-secondary');
            startButton.disabled = false;
            stopButton.disabled = true;
        }
        
        lastUpdateTime.textContent = formatDateTime();
    }
    
    // 显示消息
    function showMessage(text, isSuccess = true) {
        messageContainer.textContent = text;
        messageContainer.className = isSuccess ? 'alert alert-success' : 'alert alert-danger';
        messageContainer.classList.remove('d-none');
        
        // 5秒后自动隐藏消息
        setTimeout(() => {
            messageContainer.classList.add('d-none');
        }, 5000);
    }
    
    // 刷新状态
    function refreshStatus() {
        fetch('/iot/status')
            .then(response => response.json())
            .then(data => {
                updateStatusUI(data.status);
            })
            .catch(error => {
                console.error('获取状态失败:', error);
                showMessage('获取状态失败，请重试', false);
            });
    }
    
    // 启动IoT客户端
    startButton.addEventListener('click', function() {
        startButton.disabled = true;
        startButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>正在启动...';
        
        fetch('/iot/start', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage(data.message);
                // 启动成功后立即刷新状态
                setTimeout(refreshStatus, 1000);
            } else {
                showMessage(data.message, false);
                startButton.disabled = false;
            }
            updateStatusUI(data.status);
        })
        .catch(error => {
            console.error('启动失败:', error);
            showMessage('启动失败，请重试', false);
            startButton.disabled = false;
        })
        .finally(() => {
            startButton.innerHTML = '<i class="fas fa-play me-2"></i>启动客户端';
        });
    });
    
    // 停止IoT客户端
    stopButton.addEventListener('click', function() {
        if (!confirm('确定要停止IoT客户端吗？这将终止所有正在进行的OTA任务。')) {
            return;
        }
        
        stopButton.disabled = true;
        stopButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>正在停止...';
        
        fetch('/iot/stop', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage(data.message);
                // 停止成功后立即刷新状态
                setTimeout(refreshStatus, 1000);
            } else {
                showMessage(data.message, false);
                stopButton.disabled = false;
            }
            updateStatusUI(data.status);
        })
        .catch(error => {
            console.error('停止失败:', error);
            showMessage('停止失败，请重试', false);
            stopButton.disabled = false;
        })
        .finally(() => {
            stopButton.innerHTML = '<i class="fas fa-stop me-2"></i>停止客户端';
        });
    });
    
    // 刷新状态
    refreshButton.addEventListener('click', function() {
        refreshButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>刷新中...';
        refreshButton.disabled = true;
        
        refreshStatus();
        
        setTimeout(() => {
            refreshButton.innerHTML = '<i class="fas fa-sync-alt me-1"></i>刷新状态';
            refreshButton.disabled = false;
        }, 1000);
    });
    
    // 每30秒自动刷新一次状态
    setInterval(refreshStatus, 30000);
});
</script>
{% endblock %} 