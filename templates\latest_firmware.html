{% extends "base.html" %}

{% block title %}最新固件管理 - OTA设备管理系统{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="mb-0"><i class="fas fa-star text-warning me-2"></i>最新固件管理</h2>
                <a href="{{ url_for('firmware.firmware_list') }}" class="btn btn-outline-primary">
                    <i class="fas fa-list me-1"></i>返回固件列表
                </a>
            </div>
            <p class="text-muted mt-2">管理各设备类型的最新固件版本，用于批量升级和自动升级功能</p>
        </div>
    </div>

    <!-- 设备类型固件管理 -->
    {% for device_type in [10, 50, 51] %}
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">
                <i class="fas fa-microchip me-2"></i>
                {{ firmwares_by_type[device_type][0].get_device_type_name(device_type) if firmwares_by_type[device_type] else 
                   ("V2 (旧版霍尔传感器版本，黑色PCB)" if device_type == 10 else 
                    ("V5 (新版BL0910 10通道版本)" if device_type == 50 else "V51 (新版BL0939 2通道版本)")) }}
            </h5>
        </div>
        <div class="card-body">
            {% set current_latest = latest_firmwares | selectattr('device_type', 'equalto', device_type) | first %}
            
            <!-- 当前最新固件显示 -->
            <div class="row mb-3">
                <div class="col-md-6">
                    <h6 class="text-primary">当前最新固件</h6>
                    {% if current_latest %}
                    <div class="alert alert-success">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>{{ current_latest.firmware.name }}</strong><br>
                                <small class="text-muted">
                                    版本: {{ current_latest.firmware.version }} | 
                                    更新时间: {{ current_latest.updated_at.strftime('%Y-%m-%d %H:%M:%S') }}
                                    {% if current_latest.updated_by %}| 更新者: {{ current_latest.updated_by }}{% endif %}
                                </small>
                            </div>
                            <span class="badge bg-success">
                                <i class="fas fa-star me-1"></i>最新
                            </span>
                        </div>
                    </div>
                    {% else %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        尚未设置最新固件
                    </div>
                    {% endif %}
                </div>
                
                <!-- 固件选择 -->
                <div class="col-md-6">
                    <h6 class="text-primary">设置最新固件</h6>
                    {% if firmwares_by_type[device_type] %}
                    <form class="set-latest-form" data-device-type="{{ device_type }}">
                        <div class="input-group">
                            <select class="form-select" name="firmware_id" required>
                                <option value="">选择固件版本</option>
                                {% for firmware in firmwares_by_type[device_type] %}
                                <option value="{{ firmware.id }}" 
                                        {% if current_latest and current_latest.firmware_id == firmware.id %}selected{% endif %}>
                                    {{ firmware.name }} ({{ firmware.version }}) - {{ firmware.upload_time.strftime('%Y-%m-%d') }}
                                </option>
                                {% endfor %}
                            </select>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>设置
                            </button>
                        </div>
                    </form>
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        暂无此类型的固件，请先上传固件
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- 可用固件列表 -->
            {% if firmwares_by_type[device_type] %}
            <h6 class="text-primary">可用固件列表</h6>
            <div class="table-responsive">
                <table class="table table-sm table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>固件名称</th>
                            <th>版本</th>
                            <th>大小</th>
                            <th>上传时间</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for firmware in firmwares_by_type[device_type] %}
                        <tr>
                            <td>{{ firmware.name }}</td>
                            <td><code>{{ firmware.version }}</code></td>
                            <td>{{ "%.2f"|format(firmware.size / 1024 / 1024) }} MB</td>
                            <td>{{ firmware.upload_time.strftime('%Y-%m-%d %H:%M') }}</td>
                            <td>
                                {% if current_latest and current_latest.firmware_id == firmware.id %}
                                <span class="badge bg-success">
                                    <i class="fas fa-star me-1"></i>最新
                                </span>
                                {% else %}
                                <span class="badge bg-secondary">可用</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% endif %}
        </div>
    </div>
    {% endfor %}
</div>
{% endblock %}

{% block scripts %}
<script>
// 处理设置最新固件表单提交
document.querySelectorAll('.set-latest-form').forEach(form => {
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const deviceType = this.dataset.deviceType;
        const firmwareId = this.querySelector('select[name="firmware_id"]').value;
        
        if (!firmwareId) {
            alert('请选择固件版本');
            return;
        }
        
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>设置中...';
        submitBtn.disabled = true;
        
        const formData = new FormData();
        formData.append('device_type', deviceType);
        formData.append('firmware_id', firmwareId);
        
        fetch('/firmware/set_latest', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('设置成功！');
                location.reload();
            } else {
                alert('设置失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('设置失败: ' + error.message);
        })
        .finally(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        });
    });
});
</script>
{% endblock %}
