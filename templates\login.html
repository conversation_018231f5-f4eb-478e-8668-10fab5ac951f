<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - OTA设备管理系统</title>
    {% include 'base_css.html' %}
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        body {
            margin: 0;
            padding: 0;
            background: url('https://source.unsplash.com/1920x1080/?technology,digital') no-repeat center center fixed;
            background-size: cover;
            position: relative;
            min-height: 100vh;
        }
        
        #particles-js {
            position: fixed;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            z-index: 1;
            background: linear-gradient(45deg, #1a237e, #0d47a1);
            opacity: 0.8;
        }
        
        .login-container {
            position: relative;
            z-index: 2;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: none;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
            border-radius: 15px;
            overflow: hidden;
        }
        
        .card-header {
            background: rgba(13, 110, 253, 0.9) !important;
            border-bottom: none;
            padding: 1.5rem;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #2196F3, #4CAF50);
            border: none;
            transition: all 0.3s ease;
            padding: 12px;
            font-size: 1.1rem;
            font-weight: 500;
            letter-spacing: 1px;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            background: linear-gradient(45deg, #1976D2, #388E3C);
        }
        
        .input-group-text {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid #ced4da;
            border-right: none;
        }
        
        .form-control {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid #ced4da;
            border-left: none;
            padding: 12px;
            font-size: 1rem;
        }
        
        .form-control:focus {
            box-shadow: none;
            border-color: #80bdff;
            background: rgba(255, 255, 255, 0.95);
        }
        
        .input-group:focus-within .input-group-text {
            border-color: #80bdff;
        }
        
        .form-label {
            font-weight: 500;
            color: #495057;
            margin-bottom: 0.5rem;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .alert-success {
            background-color: rgba(76, 175, 80, 0.9);
            color: white;
        }

        .alert-danger {
            background-color: rgba(244, 67, 54, 0.9);
            color: white;
        }

        .btn-link {
            color: #6c757d;
            text-decoration: none;
            transition: all 0.3s ease;
            font-weight: 500;
            padding: 0.5rem 0;
            display: inline-block;
        }
        
        .btn-link:hover {
            color: #4CAF50;
            transform: translateX(5px);
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .card-footer {
            background: transparent !important;
            border-top: 1px solid rgba(0,0,0,0.1);
        }

        .system-info {
            color: #6c757d;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .system-info i {
            color: #4CAF50;
        }
    </style>
</head>

<body>
    <div id="particles-js"></div>
    <div class="container login-container">
        <div class="row justify-content-center align-items-center min-vh-100">
            <div class="col-md-6 col-lg-4">
                <div class="card shadow-lg border-0 rounded-lg fade-in">
                    <div class="card-header bg-primary text-white text-center py-4">
                        <h3 class="mb-0">
                            <i class="fas fa-user-shield me-2"></i>系统登录
                        </h3>
                    </div>
                    <div class="card-body p-4">
                        {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                        {% for category, message in messages %}
                        <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {% if category == 'success' %}
                            <i class="fas fa-check-circle me-2"></i>
                            {% else %}
                            <i class="fas fa-exclamation-circle me-2"></i>
                            {% endif %}
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        {% endfor %}
                        {% endif %}
                        {% endwith %}

                        <form method="POST" action="{{ url_for('auth.login') }}" class="needs-validation" novalidate>
                            <div class="mb-3">
                                <label for="username" class="form-label">用户名</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                    <input type="text" class="form-control" id="username" name="username" required>
                                </div>
                            </div>
                            <div class="mb-4">
                                <label for="password" class="form-label">密码</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                </div>
                            </div>
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-sign-in-alt me-2"></i>登录
                                </button>
                            </div>
                        </form>
                        <div class="text-center mt-3">
                            <a href="{{ url_for('auth.register') }}" class="btn-link">
                                <i class="fas fa-user-plus me-1"></i>没有账号？立即注册
                            </a>
                        </div>
                    </div>
                    <div class="card-footer text-center py-3">
                        <div class="system-info">
                            <i class="fas fa-info-circle"></i>
                            <span>OTA设备管理系统</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="{{ url_for('static', filename='libs/js/bootstrap.bundle.min.js') }}"></script>
    <script src="https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js"></script>
    <script>
        // 表单验证
        (function () {
            'use strict'
            var forms = document.querySelectorAll('.needs-validation')
            Array.prototype.slice.call(forms).forEach(function (form) {
                form.addEventListener('submit', function (event) {
                    if (!form.checkValidity()) {
                        event.preventDefault()
                        event.stopPropagation()
                    }
                    form.classList.add('was-validated')
                }, false)
            })
        })()

        // 粒子效果配置
        particlesJS('particles-js', {
            particles: {
                number: {
                    value: 80,
                    density: {
                        enable: true,
                        value_area: 800
                    }
                },
                color: {
                    value: '#ffffff'
                },
                shape: {
                    type: 'circle'
                },
                opacity: {
                    value: 0.5,
                    random: false
                },
                size: {
                    value: 3,
                    random: true
                },
                line_linked: {
                    enable: true,
                    distance: 150,
                    color: '#ffffff',
                    opacity: 0.4,
                    width: 1
                },
                move: {
                    enable: true,
                    speed: 2,
                    direction: 'none',
                    random: false,
                    straight: false,
                    out_mode: 'out',
                    bounce: false
                }
            },
            interactivity: {
                detect_on: 'canvas',
                events: {
                    onhover: {
                        enable: true,
                        mode: 'repulse'
                    },
                    onclick: {
                        enable: true,
                        mode: 'push'
                    },
                    resize: true
                }
            },
            retina_detect: true
        });
    </script>
</body>
</html>