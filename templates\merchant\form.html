{% extends "base.html" %}

{% block title %}{% if merchant %}编辑商户{% else %}添加商户{% endif %} - OTA设备管理系统{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="border-bottom pb-2">
                <i class="fas fa-{% if merchant %}edit{% else %}plus{% endif %}"></i> 
                {% if merchant %}编辑商户{% else %}添加商户{% endif %}
            </h2>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-{% if merchant %}edit{% else %}plus{% endif %}"></i> 
                        {% if merchant %}编辑商户信息{% else %}添加新商户{% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="post" action="{% if merchant %}{{ url_for('merchant.edit_merchant', id=merchant.id) }}{% else %}{{ url_for('merchant.add_merchant') }}{% endif %}">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name" class="form-label">商户名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" value="{{ merchant.name if merchant else '' }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="status" class="form-label">商户状态</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="正常" {% if merchant and merchant.status == '正常' %}selected{% endif %}>正常</option>
                                        <option value="禁用" {% if merchant and merchant.status == '禁用' %}selected{% endif %}>禁用</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="contact_person" class="form-label">联系人 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="contact_person" name="contact_person" value="{{ merchant.contact_person if merchant else '' }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="contact_phone" class="form-label">联系电话 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="contact_phone" name="contact_phone" value="{{ merchant.contact_phone if merchant else '' }}" required>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="contact_email" class="form-label">联系邮箱</label>
                                    <input type="email" class="form-control" id="contact_email" name="contact_email" value="{{ merchant.contact_email if merchant else '' }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="address" class="form-label">商户地址</label>
                                    <input type="text" class="form-control" id="address" name="address" value="{{ merchant.address if merchant else '' }}">
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="business_license" class="form-label">营业执照号</label>
                                    <input type="text" class="form-control" id="business_license" name="business_license" value="{{ merchant.business_license if merchant else '' }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="tax_number" class="form-label">税号</label>
                                    <input type="text" class="form-control" id="tax_number" name="tax_number" value="{{ merchant.tax_number if merchant else '' }}">
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="bank_name" class="form-label">开户银行</label>
                                    <input type="text" class="form-control" id="bank_name" name="bank_name" value="{{ merchant.bank_name if merchant else '' }}">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="bank_account" class="form-label">银行账号</label>
                                    <input type="text" class="form-control" id="bank_account" name="bank_account" value="{{ merchant.bank_account if merchant else '' }}">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="bank_account_name" class="form-label">开户名</label>
                                    <input type="text" class="form-control" id="bank_account_name" name="bank_account_name" value="{{ merchant.bank_account_name if merchant else '' }}">
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-12">
                                <div class="form-group">
                                    <label for="remark" class="form-label">备注</label>
                                    <textarea class="form-control" id="remark" name="remark" rows="3">{{ merchant.remark if merchant else '' }}</textarea>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> 保存
                                </button>
                                <a href="{{ url_for('merchant.merchant_list') }}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> 返回
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 