{% extends "base.html" %}

{% block title %}商户管理 - OTA设备管理系统{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="border-bottom pb-2"><i class="fas fa-store"></i> 商户管理</h2>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0"><i class="fas fa-list"></i> 商户列表</h5>
                    <a href="{{ url_for('merchant.add_merchant') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> 添加商户
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover align-middle">
                            <thead class="table-light">
                                <tr>
                                    <th>ID</th>
                                    <th>商户名称</th>
                                    <th>联系人</th>
                                    <th>联系电话</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for merchant in merchants %}
                                <tr>
                                    <td>{{ merchant.id }}</td>
                                    <td>{{ merchant.name }}</td>
                                    <td>{{ merchant.contact_person }}</td>
                                    <td>{{ merchant.contact_phone }}</td>
                                    <td>
                                        {% if merchant.status == '正常' %}
                                        <span class="badge bg-success">{{ merchant.status }}</span>
                                        {% else %}
                                        <span class="badge bg-danger">{{ merchant.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ merchant.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ url_for('merchant.view_merchant', id=merchant.id) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i> 查看
                                            </a>
                                            <a href="{{ url_for('merchant.edit_merchant', id=merchant.id) }}" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i> 编辑
                                            </a>
                                            <a href="{{ url_for('merchant.delete_merchant', id=merchant.id) }}" class="btn btn-sm btn-danger" onclick="return confirm('确定要删除此商户吗？')">
                                                <i class="fas fa-trash"></i> 删除
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 