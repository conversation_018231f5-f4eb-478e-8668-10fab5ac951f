{% extends "base.html" %}

{% block title %}商户详情 - OTA设备管理系统{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row mb-4">
        <div class="col-12">
            <h2 class="border-bottom pb-2"><i class="fas fa-store"></i> 商户详情</h2>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0"><i class="fas fa-info-circle"></i> 商户信息</h5>
                    <div>
                        <a href="{{ url_for('merchant.edit_merchant', id=merchant.id) }}" class="btn btn-warning">
                            <i class="fas fa-edit"></i> 编辑
                        </a>
                        <a href="{{ url_for('merchant.merchant_list') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> 返回
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="card-title mb-0"><i class="fas fa-building"></i> 基本信息</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th style="width: 30%">商户ID</th>
                                            <td>{{ merchant.id }}</td>
                                        </tr>
                                        <tr>
                                            <th>商户名称</th>
                                            <td>{{ merchant.name }}</td>
                                        </tr>
                                        <tr>
                                            <th>商户状态</th>
                                            <td>
                                                {% if merchant.status == '正常' %}
                                                <span class="badge bg-success">{{ merchant.status }}</span>
                                                {% else %}
                                                <span class="badge bg-danger">{{ merchant.status }}</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>创建时间</th>
                                            <td>{{ merchant.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                                        </tr>
                                        <tr>
                                            <th>更新时间</th>
                                            <td>{{ merchant.updated_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-info text-white">
                                    <h5 class="card-title mb-0"><i class="fas fa-address-card"></i> 联系信息</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th style="width: 30%">联系人</th>
                                            <td>{{ merchant.contact_person }}</td>
                                        </tr>
                                        <tr>
                                            <th>联系电话</th>
                                            <td>{{ merchant.contact_phone }}</td>
                                        </tr>
                                        <tr>
                                            <th>联系邮箱</th>
                                            <td>{{ merchant.contact_email or '未设置' }}</td>
                                        </tr>
                                        <tr>
                                            <th>商户地址</th>
                                            <td>{{ merchant.address or '未设置' }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-success text-white">
                                    <h5 class="card-title mb-0"><i class="fas fa-file-alt"></i> 营业执照信息</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th style="width: 30%">营业执照号</th>
                                            <td>{{ merchant.business_license or '未设置' }}</td>
                                        </tr>
                                        <tr>
                                            <th>税号</th>
                                            <td>{{ merchant.tax_number or '未设置' }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-header bg-warning text-dark">
                                    <h5 class="card-title mb-0"><i class="fas fa-university"></i> 银行账户信息</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th style="width: 30%">开户银行</th>
                                            <td>{{ merchant.bank_name or '未设置' }}</td>
                                        </tr>
                                        <tr>
                                            <th>银行账号</th>
                                            <td>{{ merchant.bank_account or '未设置' }}</td>
                                        </tr>
                                        <tr>
                                            <th>开户名</th>
                                            <td>{{ merchant.bank_account_name or '未设置' }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-secondary text-white">
                                    <h5 class="card-title mb-0"><i class="fas fa-sticky-note"></i> 备注</h5>
                                </div>
                                <div class="card-body">
                                    <p>{{ merchant.remark or '无备注' }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 