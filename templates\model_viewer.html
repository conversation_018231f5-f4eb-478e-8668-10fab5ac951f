{% extends "base.html" %}

{% block title %}3D模型查看器{% endblock %}

{% block styles %}
{{ super() }}
<link rel="stylesheet" href="{{ url_for('static', filename='css/model-viewer.css') }}">
{% endblock %}

{% block content %}
<div class="container mt-5">
    <h1 class="text-center mb-4">3D模型查看器</h1>
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">上传3D模型文件</h5>
                    <form id="uploadForm" enctype="multipart/form-data">
                        <div class="mb-3">
                            <input type="file" class="form-control" id="modelFile" accept=".gltf,.glb" multiple>
                            <small class="form-text text-muted">仅支持 glTF 和 GLB 格式，最大文件大小 100MB</small>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">上传</button>
                    </form>
                    <div id="uploadStatus" class="mt-3"></div>
                </div>
            </div>
            <div class="card mt-4">
                <div class="card-body">
                    <h5 class="card-title">文件列表</h5>
                    <ul id="fileList" class="list-group"></ul>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">3D模型预览</h5>
                    <model-viewer id="modelViewer" style="width: 100%; height: 400px;" alt="3D模型预览" ar ar-modes="webxr scene-viewer quick-look" environment-image="neutral" auto-rotate camera-controls></model-viewer>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script type="module" src="{{ url_for('static', filename='libs/js/model-viewer.min.js') }}"></script>
<script src="{{ url_for('static', filename='js/model-viewer.js') }}"></script>
{% endblock %}
