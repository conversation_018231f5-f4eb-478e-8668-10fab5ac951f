{% extends "base.html" %}

{% block title %}服务器监控{% endblock %}

{% block head %}
<style>
    .monitor-card {
        transition: all 0.3s ease;
        margin-bottom: 20px;
        border: none;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .monitor-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    .card-header {
        border-radius: 10px 10px 0 0 !important;
        padding: 15px 20px;
    }
    .card-body {
        padding: 20px;
    }
    .progress {
        height: 8px;
        border-radius: 4px;
        margin: 10px 0;
    }
    .chart-container {
        position: relative;
        height: 200px;
        width: 100%;
        margin-top: 20px;
    }
    .system-info-item {
        margin-bottom: 15px;
        padding: 12px;
        border-radius: 8px;
        background-color: #f8f9fa;
        transition: all 0.3s ease;
    }
    .system-info-item:hover {
        background-color: #e9ecef;
    }
    .system-info-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 5px;
    }
    .system-info-value {
        color: #212529;
        font-size: 0.95rem;
    }
    .process-table {
        margin-top: 15px;
    }
    .process-table th {
        font-weight: 600;
        background-color: #f8f9fa;
    }
    .refresh-btn {
        position: absolute;
        top: 10px;
        right: 10px;
        padding: 5px 10px;
        border-radius: 5px;
        background-color: rgba(255,255,255,0.2);
        border: none;
        color: white;
        transition: all 0.3s ease;
    }
    .refresh-btn:hover {
        background-color: rgba(255,255,255,0.3);
    }
    .status-indicator {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 5px;
    }
    .status-online {
        background-color: #28a745;
    }
    .status-offline {
        background-color: #dc3545;
    }
    .status-warning {
        background-color: #ffc107;
    }
    .metric-value {
        font-size: 1.5rem;
        font-weight: 600;
        margin: 10px 0;
    }
    .metric-label {
        color: #6c757d;
        font-size: 0.9rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card monitor-card">
                <div class="card-header bg-primary text-white">
                    <h3 class="card-title mb-0">
                        <i class="fas fa-server me-2"></i>服务器监控仪表盘
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="system-info-item">
                                <div class="system-info-label">操作系统</div>
                                <div class="system-info-value" id="os-info">加载中...</div>
                            </div>
                            <div class="system-info-item">
                                <div class="system-info-label">主机名</div>
                                <div class="system-info-value" id="hostname">加载中...</div>
                            </div>
                            <div class="system-info-item">
                                <div class="system-info-label">处理器</div>
                                <div class="system-info-value" id="processor">加载中...</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="system-info-item">
                                <div class="system-info-label">Python版本</div>
                                <div class="system-info-value" id="python-version">加载中...</div>
                            </div>
                            <div class="system-info-item">
                                <div class="system-info-label">启动时间</div>
                                <div class="system-info-value" id="boot-time">加载中...</div>
                            </div>
                            <div class="system-info-item">
                                <div class="system-info-label">架构</div>
                                <div class="system-info-value" id="architecture">加载中...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- CPU监控 -->
        <div class="col-md-6">
            <div class="card monitor-card">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-microchip me-2"></i>CPU监控
                    </h5>
                    <button class="btn btn-sm btn-light refresh-btn" onclick="refreshCPU()">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="metric-label">CPU使用率</div>
                            <div class="progress">
                                <div id="cpu-progress" class="progress-bar bg-danger" role="progressbar" style="width: 0%"></div>
                            </div>
                            <div class="metric-value" id="cpu-percent">0%</div>
                        </div>
                        <div class="col-md-6">
                            <div class="metric-label">CPU频率</div>
                            <div class="metric-value" id="cpu-freq">加载中...</div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="metric-label">CPU核心数</div>
                            <div class="metric-value" id="cpu-count">加载中...</div>
                        </div>
                        <div class="col-md-6">
                            <div class="metric-label">CPU时间</div>
                            <div class="metric-value" id="cpu-times">加载中...</div>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="cpu-chart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 内存监控 -->
        <div class="col-md-6">
            <div class="card monitor-card">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-memory me-2"></i>内存监控
                    </h5>
                    <button class="btn btn-sm btn-light refresh-btn" onclick="refreshMemory()">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="metric-label">物理内存</div>
                            <div class="progress">
                                <div id="memory-progress" class="progress-bar bg-success" role="progressbar" style="width: 0%"></div>
                            </div>
                            <div class="metric-value" id="memory-info">加载中...</div>
                        </div>
                        <div class="col-md-6">
                            <div class="metric-label">交换内存</div>
                            <div class="progress">
                                <div id="swap-progress" class="progress-bar bg-warning" role="progressbar" style="width: 0%"></div>
                            </div>
                            <div class="metric-value" id="swap-info">加载中...</div>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="memory-chart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 磁盘监控 -->
        <div class="col-md-6">
            <div class="card monitor-card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-hdd me-2"></i>磁盘监控
                    </h5>
                    <button class="btn btn-sm btn-light refresh-btn" onclick="refreshDisk()">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
                <div class="card-body">
                    <div id="disk-partitions">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="metric-label">磁盘IO</div>
                            <div class="metric-value" id="disk-io">加载中...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 网络监控 -->
        <div class="col-md-6">
            <div class="card monitor-card">
                <div class="card-header bg-danger text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-network-wired me-2"></i>网络监控
                    </h5>
                    <button class="btn btn-sm btn-light refresh-btn" onclick="refreshNetwork()">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
                <div class="card-body">
                    <div id="network-interfaces">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="metric-label">网络IO</div>
                            <div class="metric-value" id="network-io">加载中...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 进程监控 -->
        <div class="col-12">
            <div class="card monitor-card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tasks me-2"></i>进程监控
                    </h5>
                    <button class="btn btn-sm btn-light refresh-btn" onclick="refreshProcesses()">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover process-table">
                            <thead>
                                <tr>
                                    <th>PID</th>
                                    <th>名称</th>
                                    <th>用户</th>
                                    <th>CPU %</th>
                                    <th>内存 %</th>
                                    <th>创建时间</th>
                                </tr>
                            </thead>
                            <tbody id="process-list">
                                <tr>
                                    <td colspan="6" class="text-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">加载中...</span>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // 图表对象
    let cpuChart, memoryChart;
    
    // 图表数据
    const cpuData = {
        labels: [],
        datasets: [{
            label: 'CPU使用率 (%)',
            data: [],
            borderColor: 'rgba(255, 99, 132, 1)',
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            borderWidth: 2,
            tension: 0.1
        }]
    };
    
    const memoryData = {
        labels: [],
        datasets: [{
            label: '内存使用率 (%)',
            data: [],
            borderColor: 'rgba(75, 192, 192, 1)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            borderWidth: 2,
            tension: 0.1
        }]
    };
    
    // 图表配置
    const chartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                max: 100
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    };
    
    // 初始化图表
    function initCharts() {
        const cpuCtx = document.getElementById('cpu-chart').getContext('2d');
        const memoryCtx = document.getElementById('memory-chart').getContext('2d');
        
        cpuChart = new Chart(cpuCtx, {
            type: 'line',
            data: cpuData,
            options: chartOptions
        });
        
        memoryChart = new Chart(memoryCtx, {
            type: 'line',
            data: memoryData,
            options: chartOptions
        });
    }
    
    // 更新图表数据
    function updateCharts(cpuPercent, memoryPercent) {
        const now = new Date();
        const timeLabel = now.getHours() + ':' + now.getMinutes() + ':' + now.getSeconds();
        
        // 更新CPU图表
        cpuData.labels.push(timeLabel);
        cpuData.datasets[0].data.push(cpuPercent);
        
        // 限制数据点数量
        if (cpuData.labels.length > 10) {
            cpuData.labels.shift();
            cpuData.datasets[0].data.shift();
        }
        
        cpuChart.update();
        
        // 更新内存图表
        memoryData.labels.push(timeLabel);
        memoryData.datasets[0].data.push(memoryPercent);
        
        // 限制数据点数量
        if (memoryData.labels.length > 10) {
            memoryData.labels.shift();
            memoryData.datasets[0].data.shift();
        }
        
        memoryChart.update();
    }
    
    // 获取系统信息
    function getSystemInfo() {
        fetch('/api/monitor/system')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const info = data.data;
                    document.getElementById('os-info').textContent = `${info.os} ${info.os_version}`;
                    document.getElementById('hostname').textContent = info.hostname;
                    document.getElementById('processor').textContent = info.processor;
                    document.getElementById('python-version').textContent = info.python_version;
                    document.getElementById('boot-time').textContent = info.boot_time;
                    document.getElementById('architecture').textContent = info.architecture;
                }
            })
            .catch(error => console.error('获取系统信息失败:', error));
    }
    
    // 获取CPU信息
    function getCPUInfo() {
        fetch('/api/monitor/cpu')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const info = data.data;
                    
                    // 更新CPU使用率
                    const cpuPercent = info.percent;
                    document.getElementById('cpu-progress').style.width = `${cpuPercent}%`;
                    document.getElementById('cpu-percent').textContent = `${cpuPercent}%`;
                    
                    // 更新CPU频率
                    const freq = info.freq;
                    document.getElementById('cpu-freq').textContent = 
                        `当前: ${freq.current}MHz, 最小: ${freq.min}MHz, 最大: ${freq.max}MHz`;
                    
                    // 更新CPU核心数
                    const count = info.count;
                    document.getElementById('cpu-count').textContent = 
                        `物理核心: ${count.physical}, 逻辑核心: ${count.logical}`;
                    
                    // 更新CPU时间
                    const times = info.times;
                    document.getElementById('cpu-times').textContent = 
                        `用户: ${times.user}s, 系统: ${times.system}s, 空闲: ${times.idle}s`;
                    
                    // 更新图表
                    updateCharts(cpuPercent, parseFloat(document.getElementById('memory-progress').style.width) || 0);
                }
            })
            .catch(error => console.error('获取CPU信息失败:', error));
    }
    
    // 获取内存信息
    function getMemoryInfo() {
        fetch('/api/monitor/memory')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const info = data.data;
                    const memory = info.memory;
                    const swap = info.swap;
                    
                    // 更新物理内存
                    document.getElementById('memory-progress').style.width = `${memory.percent}%`;
                    document.getElementById('memory-info').textContent = 
                        `总计: ${memory.total}GB, 已用: ${memory.used}GB, 可用: ${memory.available}GB, 使用率: ${memory.percent}%`;
                    
                    // 更新交换内存
                    document.getElementById('swap-progress').style.width = `${swap.percent}%`;
                    document.getElementById('swap-info').textContent = 
                        `总计: ${swap.total}GB, 已用: ${swap.used}GB, 可用: ${swap.free}GB, 使用率: ${swap.percent}%`;
                    
                    // 更新图表
                    updateCharts(parseFloat(document.getElementById('cpu-progress').style.width) || 0, memory.percent);
                }
            })
            .catch(error => console.error('获取内存信息失败:', error));
    }
    
    // 获取磁盘信息
    function getDiskInfo() {
        fetch('/api/monitor/disk')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const info = data.data;
                    const partitions = info.partitions;
                    const io = info.io;
                    
                    // 更新磁盘分区
                    let partitionsHtml = '';
                    partitions.forEach(partition => {
                        partitionsHtml += `
                            <div class="mb-3">
                                <h6>${partition.mountpoint} (${partition.device})</h6>
                                <div class="progress mb-2">
                                    <div class="progress-bar bg-warning" role="progressbar" style="width: ${partition.percent}%"></div>
                                </div>
                                <p>总计: ${partition.total}GB, 已用: ${partition.used}GB, 可用: ${partition.free}GB, 使用率: ${partition.percent}%</p>
                            </div>
                        `;
                    });
                    document.getElementById('disk-partitions').innerHTML = partitionsHtml;
                    
                    // 更新磁盘IO
                    document.getElementById('disk-io').textContent = 
                        `读取: ${io.read_bytes}MB, 写入: ${io.write_bytes}MB, 读取次数: ${io.read_count}, 写入次数: ${io.write_count}`;
                }
            })
            .catch(error => console.error('获取磁盘信息失败:', error));
    }
    
    // 获取网络信息
    function getNetworkInfo() {
        fetch('/api/monitor/network')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const info = data.data;
                    const interfaces = info.interfaces;
                    const io = info.io;
                    
                    // 更新网络接口
                    let interfacesHtml = '';
                    interfaces.forEach(interface => {
                        let addressesHtml = '';
                        interface.addresses.forEach(address => {
                            if (address.address) {
                                addressesHtml += `<p>${address.address}</p>`;
                            }
                        });
                        
                        interfacesHtml += `
                            <div class="mb-3">
                                <h6>${interface.name}</h6>
                                <div>${addressesHtml}</div>
                            </div>
                        `;
                    });
                    document.getElementById('network-interfaces').innerHTML = interfacesHtml;
                    
                    // 更新网络IO
                    document.getElementById('network-io').textContent = 
                        `发送: ${io.bytes_sent}MB, 接收: ${io.bytes_recv}MB, 发送包: ${io.packets_sent}, 接收包: ${io.packets_recv}`;
                }
            })
            .catch(error => console.error('获取网络信息失败:', error));
    }
    
    // 获取进程信息
    function getProcesses() {
        fetch('/api/monitor/processes')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const processes = data.data;
                    
                    // 更新进程列表
                    let processesHtml = '';
                    processes.forEach(process => {
                        processesHtml += `
                            <tr>
                                <td>${process.pid}</td>
                                <td>${process.name}</td>
                                <td>${process.username || 'N/A'}</td>
                                <td>${process.cpu_percent.toFixed(1)}%</td>
                                <td>${process.memory_percent.toFixed(1)}%</td>
                                <td>${process.create_time}</td>
                            </tr>
                        `;
                    });
                    document.getElementById('process-list').innerHTML = processesHtml;
                }
            })
            .catch(error => console.error('获取进程信息失败:', error));
    }
    
    // 刷新所有信息
    function refreshAll() {
        getSystemInfo();
        getCPUInfo();
        getMemoryInfo();
        getDiskInfo();
        getNetworkInfo();
        getProcesses();
    }
    
    // 刷新CPU信息
    function refreshCPU() {
        getCPUInfo();
    }
    
    // 刷新内存信息
    function refreshMemory() {
        getMemoryInfo();
    }
    
    // 刷新磁盘信息
    function refreshDisk() {
        getDiskInfo();
    }
    
    // 刷新网络信息
    function refreshNetwork() {
        getNetworkInfo();
    }
    
    // 刷新进程信息
    function refreshProcesses() {
        getProcesses();
    }
    
    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        initCharts();
        refreshAll();
        
        // 设置定时刷新
        setInterval(refreshAll, 5000);
    });
</script>
{% endblock %} 