{% extends "base.html" %}

{% block title %}MQTT消息转发记录{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-exchange-alt"></i>
                        MQTT消息转发记录
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-sm btn-primary" onclick="refreshRecords()">
                            <i class="fas fa-sync-alt"></i> 刷新
                        </button>
                        <button type="button" class="btn btn-sm btn-info" onclick="toggleAutoRefresh()">
                            <i class="fas fa-play" id="autoRefreshIcon"></i>
                            <span id="autoRefreshText">开启自动刷新</span>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 状态和统计信息 -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-info">
                                    <i class="fas fa-server"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">服务状态</span>
                                    <span class="info-box-number" id="serviceStatus">检查中...</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-success">
                                    <i class="fas fa-check"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">成功转发</span>
                                    <span class="info-box-number" id="successCount">0</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-warning">
                                    <i class="fas fa-times"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">失败转发</span>
                                    <span class="info-box-number" id="failedCount">0</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box">
                                <span class="info-box-icon bg-primary">
                                    <i class="fas fa-devices"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">目标设备</span>
                                    <span class="info-box-number" id="deviceCount">0</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 筛选控件 -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="deviceFilter">设备ID筛选:</label>
                            <select class="form-control" id="deviceFilter" onchange="refreshRecords()">
                                <option value="">所有设备</option>
                                <option value="100001051">100001051</option>
                                <option value="100001054">100001054</option>
                                <option value="100001055">100001055</option>
                                <option value="100001061">100001061</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="limitFilter">显示数量:</label>
                            <select class="form-control" id="limitFilter" onchange="refreshRecords()">
                                <option value="50">50条</option>
                                <option value="100" selected>100条</option>
                                <option value="200">200条</option>
                                <option value="500">500条</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label>&nbsp;</label>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="autoScrollCheck" checked>
                                <label class="form-check-label" for="autoScrollCheck">
                                    自动滚动到最新消息
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- 转发记录表格 -->
                    <div class="table-responsive" style="max-height: 600px; overflow-y: auto;" id="recordsContainer">
                        <table class="table table-striped table-hover">
                            <thead class="thead-dark">
                                <tr>
                                    <th>时间</th>
                                    <th>设备ID</th>
                                    <th>转发方向</th>
                                    <th>源主题</th>
                                    <th>目标主题</th>
                                    <th>消息长度</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="recordsTableBody">
                                <tr>
                                    <td colspan="8" class="text-center">
                                        <i class="fas fa-spinner fa-spin"></i> 加载中...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 消息详情模态框 -->
<div class="modal fade" id="messageDetailModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">消息详情</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <strong>设备ID:</strong> <span id="detailDeviceId"></span>
                    </div>
                    <div class="col-md-6">
                        <strong>时间:</strong> <span id="detailTimestamp"></span>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-6">
                        <strong>转发方向:</strong> <span id="detailDirection"></span>
                    </div>
                    <div class="col-md-6">
                        <strong>状态:</strong> <span id="detailStatus"></span>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-12">
                        <strong>源主题:</strong> <span id="detailSourceTopic"></span>
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-12">
                        <strong>目标主题:</strong> <span id="detailTargetTopic"></span>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <strong>消息内容 (16进制):</strong>
                        <textarea class="form-control mt-2" id="detailPayloadHex" rows="6" readonly></textarea>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<script>
let autoRefreshInterval = null;
let isAutoRefreshEnabled = false;

// 页面加载完成后初始化
$(document).ready(function() {
    refreshStatus();
    refreshStats();
    refreshRecords();
});

// 刷新服务状态
function refreshStatus() {
    $.get('/mqtt_forwarder/api/status')
        .done(function(response) {
            if (response.success) {
                const status = response.status;
                $('#serviceStatus').text(status.running ? '运行中' : '已停止');
                $('#deviceCount').text(status.device_count);
                
                // 更新设备筛选选项
                const deviceFilter = $('#deviceFilter');
                const currentValue = deviceFilter.val();
                deviceFilter.find('option:not(:first)').remove();
                
                if (status.target_devices && status.target_devices.length > 0) {
                    status.target_devices.forEach(function(deviceId) {
                        deviceFilter.append(`<option value="${deviceId}">${deviceId}</option>`);
                    });
                }
                
                deviceFilter.val(currentValue);
            }
        })
        .fail(function() {
            $('#serviceStatus').text('获取失败');
        });
}

// 刷新统计信息
function refreshStats() {
    $.get('/mqtt_forwarder/api/stats')
        .done(function(response) {
            if (response.success) {
                const stats = response.stats;
                $('#successCount').text(stats.successful_messages);
                $('#failedCount').text(stats.failed_messages);
            }
        })
        .fail(function() {
            $('#successCount').text('获取失败');
            $('#failedCount').text('获取失败');
        });
}

// 刷新转发记录
function refreshRecords() {
    const deviceId = $('#deviceFilter').val();
    const limit = $('#limitFilter').val();
    
    const params = {
        limit: limit,
        offset: 0
    };
    
    if (deviceId) {
        params.device_id = deviceId;
    }
    
    $.get('/mqtt_forwarder/api/records', params)
        .done(function(response) {
            if (response.success) {
                updateRecordsTable(response.records);
                
                // 自动滚动到最新消息
                if ($('#autoScrollCheck').is(':checked')) {
                    const container = $('#recordsContainer');
                    container.scrollTop(0);
                }
            } else {
                showError('获取转发记录失败: ' + response.error);
            }
        })
        .fail(function() {
            showError('获取转发记录失败');
        });
}

// 更新记录表格
function updateRecordsTable(records) {
    const tbody = $('#recordsTableBody');
    tbody.empty();
    
    if (records.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="8" class="text-center text-muted">暂无转发记录</td>
            </tr>
        `);
        return;
    }
    
    records.forEach(function(record) {
        const statusBadge = record.success ? 
            '<span class="badge badge-success">成功</span>' : 
            '<span class="badge badge-danger">失败</span>';
            
        const directionBadge = record.direction === 'alicloud→emqx' ?
            '<span class="badge badge-info">阿里云→EMQX</span>' :
            '<span class="badge badge-warning">EMQX→阿里云</span>';
            
        tbody.append(`
            <tr>
                <td>${record.timestamp || '未知'}</td>
                <td>${record.device_id}</td>
                <td>${directionBadge}</td>
                <td class="text-truncate" style="max-width: 200px;" title="${record.source_topic}">${record.source_topic}</td>
                <td class="text-truncate" style="max-width: 200px;" title="${record.target_topic}">${record.target_topic}</td>
                <td>${record.payload_length} 字节</td>
                <td>${statusBadge}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="showMessageDetail(${JSON.stringify(record).replace(/"/g, '&quot;')})">
                        <i class="fas fa-eye"></i> 详情
                    </button>
                </td>
            </tr>
        `);
    });
}

// 显示消息详情
function showMessageDetail(record) {
    $('#detailDeviceId').text(record.device_id);
    $('#detailTimestamp').text(record.timestamp || '未知');
    $('#detailDirection').text(record.direction);
    $('#detailStatus').text(record.success ? '成功' : '失败');
    $('#detailSourceTopic').text(record.source_topic);
    $('#detailTargetTopic').text(record.target_topic);
    $('#detailPayloadHex').val(record.payload_hex || '');
    
    $('#messageDetailModal').modal('show');
}

// 切换自动刷新
function toggleAutoRefresh() {
    if (isAutoRefreshEnabled) {
        // 停止自动刷新
        clearInterval(autoRefreshInterval);
        autoRefreshInterval = null;
        isAutoRefreshEnabled = false;
        $('#autoRefreshIcon').removeClass('fa-pause').addClass('fa-play');
        $('#autoRefreshText').text('开启自动刷新');
    } else {
        // 开启自动刷新
        autoRefreshInterval = setInterval(function() {
            refreshRecords();
            refreshStats();
        }, 5000); // 每5秒刷新一次
        isAutoRefreshEnabled = true;
        $('#autoRefreshIcon').removeClass('fa-play').addClass('fa-pause');
        $('#autoRefreshText').text('停止自动刷新');
    }
}

// 显示错误消息
function showError(message) {
    // 这里可以使用更好的通知组件
    alert('错误: ' + message);
}
</script>
{% endblock %}
