{% extends "base.html" %}

{% block title %}{{ download.name }} - 付费下载{% endblock %}

{% block content %}
<div class="container mt-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('paid_download.list_downloads') }}">付费下载</a></li>
            <li class="breadcrumb-item active">{{ download.name }}</li>
        </ol>
    </nav>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <h1 class="card-title">{{ download.name }}</h1>
                    <p class="text-muted">
                        创建时间：{{ download.created_at.strftime('%Y-%m-%d %H:%M') }}
                        {% if download.download_count > 0 %}
                        | 下载次数：{{ download.download_count }}
                        {% endif %}
                    </p>
                    <hr>
                    <div class="download-description">
                        {{ download.description | safe }}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">下载信息</h5>
                    <p class="card-text">
                        文件大小：{{ (download.file_size / 1024 / 1024) | round(2) }} MB
                    </p>
                    <p class="card-text">
                        价格：<span class="text-danger">￥{{ "%.2f"|format(download.price) }}</span>
                    </p>
                    
                    {% if has_purchased %}
                    <a href="{{ url_for('paid_download.download_file', download_id=download.id) }}" 
                       class="btn btn-primary btn-block">
                        下载文件
                    </a>
                    {% else %}
                    <button onclick="purchaseDownload()" class="btn btn-primary btn-block">
                        立即购买
                    </button>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 支付方式选择模态框 -->
<div class="modal fade" id="paymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">选择支付方式</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>支付金额：<span class="text-danger">￥<span id="paymentAmount">0.00</span></span></label>
                </div>
                <div class="form-group">
                    <label>支付方式：</label>
                    <div class="custom-control custom-radio">
                        <input type="radio" id="alipay" name="paymentMethod" value="alipay" class="custom-control-input">
                        <label class="custom-control-label" for="alipay">支付宝</label>
                    </div>
                    <div class="custom-control custom-radio">
                        <input type="radio" id="wechat" name="paymentMethod" value="wechat" class="custom-control-input">
                        <label class="custom-control-label" for="wechat">微信支付</label>
                    </div>
                    <div class="custom-control custom-radio">
                        <input type="radio" id="simulated" name="paymentMethod" value="simulated" class="custom-control-input" checked>
                        <label class="custom-control-label" for="simulated">模拟支付（测试用）</label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="processPayment()">确认支付</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentOrderId = null;

function purchaseDownload() {
    if (!confirm('确定要购买此下载吗？')) {
        return;
    }
    
    const formData = new FormData();
    formData.append('download_id', {{ download.id }});
    
    fetch("{{ url_for('paid_download.create_order') }}", {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            currentOrderId = data.order_id;
            document.getElementById('paymentAmount').textContent = data.amount.toFixed(2);
            $('#paymentModal').modal('show');
        } else {
            alert(data.message || '购买失败');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('购买失败，请稍后重试');
    });
}

function processPayment() {
    if (!currentOrderId) {
        alert('订单信息错误');
        return;
    }
    
    const paymentMethod = document.querySelector('input[name="paymentMethod"]:checked').value;
    const formData = new FormData();
    formData.append('order_id', currentOrderId);
    formData.append('payment_method', paymentMethod);
    
    fetch("{{ url_for('paid_download.pay_order') }}", {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('支付成功！');
            location.reload();
        } else {
            alert(data.message || '支付失败');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('支付失败，请稍后重试');
    });
}
</script>
{% endblock %} 