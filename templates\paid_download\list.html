{% extends "base.html" %}

{% block title %}付费下载列表{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">付费下载列表</h2>
        {% if current_user.is_admin %}
        <a href="{{ url_for('paid_download.create_download') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> 添加下载
        </a>
        {% endif %}
    </div>

    <div class="row">
        {% for download in downloads %}
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card h-100 shadow-sm">
                <div class="card-body">
                    <h5 class="card-title">{{ download.name }}</h5>
                    <p class="card-text text-muted">
                        <small>
                            <i class="fas fa-clock"></i> {{ download.created_at.strftime('%Y-%m-%d') }}
                        </small>
                    </p>
                    <p class="card-text">{{ download.description[:100] }}{% if download.description|length > 100 %}...{% endif %}</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-primary">
                            <i class="fas fa-download"></i> {{ download.download_count }} 次下载
                        </span>
                        <span class="text-success">
                            <i class="fas fa-yen-sign"></i> {{ "%.2f"|format(download.price) }}
                        </span>
                    </div>
                </div>
                <div class="card-footer bg-transparent border-top-0">
                    <a href="{{ url_for('paid_download.download_detail', download_id=download.id) }}" 
                       class="btn btn-outline-primary btn-sm w-100">
                        查看详情
                    </a>
                </div>
            </div>
        </div>
        {% else %}
        <div class="col-12">
            <div class="alert alert-info">
                暂无付费下载内容
            </div>
        </div>
        {% endfor %}
    </div>

    {% if downloads %}
    <nav aria-label="Page navigation" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if pagination.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('paid_download.list_downloads', page=pagination.prev_num) }}">上一页</a>
            </li>
            {% endif %}
            
            {% for page in pagination.iter_pages() %}
                {% if page %}
                    <li class="page-item {% if page == pagination.page %}active{% endif %}">
                        <a class="page-link" href="{{ url_for('paid_download.list_downloads', page=page) }}">{{ page }}</a>
                    </li>
                {% else %}
                    <li class="page-item disabled"><span class="page-link">...</span></li>
                {% endif %}
            {% endfor %}
            
            {% if pagination.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('paid_download.list_downloads', page=pagination.next_num) }}">下一页</a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
</div>
{% endblock %}