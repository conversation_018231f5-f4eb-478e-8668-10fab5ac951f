{% extends "base.html" %}

{% block title %}下载订单记录{% endblock %}

{% block content %}
<div class="container py-4">
    <h2 class="mb-4">下载订单记录</h2>
    
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>订单号</th>
                            <th>下载内容</th>
                            <th>金额</th>
                            <th>支付方式</th>
                            <th>订单状态</th>
                            <th>创建时间</th>
                            <th>支付时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for order in orders.items %}
                        <tr>
                            <td>{{ order.order_no }}</td>
                            <td>
                                <a href="{{ url_for('paid_download.download_detail', download_id=order.paid_download.id) }}">
                                    {{ order.paid_download.name }}
                                </a>
                            </td>
                            <td class="text-danger">￥{{ "%.2f"|format(order.amount) }}</td>
                            <td>
                                {% if order.payment_method == 'alipay' %}
                                支付宝
                                {% elif order.payment_method == 'wechat' %}
                                微信支付
                                {% elif order.payment_method == 'simulated' %}
                                模拟支付
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td>
                                {% if order.status == 'pending' %}
                                <span class="badge badge-warning">待支付</span>
                                {% elif order.status == 'paid' %}
                                <span class="badge badge-success">已支付</span>
                                {% else %}
                                <span class="badge badge-secondary">{{ order.status }}</span>
                                {% endif %}
                            </td>
                            <td>{{ order.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            <td>
                                {% if order.payment_time %}
                                {{ order.payment_time.strftime('%Y-%m-%d %H:%M') }}
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td>
                                {% if order.status == 'paid' %}
                                <a href="{{ url_for('paid_download.download_file', download_id=order.paid_download.id) }}" 
                                   class="btn btn-sm btn-primary">
                                    下载文件
                                </a>
                                {% elif order.status == 'pending' %}
                                <button onclick="showPaymentModal('{{ order.id }}', {{ order.amount }})" 
                                        class="btn btn-sm btn-warning">
                                    去支付
                                </button>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            {% if orders.pages > 1 %}
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% for page in orders.iter_pages() %}
                        {% if page %}
                            <li class="page-item {% if page == orders.page %}active{% endif %}">
                                <a class="page-link" href="{{ url_for('paid_download.order_list', page=page) }}">
                                    {{ page }}
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                        {% endif %}
                    {% endfor %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>

<!-- 支付方式选择模态框 -->
<div class="modal fade" id="paymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">选择支付方式</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>支付金额：<span class="text-danger">￥<span id="paymentAmount">0.00</span></span></label>
                </div>
                <div class="form-group">
                    <label>支付方式：</label>
                    <div class="custom-control custom-radio">
                        <input type="radio" id="alipay" name="paymentMethod" value="alipay" class="custom-control-input">
                        <label class="custom-control-label" for="alipay">支付宝</label>
                    </div>
                    <div class="custom-control custom-radio">
                        <input type="radio" id="wechat" name="paymentMethod" value="wechat" class="custom-control-input">
                        <label class="custom-control-label" for="wechat">微信支付</label>
                    </div>
                    <div class="custom-control custom-radio">
                        <input type="radio" id="simulated" name="paymentMethod" value="simulated" class="custom-control-input" checked>
                        <label class="custom-control-label" for="simulated">模拟支付（测试用）</label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="processPayment()">确认支付</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentOrderId = null;

function showPaymentModal(orderId, amount) {
    currentOrderId = orderId;
    document.getElementById('paymentAmount').textContent = amount.toFixed(2);
    $('#paymentModal').modal('show');
}

function processPayment() {
    if (!currentOrderId) {
        alert('订单信息错误');
        return;
    }
    
    const paymentMethod = document.querySelector('input[name="paymentMethod"]:checked').value;
    const formData = new FormData();
    formData.append('order_id', currentOrderId);
    formData.append('payment_method', paymentMethod);
    
    fetch("{{ url_for('paid_download.pay_order') }}", {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('支付成功！');
            location.reload();
        } else {
            alert(data.message || '支付失败');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('支付失败，请稍后重试');
    });
}
</script>
{% endblock %} 