{% extends "base.html" %}

{% block title %}功率历史数据 - {{ device.device_id }}{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .chart-container {
        position: relative;
        height: 400px;
        width: 100%;
        margin-bottom: 2rem;
    }
    .channel-badge {
        width: 30px;
        height: 30px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        margin-right: 8px;
        border-radius: 50%;
    }
    .legend-item {
        display: flex;
        align-items: center;
        margin-right: 15px;
        margin-bottom: 10px;
    }
    .legend-container {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt text-warning me-2"></i>功率历史数据 - {{ device.device_id }}
                        <span class="badge bg-info ms-2">{{ device.device_remark or '无备注' }}</span>
                    </h5>
                    <div>
                        <a href="{{ url_for('device_parameters.device_parameters', id=device.id) }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> 返回参数页面
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 筛选条件 -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0"><i class="fas fa-filter me-2"></i>筛选条件</h6>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <!-- 日期选择器 -->
                                <div class="col-md-4">
                                    <label for="datePicker" class="form-label">日期</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                        <input type="date" class="form-control" id="datePicker" max="{{ today_date }}" value="{{ today_date }}">
                                    </div>
                                </div>

                                <!-- 通道选择 -->
                                <div class="col-md-4">
                                    <label for="channelSelect" class="form-label">通道选择</label>
                                    <select class="form-select" id="channelSelect" multiple>
                                        <option value="all" selected>全部通道</option>
                                        <option value="channel_1">通道 1</option>
                                        <option value="channel_2">通道 2</option>
                                        <option value="channel_3">通道 3</option>
                                        <option value="channel_4">通道 4</option>
                                        <option value="channel_5">通道 5</option>
                                        <option value="channel_6">通道 6</option>
                                        <option value="channel_7">通道 7</option>
                                        <option value="channel_8">通道 8</option>
                                        <option value="channel_9">通道 9</option>
                                        <option value="channel_10">通道 10</option>
                                    </select>
                                    <div class="form-text">按住Ctrl键可多选</div>
                                </div>

                                <!-- 时间范围 -->
                                <div class="col-md-4">
                                    <label for="timeRangeSelect" class="form-label">时间范围</label>
                                    <select class="form-select" id="timeRangeSelect">
                                        <option value="all" selected>全天</option>
                                        <option value="morning">上午 (6:00-12:00)</option>
                                        <option value="afternoon">下午 (12:00-18:00)</option>
                                        <option value="evening">晚上 (18:00-24:00)</option>
                                        <option value="night">凌晨 (0:00-6:00)</option>
                                    </select>
                                </div>

                                <!-- 查询按钮 -->
                                <div class="col-12 text-end">
                                    <button class="btn btn-primary" onclick="loadPowerData()">
                                        <i class="fas fa-search me-1"></i> 查询数据
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="resetFilters()">
                                        <i class="fas fa-redo me-1"></i> 重置筛选
                                    </button>
                                    <button class="btn btn-success ms-2" id="exportDataBtn">
                                        <i class="fas fa-download me-1"></i> 导出数据
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 加载中提示 -->
                    <div id="loadingIndicator" class="text-center py-5 d-none">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载功率数据，请稍候...</p>
                    </div>

                    <!-- 图表容器 -->
                    <div id="chartContainer" class="d-none">
                        <!-- 数据摘要 -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h6 class="card-title"><i class="fas fa-tachometer-alt me-2"></i>平均功率</h6>
                                        <h3 class="mb-0" id="avgPower">-- W</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <h6 class="card-title"><i class="fas fa-arrow-up me-2"></i>最大功率</h6>
                                        <h3 class="mb-0" id="maxPower">-- W</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body">
                                        <h6 class="card-title"><i class="fas fa-arrow-down me-2"></i>最小功率</h6>
                                        <h3 class="mb-0" id="minPower">-- W</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body">
                                        <h6 class="card-title"><i class="fas fa-chart-line me-2"></i>数据点数</h6>
                                        <h3 class="mb-0" id="dataPoints">--</h3>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 图表控制 -->
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="toggleChartType('line')">
                                    <i class="fas fa-chart-line me-1"></i> 折线图
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="toggleChartType('bar')">
                                    <i class="fas fa-chart-bar me-1"></i> 柱状图
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" id="resetZoomBtn">
                                    <i class="fas fa-search-minus me-1"></i> 重置缩放
                                </button>
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="form-check form-switch me-3">
                                    <input class="form-check-input" type="checkbox" id="smoothLines" checked>
                                    <label class="form-check-label" for="smoothLines">平滑曲线</label>
                                </div>
                                <div class="text-muted small">
                                    <i class="fas fa-info-circle me-1"></i> 可滚轮缩放、拖动平移
                                </div>
                            </div>
                        </div>

                        <!-- 图例 -->
                        <div class="legend-container mb-3" id="chartLegend"></div>

                        <!-- 图表 -->
                        <div class="chart-container">
                            <canvas id="powerChart"></canvas>
                        </div>

                        <!-- 图表说明 -->
                        <div class="mt-3 text-muted small">
                            <i class="fas fa-info-circle me-1"></i> 提示：可以点击图例切换显示/隐藏对应通道，双击图表可以重置缩放。
                        </div>
                    </div>

                    <!-- 无数据提示 -->
                    <div id="noDataMessage" class="alert alert-info d-none">
                        <i class="fas fa-info-circle me-2"></i> 所选日期没有功率数据。请确保调试脚本已运行并收集了数据。
                    </div>

                    <!-- 错误提示 -->
                    <div id="errorMessage" class="alert alert-danger d-none">
                        获取功率数据失败，请重试。
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js"></script>
<!-- 添加Chart.js必要的适配器和插件 -->
<script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-moment"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-zoom"></script>
<script>
    // 图表对象
    let powerChart;
    let currentChartType = 'line';

    // 图表颜色
    const channelColors = [
        'rgb(255, 99, 132)',   // 红色
        'rgb(54, 162, 235)',   // 蓝色
        'rgb(255, 206, 86)',   // 黄色
        'rgb(75, 192, 192)',   // 青色
        'rgb(153, 102, 255)',  // 紫色
        'rgb(255, 159, 64)',   // 橙色
        'rgb(199, 199, 199)',  // 灰色
        'rgb(83, 180, 50)',    // 绿色
        'rgb(244, 67, 54)',    // 深红色
        'rgb(156, 39, 176)'    // 深紫色
    ];

    // 注册Chart.js插件
    Chart.register(ChartZoom);

    // 标记有数据的日期
    function markAvailableDates() {
        fetch('/debug_script/api/device/{{ device.id }}/data_dates')
            .then(response => response.json())
            .then(data => {
                const dateInput = document.getElementById('datePicker');
                const availableDates = data.dates;
                
                // 监听input事件来标记有数据的日期
                dateInput.addEventListener('input', function() {
                    if (availableDates.includes(this.value)) {
                        this.style.borderColor = '#28a745';
                        this.style.boxShadow = '0 0 0 0.2rem rgba(40, 167, 69, 0.25)';
                    } else {
                        this.style.borderColor = '';
                        this.style.boxShadow = '';
                    }
                });
            });
    }

    // 定义animateOnScroll函数，修复base.html中的引用错误
    function animateOnScroll() {
        // 空实现，仅用于防止错误
    }

    // 初始化页面
    document.addEventListener('DOMContentLoaded', function() {
        // 标记有数据的日期
        markAvailableDates();
        
        // 初始化平滑曲线开关事件
        document.getElementById('smoothLines').addEventListener('change', function() {
            if (powerChart) {
                const tension = this.checked ? 0.4 : 0;
                powerChart.data.datasets.forEach(dataset => {
                    dataset.tension = tension;
                });
                powerChart.update();
            }
        });

        // 为日期选择器添加变更事件
        document.getElementById('datePicker').addEventListener('change', function() {
            loadPowerData();
        });

        // 加载当天数据
        loadPowerData();
    });

    // 重置筛选条件
    function resetFilters() {
        // 重置日期为今天
        const today = new Date();
        const year = today.getFullYear();
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');
        document.getElementById('datePicker').value = `${year}-${month}-${day}`;

        // 重置通道选择为全部
        const channelSelect = document.getElementById('channelSelect');
        for (let i = 0; i < channelSelect.options.length; i++) {
            channelSelect.options[i].selected = (i === 0); // 只选中"全部通道"
        }

        // 重置时间范围为全天
        document.getElementById('timeRangeSelect').value = 'all';

        // 重新加载数据
        loadPowerData();
    }

    // 切换图表类型
    function toggleChartType(type) {
        if (powerChart && type !== currentChartType) {
            currentChartType = type;
            powerChart.config.type = type;

            // 根据图表类型调整样式
            if (type === 'line') {
                powerChart.data.datasets.forEach(dataset => {
                    dataset.borderWidth = 2;
                    dataset.pointRadius = 3;
                    dataset.pointHoverRadius = 5;
                    dataset.tension = document.getElementById('smoothLines').checked ? 0.4 : 0;
                });
            } else if (type === 'bar') {
                powerChart.data.datasets.forEach(dataset => {
                    dataset.borderWidth = 1;
                    dataset.borderColor = dataset.backgroundColor.replace('20', '');
                    dataset.backgroundColor = dataset.backgroundColor.replace('20', '80');
                });
            }

            powerChart.update();
        }
    }

    // 加载功率数据
    function loadPowerData() {
        // 获取选择的日期
        const dateInput = document.getElementById('datePicker');
        const date = dateInput.value;

        if (!date) {
            // 如果没有选择日期，设置为今天
            const today = new Date();
            const year = today.getFullYear();
            const month = String(today.getMonth() + 1).padStart(2, '0');
            const day = String(today.getDate()).padStart(2, '0');
            dateInput.value = `${year}-${month}-${day}`;
            alert('未选择日期，已自动设置为今天');
            return;
        }

        // 显示加载中
        document.getElementById('loadingIndicator').classList.remove('d-none');
        document.getElementById('chartContainer').classList.add('d-none');
        document.getElementById('noDataMessage').classList.add('d-none');
        document.getElementById('errorMessage').classList.add('d-none');

        // 发送请求获取功率数据
        fetch(`/debug_script/power_data/{{ device.id }}?date=${date}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('网络请求失败');
                }
                return response.json();
            })
            .then(data => {
                // 隐藏加载中
                document.getElementById('loadingIndicator').classList.add('d-none');

                if (data.error) {
                    // 显示错误信息
                    document.getElementById('errorMessage').textContent = '获取功率数据失败: ' + data.error;
                    document.getElementById('errorMessage').classList.remove('d-none');
                    return;
                }

                // 检查是否有数据
                const powerData = data.power_data;
                const hasData = Object.keys(powerData).length > 0 &&
                                Object.values(powerData).some(channel => channel.length > 0);

                if (!hasData) {
                    // 显示无数据提示
                    document.getElementById('noDataMessage').classList.remove('d-none');
                    return;
                }

                // 应用筛选条件
                const filteredData = filterPowerData(powerData);

                // 更新图表
                updatePowerChart(filteredData);

                // 显示图表容器
                document.getElementById('chartContainer').classList.remove('d-none');
            })
            .catch(error => {
                console.error('获取功率数据失败:', error);
                // 隐藏加载中，显示错误信息
                document.getElementById('loadingIndicator').classList.add('d-none');
                document.getElementById('errorMessage').textContent = '获取功率数据失败: ' + error.message;
                document.getElementById('errorMessage').classList.remove('d-none');
            });
    }

    // 筛选功率数据
    function filterPowerData(powerData) {
        // 获取筛选条件
        const channelSelect = document.getElementById('channelSelect');
        const selectedChannels = Array.from(channelSelect.selectedOptions).map(option => option.value);
        const timeRange = document.getElementById('timeRangeSelect').value;

        // 如果选择了"全部通道"，则不筛选通道
        const filterChannels = !selectedChannels.includes('all');

        // 创建筛选后的数据对象
        const filteredData = {};

        // 筛选通道
        Object.keys(powerData).forEach(channel => {
            if (!filterChannels || selectedChannels.includes(channel)) {
                // 筛选时间范围
                let channelData = powerData[channel];

                if (timeRange !== 'all') {
                    channelData = channelData.filter(point => {
                        const hour = new Date(point.time).getHours();

                        switch (timeRange) {
                            case 'morning':
                                return hour >= 6 && hour < 12;
                            case 'afternoon':
                                return hour >= 12 && hour < 18;
                            case 'evening':
                                return hour >= 18 && hour < 24;
                            case 'night':
                                return hour >= 0 && hour < 6;
                            default:
                                return true;
                        }
                    });
                }

                // 只有在有数据的情况下才添加到筛选结果中
                if (channelData.length > 0) {
                    filteredData[channel] = channelData;
                }
            }
        });

        return filteredData;
    }

    // 计算数据统计信息
    function calculateStats(datasets) {
        let totalPoints = 0;
        let totalSum = 0;
        let maxPower = -Infinity;
        let minPower = Infinity;

        datasets.forEach(dataset => {
            const points = dataset.data;
            totalPoints += points.length;

            points.forEach(point => {
                const value = point.y;
                totalSum += value;
                maxPower = Math.max(maxPower, value);
                minPower = Math.min(minPower, value);
            });
        });

        const avgPower = totalPoints > 0 ? totalSum / totalPoints : 0;

        return {
            totalPoints,
            avgPower,
            maxPower: maxPower !== -Infinity ? maxPower : 0,
            minPower: minPower !== Infinity ? minPower : 0
        };
    }

    // 更新功率图表
    function updatePowerChart(powerData) {
        // 准备图表数据
        const datasets = [];
        const legendContainer = document.getElementById('chartLegend');
        legendContainer.innerHTML = '';

        // 处理每个通道的数据
        Object.keys(powerData).forEach((channel, index) => {
            if (channel.startsWith('channel_')) {
                const channelNumber = channel.split('_')[1];
                const color = channelColors[index % channelColors.length];

                // 准备数据点
                const dataPoints = powerData[channel].map(point => ({
                    x: new Date(point.time),
                    y: parseFloat(point.value)
                }));

                // 按时间排序数据点
                dataPoints.sort((a, b) => a.x - b.x);

                // 添加到数据集
                datasets.push({
                    label: `通道 ${channelNumber}`,
                    data: dataPoints,
                    borderColor: color,
                    backgroundColor: color + '20',
                    borderWidth: 2,
                    pointRadius: 3,
                    pointHoverRadius: 5,
                    tension: document.getElementById('smoothLines').checked ? 0.4 : 0
                });

                // 添加图例项
                const legendItem = document.createElement('div');
                legendItem.className = 'legend-item';
                legendItem.dataset.channel = channel;
                legendItem.innerHTML = `
                    <div class="channel-badge" style="background-color: ${color};">${channelNumber}</div>
                    <span>通道 ${channelNumber}</span>
                `;

                // 添加图例点击事件
                legendItem.addEventListener('click', function() {
                    const datasetIndex = datasets.findIndex(ds => ds.label === `通道 ${channelNumber}`);
                    if (datasetIndex !== -1) {
                        const meta = powerChart.getDatasetMeta(datasetIndex);
                        meta.hidden = !meta.hidden;

                        // 更新图例样式
                        this.classList.toggle('text-muted');
                        this.querySelector('.channel-badge').style.opacity = meta.hidden ? 0.5 : 1;

                        powerChart.update();
                    }
                });

                legendContainer.appendChild(legendItem);
            }
        });

        // 计算并更新统计信息
        const stats = calculateStats(datasets);
        document.getElementById('avgPower').textContent = stats.avgPower.toFixed(2) + ' W';
        document.getElementById('maxPower').textContent = stats.maxPower.toFixed(2) + ' W';
        document.getElementById('minPower').textContent = stats.minPower.toFixed(2) + ' W';
        document.getElementById('dataPoints').textContent = stats.totalPoints;

        // 销毁现有图表
        if (powerChart) {
            powerChart.destroy();
        }

        // 创建新图表
        const ctx = document.getElementById('powerChart').getContext('2d');
        powerChart = new Chart(ctx, {
            type: currentChartType,
            data: {
                datasets: datasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false
                },
                scales: {
                    x: {
                        type: 'time',
                        adapters: {
                            date: {
                                locale: 'zh-CN'
                            }
                        },
                        time: {
                            unit: 'hour',
                            displayFormats: {
                                hour: 'HH:mm'
                            },
                            tooltipFormat: 'YYYY-MM-DD HH:mm:ss'
                        },
                        title: {
                            display: true,
                            text: '时间'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: '功率 (W)'
                        },
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `${context.dataset.label}: ${context.parsed.y.toFixed(2)} W`;
                            }
                        }
                    },
                    zoom: {
                        pan: {
                            enabled: true,
                            mode: 'xy'
                        },
                        zoom: {
                            wheel: {
                                enabled: true
                            },
                            pinch: {
                                enabled: true
                            },
                            mode: 'xy'
                        }
                    }
                }
            }
        });

        // 添加双击重置缩放事件
        document.getElementById('powerChart').addEventListener('dblclick', function() {
            if (powerChart.resetZoom) {
                powerChart.resetZoom();
            }
        });

        // 添加重置缩放按钮事件
        document.getElementById('resetZoomBtn').addEventListener('click', function() {
            if (powerChart.resetZoom) {
                powerChart.resetZoom();
            }
        });
    }

    // 初始化数据导出功能
    document.addEventListener('DOMContentLoaded', function() {
        const exportBtn = document.getElementById('exportDataBtn');
        if (exportBtn) {
            exportBtn.setAttribute('data-device-id', '{{ device.id }}');

            if (window.DataExporter) {
                window.dataExporter = new DataExporter({{ device.id }});
                window.dataExporter.createExportButton = function() {};

                exportBtn.addEventListener('click', function() {
                    window.dataExporter.showExportModal();
                });
            }
        }
    });
</script>

<!-- 导出功能相关脚本 -->
<script src="{{ url_for('static', filename='js/data-interface-config.js') }}"></script>
<script src="{{ url_for('static', filename='js/optimized-data-loader.js') }}"></script>
<script src="{{ url_for('static', filename='js/data-export.js') }}"></script>
{% endblock %}
