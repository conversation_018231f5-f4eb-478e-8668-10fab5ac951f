{% extends "base.html" %}

{% block title %}温度历史数据 - {{ device.device_id }}{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .chart-container {
        position: relative;
        height: 400px;
        width: 100%;
        margin-bottom: 2rem;
    }
    .legend-item {
        display: flex;
        align-items: center;
        margin-right: 15px;
        margin-bottom: 10px;
    }
    .legend-container {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-thermometer-half text-danger me-2"></i>温度历史数据 - {{ device.device_id }}
                        <span class="badge bg-info ms-2">{{ device.device_remark or '无备注' }}</span>
                    </h5>
                    <div>
                        <a href="{{ url_for('device_parameters.device_parameters', id=device.id) }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> 返回参数页面
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 筛选条件 -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0"><i class="fas fa-filter me-2"></i>筛选条件</h6>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <!-- 日期选择器 -->
                                <div class="col-md-6">
                                    <label for="datePicker" class="form-label">日期</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                                        <input type="date" class="form-control" id="datePicker" max="{{ today_date }}" value="{{ today_date }}">
                                    </div>
                                </div>

                                <!-- 时间范围 -->
                                <div class="col-md-6">
                                    <label for="timeRangeSelect" class="form-label">时间范围</label>
                                    <select class="form-select" id="timeRangeSelect">
                                        <option value="all" selected>全天</option>
                                        <option value="morning">上午 (6:00-12:00)</option>
                                        <option value="afternoon">下午 (12:00-18:00)</option>
                                        <option value="evening">晚上 (18:00-24:00)</option>
                                        <option value="night">凌晨 (0:00-6:00)</option>
                                    </select>
                                </div>

                                <!-- 查询按钮 -->
                                <div class="col-12 text-end">
                                    <button class="btn btn-primary" onclick="loadTemperatureData()">
                                        <i class="fas fa-search me-1"></i> 查询数据
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="resetFilters()">
                                        <i class="fas fa-redo me-1"></i> 重置筛选
                                    </button>
                                    <button class="btn btn-success ms-2" id="exportDataBtn">
                                        <i class="fas fa-download me-1"></i> 导出数据
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 加载中提示 -->
                    <div id="loadingIndicator" class="text-center py-5 d-none">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载温度数据，请稍候...</p>
                    </div>

                    <!-- 图表容器 -->
                    <div id="chartContainer" class="d-none">
                        <!-- 数据摘要 -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h6 class="card-title"><i class="fas fa-calculator me-2"></i>平均温度</h6>
                                        <h3 class="mb-0" id="avgTemperature">-- °C</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-danger text-white">
                                    <div class="card-body">
                                        <h6 class="card-title"><i class="fas fa-arrow-up me-2"></i>最高温度</h6>
                                        <h3 class="mb-0" id="maxTemperature">-- °C</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body">
                                        <h6 class="card-title"><i class="fas fa-arrow-down me-2"></i>最低温度</h6>
                                        <h3 class="mb-0" id="minTemperature">-- °C</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body">
                                        <h6 class="card-title"><i class="fas fa-chart-line me-2"></i>数据点数</h6>
                                        <h3 class="mb-0" id="dataPoints">--</h3>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 图表控制 -->
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="toggleChartType('line')">
                                    <i class="fas fa-chart-line me-1"></i> 折线图
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="toggleChartType('bar')">
                                    <i class="fas fa-chart-bar me-1"></i> 柱状图
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" id="resetZoomBtn">
                                    <i class="fas fa-search-minus me-1"></i> 重置缩放
                                </button>
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="form-check form-switch me-3">
                                    <input class="form-check-input" type="checkbox" id="smoothLines" checked>
                                    <label class="form-check-label" for="smoothLines">平滑曲线</label>
                                </div>
                                <div class="text-muted small">
                                    <i class="fas fa-info-circle me-1"></i> 可滚轮缩放、拖动平移
                                </div>
                            </div>
                        </div>

                        <!-- 图例 -->
                        <div class="legend-container mb-3" id="chartLegend"></div>

                        <!-- 图表 -->
                        <div class="chart-container">
                            <canvas id="temperatureChart"></canvas>
                        </div>

                        <!-- 图表说明 -->
                        <div class="mt-3 text-muted small">
                            <i class="fas fa-info-circle me-1"></i> 提示：双击图表可以重置缩放。
                        </div>
                    </div>

                    <!-- 无数据提示 -->
                    <div id="noDataMessage" class="alert alert-info d-none">
                        <i class="fas fa-info-circle me-2"></i> 所选日期没有温度数据。请确保调试脚本已运行并收集了数据。
                    </div>

                    <!-- 错误提示 -->
                    <div id="errorMessage" class="alert alert-danger d-none">
                        获取温度数据失败，请重试。
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js"></script>
<!-- 添加Chart.js必要的适配器和插件 -->
<script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-moment"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-zoom"></script>
<script>
    // 图表对象
    let temperatureChart;
    let currentChartType = 'line';

    // 图表颜色
    const chartColor = 'rgb(255, 99, 132)';
    const chartColors = [
        'rgb(255, 99, 132)',   // 红色
        'rgb(54, 162, 235)',   // 蓝色
        'rgb(255, 206, 86)',   // 黄色
        'rgb(75, 192, 192)',   // 青色
        'rgb(153, 102, 255)',  // 紫色
        'rgb(255, 159, 64)'    // 橙色
    ];

    // 注册Chart.js插件
    Chart.register(ChartZoom);

    // 标记有数据的日期
    function markAvailableDates() {
        fetch('/debug_script/api/device/{{ device.id }}/data_dates')
            .then(response => response.json())
            .then(data => {
                const dateInput = document.getElementById('datePicker');
                const availableDates = data.dates;
                
                // 监听input事件来标记有数据的日期
                dateInput.addEventListener('input', function() {
                    if (availableDates.includes(this.value)) {
                        this.style.borderColor = '#28a745';
                        this.style.boxShadow = '0 0 0 0.2rem rgba(40, 167, 69, 0.25)';
                    } else {
                        this.style.borderColor = '';
                        this.style.boxShadow = '';
                    }
                });
            });
    }

    // 定义animateOnScroll函数，修复base.html中的引用错误
    function animateOnScroll() {
        // 空实现，仅用于防止错误
    }

    // 初始化页面
    document.addEventListener('DOMContentLoaded', function() {
        // 标记有数据的日期
        markAvailableDates();
        
        // 初始化平滑曲线开关事件
        document.getElementById('smoothLines').addEventListener('change', function() {
            if (temperatureChart) {
                const tension = this.checked ? 0.4 : 0;
                temperatureChart.data.datasets.forEach(dataset => {
                    dataset.tension = tension;
                });
                temperatureChart.update();
            }
        });

        // 为日期选择器添加变更事件
        document.getElementById('datePicker').addEventListener('change', function() {
            loadTemperatureData();
        });

        // 加载当天数据
        loadTemperatureData();
    });

    // 重置筛选条件
    function resetFilters() {
        // 重置日期为今天
        const today = new Date();
        const year = today.getFullYear();
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');
        document.getElementById('datePicker').value = `${year}-${month}-${day}`;

        // 重置时间范围为全天
        document.getElementById('timeRangeSelect').value = 'all';

        // 重新加载数据
        loadTemperatureData();
    }

    // 切换图表类型
    function toggleChartType(type) {
        if (temperatureChart && type !== currentChartType) {
            currentChartType = type;
            temperatureChart.config.type = type;

            // 根据图表类型调整样式
            if (type === 'line') {
                temperatureChart.data.datasets.forEach(dataset => {
                    dataset.borderWidth = 2;
                    dataset.pointRadius = 3;
                    dataset.pointHoverRadius = 5;
                    dataset.tension = document.getElementById('smoothLines').checked ? 0.4 : 0;
                });
            } else if (type === 'bar') {
                temperatureChart.data.datasets.forEach(dataset => {
                    dataset.borderWidth = 1;
                    dataset.borderColor = dataset.backgroundColor.replace('20', '');
                    dataset.backgroundColor = dataset.backgroundColor.replace('20', '80');
                });
            }

            temperatureChart.update();
        }
    }

    // 加载温度数据
    function loadTemperatureData() {
        // 获取选择的日期
        const dateInput = document.getElementById('datePicker');
        const date = dateInput.value;

        if (!date) {
            // 如果没有选择日期，设置为今天
            const today = new Date();
            const year = today.getFullYear();
            const month = String(today.getMonth() + 1).padStart(2, '0');
            const day = String(today.getDate()).padStart(2, '0');
            dateInput.value = `${year}-${month}-${day}`;
            alert('未选择日期，已自动设置为今天');
            return;
        }

        // 显示加载中
        document.getElementById('loadingIndicator').classList.remove('d-none');
        document.getElementById('chartContainer').classList.add('d-none');
        document.getElementById('noDataMessage').classList.add('d-none');
        document.getElementById('errorMessage').classList.add('d-none');

        // 发送请求获取温度数据
        fetch(`/debug_script/temperature_data/{{ device.id }}?date=${date}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('网络请求失败');
                }
                return response.json();
            })
            .then(data => {
                // 隐藏加载中
                document.getElementById('loadingIndicator').classList.add('d-none');

                if (data.error) {
                    // 显示错误信息
                    document.getElementById('errorMessage').textContent = '获取温度数据失败: ' + data.error;
                    document.getElementById('errorMessage').classList.remove('d-none');
                    return;
                }

                // 检查是否有数据
                const temperatureData = data.temperature_data;
                const hasData = temperatureData && temperatureData.length > 0;

                if (!hasData) {
                    // 显示无数据提示
                    document.getElementById('noDataMessage').classList.remove('d-none');
                    return;
                }

                // 应用筛选条件
                const filteredData = filterTemperatureData(temperatureData);

                // 更新图表
                updateTemperatureChart(filteredData);

                // 显示图表容器
                document.getElementById('chartContainer').classList.remove('d-none');
            })
            .catch(error => {
                console.error('获取温度数据失败:', error);
                // 隐藏加载中，显示错误信息
                document.getElementById('loadingIndicator').classList.add('d-none');
                document.getElementById('errorMessage').textContent = '获取温度数据失败: ' + error.message;
                document.getElementById('errorMessage').classList.remove('d-none');
            });
    }

    // 筛选温度数据
    function filterTemperatureData(temperatureData) {
        // 获取筛选条件
        const timeRange = document.getElementById('timeRangeSelect').value;

        // 如果选择了全天，则不筛选
        if (timeRange === 'all') {
            return temperatureData;
        }

        // 筛选时间范围
        return temperatureData.filter(point => {
            const hour = new Date(point.time).getHours();

            switch (timeRange) {
                case 'morning':
                    return hour >= 6 && hour < 12;
                case 'afternoon':
                    return hour >= 12 && hour < 18;
                case 'evening':
                    return hour >= 18 && hour < 24;
                case 'night':
                    return hour >= 0 && hour < 6;
                default:
                    return true;
            }
        });
    }

    // 计算数据统计信息
    function calculateStats(dataPoints) {
        if (!dataPoints || dataPoints.length === 0) {
            return {
                totalPoints: 0,
                avgTemperature: 0,
                maxTemperature: 0,
                minTemperature: 0
            };
        }

        let totalSum = 0;
        let maxTemperature = -Infinity;
        let minTemperature = Infinity;

        dataPoints.forEach(point => {
            const value = point.value;
            totalSum += value;
            maxTemperature = Math.max(maxTemperature, value);
            minTemperature = Math.min(minTemperature, value);
        });

        const avgTemperature = dataPoints.length > 0 ? totalSum / dataPoints.length : 0;

        return {
            totalPoints: dataPoints.length,
            avgTemperature,
            maxTemperature: maxTemperature !== -Infinity ? maxTemperature : 0,
            minTemperature: minTemperature !== Infinity ? minTemperature : 0
        };
    }

    // 更新温度图表
    function updateTemperatureChart(temperatureData) {
        // 准备图表数据
        const dataPoints = temperatureData.map(point => ({
            x: new Date(point.time),
            y: point.value
        }));

        // 按时间排序数据点
        dataPoints.sort((a, b) => a.x - b.x);

        // 计算并更新统计信息
        const stats = calculateStats(temperatureData);
        document.getElementById('avgTemperature').textContent = stats.avgTemperature.toFixed(2) + ' °C';
        document.getElementById('maxTemperature').textContent = stats.maxTemperature.toFixed(2) + ' °C';
        document.getElementById('minTemperature').textContent = stats.minTemperature.toFixed(2) + ' °C';
        document.getElementById('dataPoints').textContent = stats.totalPoints;

        // 更新图例
        const legendContainer = document.getElementById('chartLegend');
        legendContainer.innerHTML = '';

        // 添加图例项
        const legendItem = document.createElement('div');
        legendItem.className = 'legend-item';
        legendItem.innerHTML = `
            <div style="width: 20px; height: 20px; background-color: ${chartColor}; margin-right: 8px; border-radius: 4px;"></div>
            <span>温度</span>
        `;
        legendContainer.appendChild(legendItem);

        // 销毁现有图表
        if (temperatureChart) {
            temperatureChart.destroy();
        }

        // 创建新图表
        const ctx = document.getElementById('temperatureChart').getContext('2d');
        temperatureChart = new Chart(ctx, {
            type: currentChartType,
            data: {
                datasets: [{
                    label: '温度',
                    data: dataPoints,
                    borderColor: chartColor,
                    backgroundColor: chartColor + '20',
                    borderWidth: 2,
                    pointRadius: 3,
                    pointHoverRadius: 5,
                    tension: document.getElementById('smoothLines').checked ? 0.4 : 0,
                    fill: false
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false
                },
                scales: {
                    x: {
                        type: 'time',
                        adapters: {
                            date: {
                                locale: 'zh-CN'
                            }
                        },
                        time: {
                            unit: 'hour',
                            displayFormats: {
                                hour: 'HH:mm'
                            },
                            tooltipFormat: 'YYYY-MM-DD HH:mm:ss'
                        },
                        title: {
                            display: true,
                            text: '时间'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: '温度 (°C)'
                        },
                        beginAtZero: false
                    }
                },
                plugins: {
                    legend: {
                        display: false  // 隐藏默认图例，使用自定义图例
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `温度: ${context.parsed.y.toFixed(2)} °C`;
                            }
                        }
                    },
                    zoom: {
                        pan: {
                            enabled: true,
                            mode: 'xy'
                        },
                        zoom: {
                            wheel: {
                                enabled: true
                            },
                            pinch: {
                                enabled: true
                            },
                            mode: 'xy'
                        }
                    }
                }
            }
        });

        // 添加双击重置缩放事件
        document.getElementById('temperatureChart').addEventListener('dblclick', function() {
            if (temperatureChart.resetZoom) {
                temperatureChart.resetZoom();
            }
        });

        // 添加重置缩放按钮事件
        document.getElementById('resetZoomBtn').addEventListener('click', function() {
            if (temperatureChart.resetZoom) {
                temperatureChart.resetZoom();
            }
        });
    }

    // 初始化数据导出功能
    document.addEventListener('DOMContentLoaded', function() {
        const exportBtn = document.getElementById('exportDataBtn');
        if (exportBtn) {
            exportBtn.setAttribute('data-device-id', '{{ device.id }}');

            if (window.DataExporter) {
                window.dataExporter = new DataExporter({{ device.id }});
                window.dataExporter.createExportButton = function() {};

                exportBtn.addEventListener('click', function() {
                    window.dataExporter.showExportModal();
                });
            }
        }
    });
</script>

<!-- 导出功能相关脚本 -->
<script src="{{ url_for('static', filename='js/data-interface-config.js') }}"></script>
<script src="{{ url_for('static', filename='js/optimized-data-loader.js') }}"></script>
<script src="{{ url_for('static', filename='js/data-export.js') }}"></script>
{% endblock %}
