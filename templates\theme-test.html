{% extends "base.html" %}

{% block title %}主题测试页面 - OTA设备管理系统{% endblock %}

{% block head %}
<!-- Liquid Glass Homepage Styles -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/themes/liquid-glass-homepage.css') }}">
<style>
    .test-section {
        margin-bottom: 40px;
    }
    
    .test-title {
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid rgba(255, 255, 255, 0.2);
    }
    
    .component-demo {
        padding: 20px;
        margin-bottom: 20px;
        border-radius: 15px;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 主题测试标题 -->
    <div class="hero-section">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-4 mb-3">液态玻璃主题测试</h1>
                <p class="lead mb-4">测试所有组件在液态玻璃主题下的显示效果</p>
                <div class="d-flex gap-3">
                    <button class="btn btn-light btn-lg" onclick="window.themeSwitcher.switchTheme('liquidGlass')">
                        <i class="fas fa-palette"></i> 切换到液态玻璃
                    </button>
                    <button class="btn btn-outline-light btn-lg" onclick="window.themeSwitcher.switchTheme('default')">
                        <i class="fas fa-undo"></i> 切换到默认主题
                    </button>
                </div>
            </div>
            <div class="col-lg-4 text-center">
                <i class="fas fa-flask" style="font-size: 8rem; opacity: 0.3;"></i>
            </div>
        </div>
    </div>

    <!-- 按钮测试 -->
    <div class="test-section">
        <h3 class="test-title"><i class="fas fa-mouse-pointer"></i> 按钮组件测试</h3>
        <div class="component-demo">
            <div class="row">
                <div class="col-md-6">
                    <h5>基础按钮</h5>
                    <div class="d-flex gap-2 flex-wrap mb-3">
                        <button class="btn btn-primary">主要按钮</button>
                        <button class="btn btn-secondary">次要按钮</button>
                        <button class="btn btn-success">成功按钮</button>
                        <button class="btn btn-warning">警告按钮</button>
                        <button class="btn btn-danger">危险按钮</button>
                        <button class="btn btn-info">信息按钮</button>
                    </div>
                </div>
                <div class="col-md-6">
                    <h5>按钮状态</h5>
                    <div class="d-flex gap-2 flex-wrap mb-3">
                        <button class="btn btn-primary">正常状态</button>
                        <button class="btn btn-primary" disabled>禁用状态</button>
                        <button class="btn btn-outline-primary">轮廓按钮</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 卡片测试 -->
    <div class="test-section">
        <h3 class="test-title"><i class="fas fa-id-card"></i> 卡片组件测试</h3>
        <div class="row">
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="stat-card">
                    <div class="card-header text-center">
                        <i class="fas fa-users stat-icon text-primary"></i>
                        <h5 class="card-title mb-0">用户统计</h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="stat-number text-primary">1,234</div>
                        <p class="text-muted mb-0">活跃用户</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">普通卡片</h5>
                    </div>
                    <div class="card-body">
                        <p class="card-text">这是一个普通的卡片内容，用于测试液态玻璃效果。</p>
                        <a href="#" class="btn btn-primary">操作按钮</a>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">无头部卡片</h5>
                        <p class="card-text">这是一个没有头部的卡片，测试不同的卡片样式。</p>
                        <div class="progress mb-2">
                            <div class="progress-bar" role="progressbar" style="width: 75%" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <small class="text-muted">进度: 75%</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 表格测试 -->
    <div class="test-section">
        <h3 class="test-title"><i class="fas fa-table"></i> 表格组件测试</h3>
        <div class="component-demo">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>设备ID</th>
                            <th>设备名称</th>
                            <th>状态</th>
                            <th>最后在线时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>DEV001</td>
                            <td>测试设备1</td>
                            <td><span class="badge bg-success">在线</span></td>
                            <td>2025-01-01 12:00:00</td>
                            <td>
                                <button class="btn btn-sm btn-primary">查看</button>
                                <button class="btn btn-sm btn-warning">编辑</button>
                            </td>
                        </tr>
                        <tr>
                            <td>DEV002</td>
                            <td>测试设备2</td>
                            <td><span class="badge bg-danger">离线</span></td>
                            <td>2025-01-01 10:30:00</td>
                            <td>
                                <button class="btn btn-sm btn-primary">查看</button>
                                <button class="btn btn-sm btn-warning">编辑</button>
                            </td>
                        </tr>
                        <tr>
                            <td>DEV003</td>
                            <td>测试设备3</td>
                            <td><span class="badge bg-warning">维护中</span></td>
                            <td>2025-01-01 11:15:00</td>
                            <td>
                                <button class="btn btn-sm btn-primary">查看</button>
                                <button class="btn btn-sm btn-warning">编辑</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 表单测试 -->
    <div class="test-section">
        <h3 class="test-title"><i class="fas fa-edit"></i> 表单组件测试</h3>
        <div class="component-demo">
            <div class="row">
                <div class="col-md-6">
                    <form>
                        <div class="mb-3">
                            <label for="testInput" class="form-label">测试输入框</label>
                            <input type="text" class="form-control" id="testInput" placeholder="请输入内容">
                        </div>
                        <div class="mb-3">
                            <label for="testSelect" class="form-label">测试选择框</label>
                            <select class="form-control" id="testSelect">
                                <option>选项1</option>
                                <option>选项2</option>
                                <option>选项3</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="testTextarea" class="form-label">测试文本域</label>
                            <textarea class="form-control" id="testTextarea" rows="3" placeholder="请输入多行文本"></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">提交表单</button>
                    </form>
                </div>
                <div class="col-md-6">
                    <h5>复选框和单选框</h5>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="" id="check1">
                            <label class="form-check-label" for="check1">复选框选项1</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" value="" id="check2">
                            <label class="form-check-label" for="check2">复选框选项2</label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="radioTest" id="radio1">
                            <label class="form-check-label" for="radio1">单选框选项1</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="radioTest" id="radio2">
                            <label class="form-check-label" for="radio2">单选框选项2</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 警告框测试 -->
    <div class="test-section">
        <h3 class="test-title"><i class="fas fa-exclamation-triangle"></i> 警告框组件测试</h3>
        <div class="component-demo">
            <div class="alert alert-primary" role="alert">
                <i class="fas fa-info-circle"></i> 这是一个主要信息警告框。
            </div>
            <div class="alert alert-success" role="alert">
                <i class="fas fa-check-circle"></i> 这是一个成功警告框。
            </div>
            <div class="alert alert-warning" role="alert">
                <i class="fas fa-exclamation-triangle"></i> 这是一个警告警告框。
            </div>
            <div class="alert alert-danger" role="alert">
                <i class="fas fa-times-circle"></i> 这是一个危险警告框。
            </div>
        </div>
    </div>

    <!-- 导航组件测试 -->
    <div class="test-section">
        <h3 class="test-title"><i class="fas fa-bars"></i> 导航组件测试</h3>
        <div class="component-demo">
            <ul class="nav nav-tabs" id="testTab" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="tab1-tab" data-bs-toggle="tab" data-bs-target="#tab1" type="button" role="tab">标签页1</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="tab2-tab" data-bs-toggle="tab" data-bs-target="#tab2" type="button" role="tab">标签页2</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="tab3-tab" data-bs-toggle="tab" data-bs-target="#tab3" type="button" role="tab">标签页3</button>
                </li>
            </ul>
            <div class="tab-content" id="testTabContent">
                <div class="tab-pane fade show active" id="tab1" role="tabpanel">
                    <p>这是标签页1的内容。液态玻璃效果应该在这里正常显示。</p>
                </div>
                <div class="tab-pane fade" id="tab2" role="tabpanel">
                    <p>这是标签页2的内容。测试标签页切换效果。</p>
                </div>
                <div class="tab-pane fade" id="tab3" role="tabpanel">
                    <p>这是标签页3的内容。验证所有标签页的样式一致性。</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框测试 -->
    <div class="test-section">
        <h3 class="test-title"><i class="fas fa-window-maximize"></i> 模态框组件测试</h3>
        <div class="component-demo">
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#testModal">
                打开测试模态框
            </button>
        </div>
    </div>
</div>

<!-- 测试模态框 -->
<div class="modal fade" id="testModal" tabindex="-1" aria-labelledby="testModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="testModalLabel">测试模态框</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>这是一个测试模态框，用于验证液态玻璃主题下的模态框效果。</p>
                <p>模态框应该具有毛玻璃背景和适当的模糊效果。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary">保存更改</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
    // 测试页面的额外脚本
    document.addEventListener('DOMContentLoaded', function() {
        // 监听主题变化
        document.addEventListener('themeChanged', function(e) {
            console.log('主题已切换到:', e.detail.theme);
            
            // 显示提示
            const toast = document.createElement('div');
            toast.className = 'alert alert-info position-fixed';
            toast.style.cssText = `
                top: 20px;
                right: 20px;
                z-index: 10000;
                min-width: 300px;
            `;
            toast.innerHTML = `
                <i class="fas fa-palette"></i> 
                主题已切换到: ${e.detail.themeData.name}
                <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
            `;
            document.body.appendChild(toast);
            
            // 3秒后自动移除
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 3000);
        });
    });
</script>
{% endblock %}
