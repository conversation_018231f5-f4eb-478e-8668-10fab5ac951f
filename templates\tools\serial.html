{% extends "base.html" %}

{% block title %}串口工具{% endblock %}

{% block content %}
<div class="container-fluid mt-4">
    <h2>串口工具</h2>
    
    <!-- 浏览器兼容性检查 -->
    <div id="browserSupport" class="alert alert-warning" style="display: none;">
        <i class="fas fa-exclamation-triangle"></i>
        您的浏览器不支持Web Serial API，请使用Chrome或Edge浏览器访问。
    </div>
    
    <!-- 串口工具界面 -->
    <div id="serialTool" class="row">
        <div class="col-md-4">
            <div class="card mb-3">
                <div class="card-header bg-primary text-white">
                    <i class="fas fa-cog"></i> 串口设置
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label for="port"><i class="fas fa-plug"></i> 串口</label>
                        <small class="form-text text-muted">点击"连接"按钮选择串口</small>
                    </div>
                    <div class="form-group">
                        <label for="baudrate"><i class="fas fa-tachometer-alt"></i> 波特率</label>
                        <select class="form-control" id="baudrate">
                            <!-- 波特率选项将由JavaScript动态填充 -->
                        </select>
                    </div>
                    <div class="d-flex justify-content-between mt-3">
                        <button class="btn btn-success" id="connect">
                            <i class="fas fa-plug"></i> 连接
                        </button>
                        <button class="btn btn-danger" id="disconnect" disabled>
                            <i class="fas fa-times"></i> 断开
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header bg-success text-white">
                    <i class="fas fa-paper-plane"></i> 发送数据
                </div>
                <div class="card-body">
                    <div class="form-group">
                        <label for="sendData"><i class="fas fa-keyboard"></i> 数据</label>
                        <textarea class="form-control" id="sendData" rows="3" placeholder="输入要发送的数据"></textarea>
                    </div>
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="hexSend">
                        <label class="form-check-label" for="hexSend">
                            <i class="fas fa-code"></i> 十六进制发送
                        </label>
                        <small class="form-text text-muted">选中后，输入格式为"AA BB CC"的十六进制数据</small>
                    </div>
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="autoNewline" checked>
                        <label class="form-check-label" for="autoNewline">
                            <i class="fas fa-arrow-down"></i> 自动添加换行
                        </label>
                        <small class="form-text text-muted">选中后，发送数据时自动在末尾添加换行符</small>
                    </div>
                    <button class="btn btn-primary w-100" id="send" disabled>
                        <i class="fas fa-paper-plane"></i> 发送
                    </button>
                </div>
            </div>
        </div>
        
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-terminal"></i> 终端
                        </div>
                        <div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="checkbox" id="autoScroll" checked>
                                <label class="form-check-label text-white" for="autoScroll">
                                    <i class="fas fa-scroll"></i> 自动滚动
                                </label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="checkbox" id="hexDisplay">
                                <label class="form-check-label text-white" for="hexDisplay">
                                    <i class="fas fa-code"></i> 十六进制显示
                                </label>
                            </div>
                            <button class="btn btn-sm btn-outline-light" id="clear">
                                <i class="fas fa-trash"></i> 清空
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div id="terminal" class="terminal">
                        <!-- 内容将自动换行并保持原始格式 -->
                    </div>
                </div>
            </div>
            
            <div class="card mt-3">
                <div class="card-header bg-info text-white">
                    <i class="fas fa-info-circle"></i> 使用说明
                </div>
                <div class="card-body">
                    <ol>
                        <li>点击"连接"按钮选择串口并连接</li>
                        <li>选择适当的波特率（默认115200）</li>
                        <li>在发送区域输入数据，点击"发送"按钮发送</li>
                        <li>如需发送十六进制数据，勾选"十六进制发送"并输入格式如"AA BB CC"的数据</li>
                        <li>接收到的数据会显示在终端区域</li>
                        <li>可以勾选"十六进制显示"将接收到的数据以十六进制格式显示</li>
                        <li>使用完毕后点击"断开"按钮断开连接</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
.terminal {
    background-color: #1e1e1e;
    color: #e9ecef;
    font-family: 'Consolas', 'Courier New', monospace;
    padding: 10px;
    height: 500px;
    overflow-y: auto;
    white-space: pre-wrap;
    border-radius: 4px;
    border: 1px solid #343a40;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.5);
    margin: 0;
    line-height: 1.5;
}

.terminal-line {
    margin: 2px 0;
    line-height: 1.4;
    word-break: break-word;
    white-space: pre-wrap;
    font-family: 'Times New Roman', 'Consolas', 'Courier New', monospace;
    font-size: 14px;
}

/* 移除原有的:hover效果 */
.terminal-line:hover {
    background-color: transparent;
}

/* 优化滚动条样式 */
.terminal::-webkit-scrollbar {
    width: 8px;
}

.terminal::-webkit-scrollbar-track {
    background: #2d2d2d;
}

.terminal::-webkit-scrollbar-thumb {
    background: #4d4d4d;
    border-radius: 4px;
}

.card {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: none;
    margin-bottom: 20px;
}
.card-header {
    font-weight: bold;
}
.btn {
    border-radius: 4px;
}
.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}
</style>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/tools/serial.js') }}"></script>
{% endblock %} 