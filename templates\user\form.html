{% extends "base.html" %}

{% block title %}{% if user %}编辑用户{% else %}添加用户{% endif %}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{% if user %}编辑用户{% else %}添加用户{% endif %}</h3>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label for="username" class="form-label">用户名</label>
                            <input type="text" class="form-control" id="username" name="username" 
                                   value="{{ user.username if user else '' }}" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">密码</label>
                            <input type="password" class="form-control" id="password" name="password" 
                                   {% if not user %}required{% endif %}>
                            {% if user %}
                            <div class="form-text">留空表示不修改密码</div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="is_admin" name="is_admin"
                                       {% if user and user.is_admin %}checked{% endif %}>
                                <label class="form-check-label" for="is_admin">管理员权限</label>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">保存</button>
                            <a href="{{ url_for('user.user_list') }}" class="btn btn-secondary">返回</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 