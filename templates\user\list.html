{% extends "base.html" %}

{% block title %}用户管理{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>用户管理</h2>
        <a href="{{ url_for('user.add_user') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> 添加用户
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>用户名</th>
                            <th>角色</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                        <tr>
                            <td>{{ user.id }}</td>
                            <td>{{ user.username }}</td>
                            <td>
                                {% if user.is_admin %}
                                <span class="badge bg-primary">管理员</span>
                                {% else %}
                                <span class="badge bg-secondary">普通用户</span>
                                {% endif %}
                            </td>
                            <td>{{ user.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                            <td>
                                <a href="{{ url_for('user.edit_user', id=user.id) }}" class="btn btn-sm btn-info">
                                    <i class="fas fa-edit"></i> 编辑
                                </a>
                                {% if user.id != current_user.id %}
                                <a href="{{ url_for('user.delete_user', id=user.id) }}" 
                                   class="btn btn-sm btn-danger"
                                   onclick="return confirm('确定要删除此用户吗？')">
                                    <i class="fas fa-trash"></i> 删除
                                </a>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %} 