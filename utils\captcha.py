import random
import string
from flask import session

def generate_captcha(length=4):
    """生成数字验证码"""
    # 生成随机数字验证码
    captcha = ''.join(random.choices(string.digits, k=length))
    # 将验证码存入session
    session['captcha'] = captcha
    return captcha

def verify_captcha(user_input):
    """验证用户输入的验证码"""
    if not user_input:
        return False
    # 从session中获取验证码
    captcha = session.get('captcha')
    if not captcha:
        return False
    # 验证完成后删除session中的验证码
    session.pop('captcha', None)
    # 不区分大小写比较
    return user_input.lower() == captcha.lower()