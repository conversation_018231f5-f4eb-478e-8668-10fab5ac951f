#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
代码质量检查工具
用于检查前端代码的质量和一致性
"""

import os
import re
import json
from pathlib import Path


class CodeQualityChecker:
    """代码质量检查器"""
    
    def __init__(self, project_root='.'):
        self.project_root = Path(project_root)
        self.issues = []
        
    def check_html_templates(self):
        """检查HTML模板文件"""
        print("检查HTML模板文件...")
        
        templates_dir = self.project_root / 'templates'
        if not templates_dir.exists():
            print("templates目录不存在")
            return
        
        for html_file in templates_dir.rglob('*.html'):
            self._check_html_file(html_file)
    
    def _check_html_file(self, file_path):
        """检查单个HTML文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查重复的函数定义
            self._check_duplicate_functions(file_path, content)
            
            # 检查未使用的变量
            self._check_unused_variables(file_path, content)
            
            # 检查console.log语句
            self._check_console_logs(file_path, content)
            
            # 检查注释掉的代码
            self._check_commented_code(file_path, content)
            
        except Exception as e:
            self.issues.append({
                'file': str(file_path),
                'type': 'error',
                'message': f'读取文件失败: {e}'
            })
    
    def _check_duplicate_functions(self, file_path, content):
        """检查重复的函数定义"""
        # 查找JavaScript函数定义
        function_pattern = r'function\s+(\w+)\s*\('
        functions = re.findall(function_pattern, content)
        
        # 查找重复函数
        seen = set()
        for func_name in functions:
            if func_name in seen:
                self.issues.append({
                    'file': str(file_path),
                    'type': 'warning',
                    'message': f'重复的函数定义: {func_name}'
                })
            seen.add(func_name)
    
    def _check_unused_variables(self, file_path, content):
        """检查未使用的变量（简单检查）"""
        # 查找变量声明
        var_pattern = r'(?:var|let|const)\s+(\w+)'
        variables = re.findall(var_pattern, content)
        
        # 检查是否在其他地方使用
        for var_name in variables:
            # 简单检查：如果变量名在文件中只出现一次，可能未使用
            if content.count(var_name) == 1:
                self.issues.append({
                    'file': str(file_path),
                    'type': 'info',
                    'message': f'可能未使用的变量: {var_name}'
                })
    
    def _check_console_logs(self, file_path, content):
        """检查console.log语句"""
        console_pattern = r'console\.(log|warn|error|debug)'
        matches = re.findall(console_pattern, content)
        
        if len(matches) > 10:  # 如果console语句太多
            self.issues.append({
                'file': str(file_path),
                'type': 'info',
                'message': f'发现 {len(matches)} 个console语句，考虑在生产环境中移除'
            })
    
    def _check_commented_code(self, file_path, content):
        """检查注释掉的代码"""
        # 查找大块的注释代码
        comment_pattern = r'//.*(?:function|var|let|const|if|for|while)'
        matches = re.findall(comment_pattern, content)
        
        if matches:
            self.issues.append({
                'file': str(file_path),
                'type': 'info',
                'message': f'发现 {len(matches)} 处注释掉的代码，考虑清理'
            })
    
    def check_python_files(self):
        """检查Python文件"""
        print("检查Python文件...")
        
        python_files = list(self.project_root.rglob('*.py'))
        for py_file in python_files:
            if 'venv' in str(py_file) or '__pycache__' in str(py_file):
                continue
            self._check_python_file(py_file)
    
    def _check_python_file(self, file_path):
        """检查单个Python文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查导入语句
            self._check_imports(file_path, content)
            
            # 检查函数长度
            self._check_function_length(file_path, content)
            
        except Exception as e:
            self.issues.append({
                'file': str(file_path),
                'type': 'error',
                'message': f'读取文件失败: {e}'
            })
    
    def _check_imports(self, file_path, content):
        """检查导入语句"""
        lines = content.split('\n')
        import_lines = [line for line in lines if line.strip().startswith(('import ', 'from '))]
        
        # 检查是否有未使用的导入
        for line in import_lines:
            if 'import' in line:
                # 简单提取模块名
                parts = line.split()
                if len(parts) >= 2:
                    module = parts[-1].split('.')[0]
                    if content.count(module) == 1:  # 只在导入语句中出现
                        self.issues.append({
                            'file': str(file_path),
                            'type': 'info',
                            'message': f'可能未使用的导入: {line.strip()}'
                        })
    
    def _check_function_length(self, file_path, content):
        """检查函数长度"""
        lines = content.split('\n')
        in_function = False
        function_start = 0
        function_name = ''
        
        for i, line in enumerate(lines):
            if line.strip().startswith('def '):
                if in_function:
                    # 检查上一个函数的长度
                    length = i - function_start
                    if length > 50:  # 函数超过50行
                        self.issues.append({
                            'file': str(file_path),
                            'type': 'warning',
                            'message': f'函数 {function_name} 过长 ({length} 行)，考虑拆分'
                        })
                
                in_function = True
                function_start = i
                function_name = line.split('(')[0].replace('def ', '').strip()
            
            elif line.strip().startswith('class '):
                in_function = False
    
    def check_css_files(self):
        """检查CSS文件"""
        print("检查CSS文件...")
        
        css_files = list(self.project_root.rglob('*.css'))
        for css_file in css_files:
            self._check_css_file(css_file)
    
    def _check_css_file(self, file_path):
        """检查单个CSS文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查重复的选择器
            selector_pattern = r'([.#]?[\w-]+)\s*{'
            selectors = re.findall(selector_pattern, content)
            
            seen = set()
            for selector in selectors:
                if selector in seen:
                    self.issues.append({
                        'file': str(file_path),
                        'type': 'warning',
                        'message': f'重复的CSS选择器: {selector}'
                    })
                seen.add(selector)
                
        except Exception as e:
            self.issues.append({
                'file': str(file_path),
                'type': 'error',
                'message': f'读取文件失败: {e}'
            })
    
    def run_all_checks(self):
        """运行所有检查"""
        print("开始代码质量检查...")
        print("=" * 50)
        
        self.check_html_templates()
        self.check_python_files()
        self.check_css_files()
        
        self.print_report()
    
    def print_report(self):
        """打印检查报告"""
        print("\n" + "=" * 50)
        print("代码质量检查报告")
        print("=" * 50)
        
        if not self.issues:
            print("✅ 没有发现问题！")
            return
        
        # 按类型分组
        errors = [issue for issue in self.issues if issue['type'] == 'error']
        warnings = [issue for issue in self.issues if issue['type'] == 'warning']
        infos = [issue for issue in self.issues if issue['type'] == 'info']
        
        print(f"总问题数: {len(self.issues)}")
        print(f"错误: {len(errors)}")
        print(f"警告: {len(warnings)}")
        print(f"信息: {len(infos)}")
        
        # 打印详细信息
        for issue_type, issues in [('错误', errors), ('警告', warnings), ('信息', infos)]:
            if issues:
                print(f"\n{issue_type}:")
                for issue in issues:
                    print(f"  📁 {issue['file']}")
                    print(f"     {issue['message']}")
    
    def save_report(self, output_file='code_quality_report.json'):
        """保存报告到文件"""
        report = {
            'total_issues': len(self.issues),
            'errors': len([i for i in self.issues if i['type'] == 'error']),
            'warnings': len([i for i in self.issues if i['type'] == 'warning']),
            'infos': len([i for i in self.issues if i['type'] == 'info']),
            'issues': self.issues
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n报告已保存到: {output_file}")


def main():
    """主函数"""
    checker = CodeQualityChecker()
    checker.run_all_checks()
    checker.save_report()


if __name__ == "__main__":
    main()
