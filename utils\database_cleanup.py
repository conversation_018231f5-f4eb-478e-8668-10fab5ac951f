#!/usr/bin/env python3
"""
数据库清理工具
只清理无用的数据表和数据，不删除生产数据
"""

import os
import sys
from datetime import datetime, timedelta
from sqlalchemy import text, inspect

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app_factory import create_app
from models.database import db

def get_all_tables():
    """获取数据库中的所有表"""
    inspector = inspect(db.engine)
    return inspector.get_table_names()

def get_table_row_count(table_name):
    """获取表的行数"""
    try:
        result = db.session.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
        return result.scalar()
    except Exception as e:
        print(f"   ❌ 无法获取表 {table_name} 的行数: {e}")
        return None

def analyze_database():
    """分析数据库结构和数据"""
    print("🔍 分析数据库结构...")
    
    # 获取所有表
    tables = get_all_tables()
    print(f"   📊 数据库中共有 {len(tables)} 个表")
    
    # 定义已知的有用表
    useful_tables = {
        'device',           # 设备表
        'users',            # 用户表
        'ota_task',         # OTA任务表
        'firmware',         # 固件表
        'register_config',  # 寄存器配置表
        'merchant',         # 商户表
        'time_series_data', # 时序数据表
        'time_series_data_batch', # 时序数据批次表
        'optimized_time_series_data', # 优化时序数据表
        'optimized_time_series_buffer', # 优化时序数据缓冲表
        'alembic_version',  # 数据库迁移版本表
    }
    
    # 分析每个表
    table_info = {}
    for table in tables:
        row_count = get_table_row_count(table)
        is_useful = table in useful_tables
        table_info[table] = {
            'row_count': row_count,
            'is_useful': is_useful,
            'status': 'useful' if is_useful else 'unknown'
        }
        
        status_icon = "✅" if is_useful else "❓"
        print(f"   {status_icon} {table}: {row_count} 行 ({'有用' if is_useful else '未知'})")
    
    return table_info

def identify_cleanup_candidates(table_info):
    """识别可以清理的候选项"""
    print("\n🧹 识别清理候选项...")
    
    cleanup_candidates = []
    
    # 1. 空表（除了有用的表）
    empty_tables = []
    for table, info in table_info.items():
        if info['row_count'] == 0 and not info['is_useful']:
            empty_tables.append(table)
    
    if empty_tables:
        print(f"   📋 发现 {len(empty_tables)} 个空的未知表:")
        for table in empty_tables:
            print(f"      - {table}")
        cleanup_candidates.extend([('empty_table', table) for table in empty_tables])
    
    # 2. 旧的时序数据（超过30天的）
    try:
        cutoff_date = datetime.now() - timedelta(days=30)
        
        # 检查time_series_data表中的旧数据
        if 'time_series_data' in table_info:
            result = db.session.execute(text("""
                SELECT COUNT(*) FROM time_series_data 
                WHERE created_at < :cutoff_date
            """), {'cutoff_date': cutoff_date})
            old_data_count = result.scalar()
            
            if old_data_count > 0:
                print(f"   📅 time_series_data表中有 {old_data_count} 条超过30天的旧数据")
                cleanup_candidates.append(('old_time_series', old_data_count))
        
        # 检查optimized_time_series_data表中的旧数据
        if 'optimized_time_series_data' in table_info:
            result = db.session.execute(text("""
                SELECT COUNT(*) FROM optimized_time_series_data 
                WHERE created_at < :cutoff_date
            """), {'cutoff_date': cutoff_date})
            old_opt_data_count = result.scalar()
            
            if old_opt_data_count > 0:
                print(f"   📅 optimized_time_series_data表中有 {old_opt_data_count} 条超过30天的旧数据")
                cleanup_candidates.append(('old_optimized_time_series', old_opt_data_count))
                
    except Exception as e:
        print(f"   ❌ 检查旧数据时出错: {e}")
    
    # 3. 失败的OTA任务（超过7天的）
    try:
        cutoff_date = datetime.now() - timedelta(days=7)
        result = db.session.execute(text("""
            SELECT COUNT(*) FROM ota_task 
            WHERE status = '失败' AND created_at < :cutoff_date
        """), {'cutoff_date': cutoff_date})
        failed_tasks = result.scalar()
        
        if failed_tasks > 0:
            print(f"   🚫 发现 {failed_tasks} 个超过7天的失败OTA任务")
            cleanup_candidates.append(('old_failed_ota', failed_tasks))
            
    except Exception as e:
        print(f"   ❌ 检查失败OTA任务时出错: {e}")
    
    return cleanup_candidates

def perform_cleanup(cleanup_candidates, dry_run=True):
    """执行清理操作"""
    if dry_run:
        print("\n🔍 模拟清理操作 (dry run)...")
    else:
        print("\n🧹 执行实际清理操作...")
        print("⚠️  警告: 这将永久删除数据!")
        
        confirm = input("确认要执行清理吗? (输入 'YES' 确认): ")
        if confirm != 'YES':
            print("❌ 清理操作已取消")
            return False
    
    cleaned_items = 0
    
    for cleanup_type, data in cleanup_candidates:
        try:
            if cleanup_type == 'empty_table':
                table_name = data
                if not dry_run:
                    db.session.execute(text(f"DROP TABLE IF EXISTS {table_name}"))
                    db.session.commit()
                print(f"   {'🔍' if dry_run else '✅'} 删除空表: {table_name}")
                cleaned_items += 1
                
            elif cleanup_type == 'old_time_series':
                count = data
                cutoff_date = datetime.now() - timedelta(days=30)
                if not dry_run:
                    db.session.execute(text("""
                        DELETE FROM time_series_data 
                        WHERE created_at < :cutoff_date
                    """), {'cutoff_date': cutoff_date})
                    db.session.commit()
                print(f"   {'🔍' if dry_run else '✅'} 删除旧时序数据: {count} 条")
                cleaned_items += count
                
            elif cleanup_type == 'old_optimized_time_series':
                count = data
                cutoff_date = datetime.now() - timedelta(days=30)
                if not dry_run:
                    db.session.execute(text("""
                        DELETE FROM optimized_time_series_data 
                        WHERE created_at < :cutoff_date
                    """), {'cutoff_date': cutoff_date})
                    db.session.commit()
                print(f"   {'🔍' if dry_run else '✅'} 删除旧优化时序数据: {count} 条")
                cleaned_items += count
                
            elif cleanup_type == 'old_failed_ota':
                count = data
                cutoff_date = datetime.now() - timedelta(days=7)
                if not dry_run:
                    db.session.execute(text("""
                        DELETE FROM ota_task 
                        WHERE status = '失败' AND created_at < :cutoff_date
                    """), {'cutoff_date': cutoff_date})
                    db.session.commit()
                print(f"   {'🔍' if dry_run else '✅'} 删除旧失败OTA任务: {count} 个")
                cleaned_items += count
                
        except Exception as e:
            print(f"   ❌ 清理 {cleanup_type} 时出错: {e}")
            if not dry_run:
                db.session.rollback()
    
    if dry_run:
        print(f"\n📊 模拟清理完成，共识别出 {cleaned_items} 项可清理内容")
    else:
        print(f"\n✅ 清理完成，共清理了 {cleaned_items} 项内容")
    
    return True

def main():
    """主函数"""
    print("🗃️  数据库清理工具")
    print("=" * 50)
    
    # 创建应用上下文
    app = create_app()
    
    with app.app_context():
        try:
            # 分析数据库
            table_info = analyze_database()
            
            # 识别清理候选项
            cleanup_candidates = identify_cleanup_candidates(table_info)
            
            if not cleanup_candidates:
                print("\n✨ 数据库很干净，没有发现需要清理的内容")
                return
            
            # 首先执行模拟清理
            perform_cleanup(cleanup_candidates, dry_run=True)
            
            # 询问是否执行实际清理
            print("\n" + "=" * 50)
            choice = input("是否执行实际清理? (y/N): ").lower()
            
            if choice == 'y':
                perform_cleanup(cleanup_candidates, dry_run=False)
            else:
                print("❌ 清理操作已取消")
                
        except Exception as e:
            print(f"❌ 清理过程中出错: {e}")
            return False
    
    print("\n🎉 数据库清理工具执行完成")
    return True

if __name__ == "__main__":
    main()
