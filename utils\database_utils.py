#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据库兼容性工具模块
提供跨数据库的兼容性函数
"""

from flask import current_app
from sqlalchemy import func, text
from models.database import db


def get_database_type():
    """获取当前数据库类型"""
    database_uri = current_app.config.get('SQLALCHEMY_DATABASE_URI', '')
    if 'postgresql' in database_uri:
        return 'postgresql'
    elif 'sqlite' in database_uri:
        return 'sqlite'
    else:
        return 'unknown'


def date_filter(column, target_date):
    """
    跨数据库的日期筛选函数
    
    Args:
        column: 数据库列对象
        target_date: 目标日期（date对象）
    
    Returns:
        SQLAlchemy筛选条件
    """
    db_type = get_database_type()
    
    if db_type == 'postgresql':
        # PostgreSQL使用DATE()函数
        return func.date(column) == target_date
    elif db_type == 'sqlite':
        # SQLite使用date()函数
        return func.date(column) == target_date
    else:
        # 通用方法：使用日期范围
        from datetime import datetime, timedelta
        start_datetime = datetime.combine(target_date, datetime.min.time())
        end_datetime = start_datetime + timedelta(days=1)
        return db.and_(column >= start_datetime, column < end_datetime)


def ilike_filter(column, pattern):
    """
    跨数据库的不区分大小写模糊查询
    
    Args:
        column: 数据库列对象
        pattern: 查询模式
    
    Returns:
        SQLAlchemy筛选条件
    """
    db_type = get_database_type()
    
    if db_type == 'postgresql':
        # PostgreSQL原生支持ILIKE
        return column.ilike(f'%{pattern}%')
    elif db_type == 'sqlite':
        # SQLite使用LIKE（默认不区分大小写）
        return column.like(f'%{pattern}%')
    else:
        # 通用方法：转换为小写比较
        return func.lower(column).like(f'%{pattern.lower()}%')


def contains_filter(column, pattern):
    """
    跨数据库的包含查询（区分大小写）
    
    Args:
        column: 数据库列对象
        pattern: 查询模式
    
    Returns:
        SQLAlchemy筛选条件
    """
    # contains方法在所有数据库中都应该正常工作
    return column.contains(pattern)


def like_filter(column, pattern):
    """
    跨数据库的LIKE查询
    
    Args:
        column: 数据库列对象
        pattern: 查询模式（包含%通配符）
    
    Returns:
        SQLAlchemy筛选条件
    """
    return column.like(pattern)


def get_connection_info():
    """获取数据库连接信息"""
    db_type = get_database_type()
    database_uri = current_app.config.get('SQLALCHEMY_DATABASE_URI', '')
    
    info = {
        'type': db_type,
        'uri': database_uri,
        'engine': str(db.engine),
        'pool_size': getattr(db.engine.pool, 'size', 'N/A'),
        'checked_out': getattr(db.engine.pool, 'checkedout', 'N/A'),
    }
    
    return info


def test_database_compatibility():
    """测试数据库兼容性"""
    try:
        db_type = get_database_type()
        
        # 测试基本连接
        with db.engine.connect() as conn:
            if db_type == 'postgresql':
                result = conn.execute(text('SELECT version()'))
            else:
                result = conn.execute(text('SELECT sqlite_version()'))
            
            version = result.fetchone()[0]
            
        # 测试日期函数
        from datetime import date
        from models.user import User
        test_date = date.today()
        
        # 这个查询应该不会返回结果，但会测试语法
        test_query = User.query.filter(date_filter(User.created_at, test_date)).limit(1)
        test_query.all()  # 执行查询
        
        return True, f"{db_type} 兼容性测试通过，版本: {version}"
        
    except Exception as e:
        return False, f"兼容性测试失败: {e}"


def optimize_query_for_database(query, db_type=None):
    """
    根据数据库类型优化查询
    
    Args:
        query: SQLAlchemy查询对象
        db_type: 数据库类型，如果为None则自动检测
    
    Returns:
        优化后的查询对象
    """
    if db_type is None:
        db_type = get_database_type()
    
    if db_type == 'postgresql':
        # PostgreSQL优化：添加查询提示
        # 这里可以添加PostgreSQL特定的优化
        pass
    elif db_type == 'sqlite':
        # SQLite优化
        # 这里可以添加SQLite特定的优化
        pass
    
    return query


def get_database_stats():
    """获取数据库统计信息"""
    try:
        stats = {}
        
        # 获取所有表的行数
        from models.user import User
        from models.device import Device
        from models.ota_task import OtaTask
        from models.firmware import Firmware
        
        tables = [
            ('users', User),
            ('devices', Device),
            ('ota_tasks', OtaTask),
            ('firmwares', Firmware),
        ]
        
        for table_name, model in tables:
            try:
                count = model.query.count()
                stats[table_name] = count
            except Exception as e:
                stats[table_name] = f"Error: {e}"
        
        # 获取连接信息
        stats['connection_info'] = get_connection_info()
        
        return stats
        
    except Exception as e:
        return {'error': str(e)}
