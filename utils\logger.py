import os
import logging
from datetime import datetime


class LoggerManager:
    """
    日志管理器类，使用单例模式确保整个应用只有一个日志记录器实例
    """
    _instance = None
    _logger = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(LoggerManager, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        # 由于__new__方法已经确保了单例，这里只需要初始化一次
        if not LoggerManager._initialized:
            LoggerManager._initialized = True
            self._setup_logging()

    def _setup_logging(self):
        """配置日志记录器"""
        # 创建日志目录（如果不存在）
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # 生成日志文件名（包含时间戳）
        log_file = os.path.join(log_dir, f"ota_admin_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
        
        # 配置根日志记录器
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.INFO)
        
        # 清除现有的处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)
        
        # 创建文件处理器
        file_handler = logging.FileHandler(log_file, encoding='utf-8', mode='w')
        file_handler.setLevel(logging.INFO)
        file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(file_formatter)
        root_logger.addHandler(file_handler)
        
        # 创建并返回OTA管理后台专用的日志记录器
        LoggerManager._logger = logging.getLogger("OTA_Admin")
        LoggerManager._logger.info(f"日志文件已创建: {log_file}")

    @classmethod
    def get_logger(cls):
        """
        获取日志记录器实例
        如果尚未初始化，则先初始化
        """
        if cls._instance is None:
            cls()
        return cls._logger


# 为了向后兼容，保留原来的setup_logging函数
def setup_logging():
    """
    配置日志记录器（向后兼容函数）
    """
    return LoggerManager.get_logger()
