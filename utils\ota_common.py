#!/usr/bin/env python3
"""
OTA通用工具模块

统一管理所有OTA相关的工具方法，避免重复实现
包括：版本解析、状态转换、设备验证、固件验证等功能
"""

import os
import re
import logging
from typing import List, Tuple, Optional, Dict, Any
from datetime import datetime

logger = logging.getLogger(__name__)


class VersionUtils:
    """版本号处理工具类"""
    
    @staticmethod
    def parse_version_string(version_str: str) -> Optional[int]:
        """将版本字符串转换为数值
        
        支持格式：
        - "0.3.2" -> 0x00000302
        - "1.4.5" -> 0x00010405
        - "v0.3.2" -> 0x00000302
        
        格式：0x00MMNNPP (MM=主版本, NN=次版本, PP=补丁版本)
        
        Args:
            version_str: 版本字符串
            
        Returns:
            版本号整数值，失败返回None
        """
        try:
            # 移除可能的前缀和后缀
            version_str = version_str.strip().lower()
            if version_str.startswith('v'):
                version_str = version_str[1:]

            # 分割版本号
            parts = version_str.split('.')
            if len(parts) < 2:
                logger.warning(f"版本格式不正确: {version_str}")
                return None

            # 确保有3个部分
            while len(parts) < 3:
                parts.append('0')

            # 转换为整数
            major = int(parts[0]) & 0xFF
            minor = int(parts[1]) & 0xFF
            patch = int(parts[2]) & 0xFF

            # 组合为32位整数 (0x00MMNNPP格式)
            version_int = (major << 16) | (minor << 8) | patch

            logger.debug(f"版本解析: {version_str} -> 0x{version_int:08x}")
            return version_int

        except Exception as e:
            logger.error(f"解析版本字符串失败: {version_str}, 错误: {e}")
            return None
    
    @staticmethod
    def parse_version_from_registers(version_h: int, version_l: int) -> str:
        """从寄存器值解析版本号
        
        Args:
            version_h: 版本号高字节
            version_l: 版本号低字节
            
        Returns:
            版本字符串 (例如: "1.2.3")
        """
        try:
            # 解析APP版本号：REG_VERSION_H的低8位 + REG_VERSION_L的16位
            version_low_8 = version_h & 0xFF
            version_combined = (version_low_8 << 16) | version_l

            # 转换为版本字符串
            major = (version_combined >> 16) & 0xFF
            minor = (version_combined >> 8) & 0xFF
            patch = version_combined & 0xFF
            
            return f"{major}.{minor}.{patch}"
            
        except Exception as e:
            logger.error(f"从寄存器解析版本失败: version_h={version_h}, version_l={version_l}, 错误: {e}")
            return "未知"
    
    @staticmethod
    def extract_version_from_filename(filename: str) -> str:
        """从文件名提取版本信息
        
        Args:
            filename: 文件名
            
        Returns:
            版本字符串
        """
        # 移除扩展名
        name_without_ext = os.path.splitext(filename)[0]

        # 尝试提取版本号模式
        version_patterns = [
            r"v?(\d+\.\d+\.\d+)",  # v1.0.0 或 1.0.0
            r"v?(\d+\.\d+)",       # v1.0 或 1.0
            r"v?(\d+)",            # v1 或 1
        ]

        for pattern in version_patterns:
            match = re.search(pattern, name_without_ext, re.IGNORECASE)
            if match:
                return match.group(1)

        # 如果没有找到版本号，返回文件名
        return name_without_ext
    
    @staticmethod
    def compare_versions(version1: str, version2: str) -> int:
        """比较两个版本号
        
        Args:
            version1: 版本1
            version2: 版本2
            
        Returns:
            -1: version1 < version2
             0: version1 == version2
             1: version1 > version2
        """
        try:
            v1_int = VersionUtils.parse_version_string(version1)
            v2_int = VersionUtils.parse_version_string(version2)
            
            if v1_int is None or v2_int is None:
                return 0  # 无法比较时认为相等
                
            if v1_int < v2_int:
                return -1
            elif v1_int > v2_int:
                return 1
            else:
                return 0
                
        except Exception as e:
            logger.error(f"版本比较失败: {version1} vs {version2}, 错误: {e}")
            return 0


class StatusUtils:
    """状态转换工具类"""
    
    # OTA任务状态映射
    DETAILED_STATUS_MAPPING = {
        "PENDING": "等待中",
        "INITIALIZING": "进行中",
        "CONNECTING": "进行中", 
        "LOADING_FIRMWARE": "进行中",
        "STARTING_OTA": "进行中",
        "CHECKING_DIFF": "进行中",
        "SENDING_SLICES": "进行中",
        "QUERYING_RESULT": "进行中",
        "REBOOTING": "进行中",
        "SUCCESS": "成功",
        "FAILED": "失败",
        "CANCELLED": "失败",
        "PAUSED": "等待中",
    }
    
    @staticmethod
    def get_legacy_status(detailed_status: str) -> str:
        """获取兼容旧系统的状态
        
        Args:
            detailed_status: 详细状态
            
        Returns:
            简化状态
        """
        return StatusUtils.DETAILED_STATUS_MAPPING.get(detailed_status.upper(), "未知")
    
    @staticmethod
    def is_terminal_status(status: str) -> bool:
        """判断是否为终止状态
        
        Args:
            status: 状态字符串
            
        Returns:
            是否为终止状态
        """
        terminal_statuses = {"成功", "失败", "SUCCESS", "FAILED", "CANCELLED"}
        return status in terminal_statuses
    
    @staticmethod
    def is_running_status(status: str) -> bool:
        """判断是否为运行中状态
        
        Args:
            status: 状态字符串
            
        Returns:
            是否为运行中状态
        """
        running_statuses = {"进行中", "INITIALIZING", "CONNECTING", "LOADING_FIRMWARE", 
                           "STARTING_OTA", "CHECKING_DIFF", "SENDING_SLICES", 
                           "QUERYING_RESULT", "REBOOTING"}
        return status in running_statuses


class FirmwareValidator:
    """固件验证工具类"""
    
    @staticmethod
    def validate_firmware_file(file_path: str) -> Tuple[bool, str]:
        """验证固件文件
        
        Args:
            file_path: 固件文件路径
            
        Returns:
            (是否有效, 错误消息)
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                return False, f"固件文件不存在: {file_path}"

            # 检查文件大小
            file_size = os.path.getsize(file_path)
            if file_size == 0:
                return False, "固件文件为空"

            # 检查文件大小限制（最大50MB）
            max_size = 50 * 1024 * 1024  # 50MB
            if file_size > max_size:
                return False, f"固件文件过大: {file_size} bytes，最大允许: {max_size} bytes"

            # 检查文件扩展名
            allowed_extensions = [".bin", ".hex", ".fw", ".img"]
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext not in allowed_extensions:
                return False, f"不支持的固件文件格式: {file_ext}"

            # 检查文件是否可读
            try:
                with open(file_path, "rb") as f:
                    f.read(1024)  # 尝试读取前1KB
            except Exception as e:
                return False, f"无法读取固件文件: {e}"

            return True, "固件文件验证通过"

        except Exception as e:
            return False, f"固件文件验证失败: {e}"


class DeviceValidator:
    """设备验证工具类"""
    
    @staticmethod
    def validate_device_list(device_ids: List[str]) -> Tuple[List[str], List[str]]:
        """验证设备列表
        
        Args:
            device_ids: 设备ID列表
            
        Returns:
            (有效设备ID列表, 错误消息列表)
        """
        from models.device import Device  # 避免循环导入
        
        valid_devices = []
        error_messages = []

        if not device_ids:
            error_messages.append("设备ID列表不能为空")
            return valid_devices, error_messages

        for device_id in device_ids:
            try:
                device = Device.query.filter_by(device_id=device_id).first()
                if not device:
                    error_messages.append(f"设备 {device_id} 不存在")
                    continue

                # 检查设备状态（可以根据需要添加更多检查）
                valid_devices.append(device_id)

            except Exception as e:
                error_messages.append(f"验证设备 {device_id} 失败: {e}")

        return valid_devices, error_messages
    
    @staticmethod
    def check_device_compatibility(device_id: int, firmware_version: str) -> Tuple[bool, str]:
        """检查设备与固件的兼容性
        
        Args:
            device_id: 设备ID
            firmware_version: 固件版本
            
        Returns:
            (是否兼容, 消息)
        """
        from models.device import Device  # 避免循环导入
        
        try:
            device = Device.query.get(device_id)
            if not device:
                return False, f"设备 {device_id} 不存在"

            # 检查设备当前版本
            current_version = device.firmware_version
            
            if current_version == '未知':
                return True, f"设备 {device_id} 当前版本未知，兼容"

            # 使用统一的版本比较方法
            comparison = VersionUtils.compare_versions(current_version, firmware_version)
            
            if comparison > 0:
                return False, f"设备 {device_id} 已是更新版本 {current_version}"
            else:
                return True, f"设备 {device_id} 兼容固件版本 {firmware_version}"

        except Exception as e:
            return False, f"检查设备兼容性失败: {e}"


class OtaTaskHelper:
    """OTA任务助手类"""
    
    @staticmethod
    def create_task_batch(device_ids: List[int], firmware_path: str, firmware_version: str) -> List[Dict]:
        """批量创建OTA任务数据
        
        Args:
            device_ids: 设备ID列表
            firmware_path: 固件路径
            firmware_version: 固件版本
            
        Returns:
            任务数据列表
        """
        tasks = []
        for device_id in device_ids:
            task_data = {
                "device_id": device_id,
                "firmware_path": firmware_path,
                "firmware_version": firmware_version,
                "status": "等待中",
                "progress": 0,
                "detailed_status": "等待中",
                "stage_info": "任务已创建，等待执行",
                "retry_count": 0,
                "max_retries": 3,
            }
            tasks.append(task_data)

        return tasks
    
    @staticmethod
    def calculate_task_statistics(tasks: List[Dict]) -> Dict[str, int]:
        """计算任务统计信息
        
        Args:
            tasks: 任务列表
            
        Returns:
            统计信息字典
        """
        stats = {
            "total": len(tasks),
            "success": 0,
            "failed": 0,
            "in_progress": 0,
            "waiting": 0
        }
        
        for task in tasks:
            status = task.get("status", "")
            if status == "成功":
                stats["success"] += 1
            elif status == "失败":
                stats["failed"] += 1
            elif status == "进行中":
                stats["in_progress"] += 1
            elif status == "等待中":
                stats["waiting"] += 1
                
        return stats
