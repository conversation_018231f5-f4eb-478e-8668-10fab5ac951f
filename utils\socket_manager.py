#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
SocketIO管理模块
用于管理WebSocket连接和事件
"""

from flask_socketio import SocketIO, emit
from flask import request
from flask_login import current_user
from utils.logger import LoggerManager
from datetime import datetime
import queue

# 获取日志记录器
logger = LoggerManager.get_logger()

# 创建SocketIO实例
socketio = None
# 维护一个全局队列
msg_queue = queue.Queue()
# 队列处理标志
queue_processing = False
# 后台任务启动标志
background_task_started = False


def flush_queue():
    global queue_processing
    if queue_processing:
        return
    
    queue_processing = True
    try:
        # 批量处理队列中的消息，减少循环次数
        messages = []
        while not msg_queue.empty():
            try:
                messages.append(msg_queue.get_nowait())
            except queue.Empty:
                break
        
        # 批量发送消息
        for msg in messages:
            try:
                socketio.emit(msg['event'], msg['data'])
                logger.debug(f"已发送消息: {msg['event']}")
            except Exception as e:
                logger.error(f"发送消息失败: {e}")
    finally:
        queue_processing = False


def handle_queue():
    """
    处理消息队列的后台任务
    使用自适应休眠时间，根据队列状态动态调整检查频率
    """
    last_queue_size = 0
    sleep_time = 1.0  # 初始休眠时间（秒）
    min_sleep = 0.1   # 最小休眠时间
    max_sleep = 5.0   # 最大休眠时间
    
    while True:
        current_queue_size = msg_queue.qsize()
        
        # 根据队列大小动态调整休眠时间
        if current_queue_size > last_queue_size:
            # 队列在增长，减少休眠时间以更快处理
            sleep_time = max(min_sleep, sleep_time * 0.8)
        elif current_queue_size == 0:
            # 队列为空，增加休眠时间以减少CPU使用
            sleep_time = min(max_sleep, sleep_time * 1.2)
        
        # 处理队列
        flush_queue()
        
        # 更新上次队列大小
        last_queue_size = current_queue_size
        
        # 使用socketio.sleep进行休眠，保持与SocketIO框架的兼容性
        socketio.sleep(sleep_time)


def init_socketio(app):
    """
    初始化SocketIO
    
    Args:
        app: Flask应用实例
        
    Returns:
        SocketIO实例
    """
    global socketio
    socketio = SocketIO(
        app,
        cors_allowed_origins="*",
        # async_mode='threading',  # 使用线程模式
        # ping_interval=25,  # 增加 ping 间隔
        # ping_timeout=10,  # 增加 ping 超时时间
        # logger=True,  # 启用日志
        # engineio_logger=True  # 启用 Engine.IO 日志
    )
    
    # 注册事件处理器
    register_handlers()
    
    return socketio

def register_handlers():
    """注册SocketIO事件处理器"""
    
    @socketio.on('connect')
    def handle_connect():
        """处理WebSocket连接"""
        global background_task_started
        try:
            if not current_user.is_authenticated:
                return False  # 拒绝未认证的连接

            # 使用自定义的会话管理
            socketio.server.save_session(request.sid, {'user_id': current_user.id})
            logger.info(f'Client connected: user_id={current_user.id}')

            # 只启动一次后台任务处理队列
            if not background_task_started:
                socketio.start_background_task(target=handle_queue)
                background_task_started = True
                logger.info("后台队列处理任务已启动")

            return True
        except Exception as e:
            logger.error(f"WebSocket连接异常: {e}")
            return False

    @socketio.on('disconnect')
    def handle_disconnect():
        """处理WebSocket断开连接"""
        try:
            session_data = socketio.server.get_session(request.sid)
            if session_data and 'user_id' in session_data:
                logger.info(f'Client disconnected: user_id={session_data["user_id"]}')
        except Exception as e:
            logger.error(f"WebSocket断开连接异常: {e}")

def emit_task_update(task_id, status, progress, message='', error_message=''):
    """
    发送任务状态更新

    Args:
        task_id: 任务ID
        status: 任务状态
        progress: 任务进度
        message: 状态消息
        error_message: 错误信息
    """
    try:
        if socketio is None:
            logger.warning("SocketIO未初始化，无法发送任务更新")
            return

        data = {
            'task_id': str(task_id),
            'status': status,
            'progress': int(progress) if progress is not None else 0,
            'message': message or '',
            'error_message': error_message or '',
            'timestamp': str(datetime.now())
        }

        # 立即发送消息（不使用队列）
        try:
            socketio.emit('ota_task_update', data, broadcast=True)
            logger.info(f"已发送任务状态更新: task_id={task_id}, status={status}, progress={progress}%")
        except Exception as emit_error:
            logger.error(f"直接发送消息失败，使用队列: {emit_error}")
            # 如果直接发送失败，则使用队列
            msg = {
                'event': 'ota_task_update',
                'data': data
            }
            msg_queue.put(msg)
            logger.info(f"已添加到队列: {data}")

    except Exception as e:
        logger.error(f"发送任务状态更新失败: {e}")


def emit_task_update_safe(task_id, status, progress, message=''):
    """
    安全的任务状态更新发送函数（兼容旧接口）
    """
    try:
        emit_task_update(task_id, status, progress, message)
    except Exception as e:
        logger.error(f"安全发送任务更新失败: {e}")

def run_socketio(app, host='localhost', port=5000, debug=False):
    """
    运行SocketIO应用
    
    Args:
        app: Flask应用实例
        host: 主机地址
        port: 端口号
        debug: 是否开启调试模式
    """
    socketio.run(app, host=host, port=port, debug=debug)