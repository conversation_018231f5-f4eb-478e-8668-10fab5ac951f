#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
WebSocket连接测试工具
用于诊断WebSocket连接和消息发送问题
"""

import time
import threading
from utils.logger import LoggerManager
from utils.socket_manager import socketio, emit_task_update, emit_task_update_safe

# 获取日志记录器
logger = LoggerManager.get_logger()


class WebSocketTester:
    """WebSocket测试器"""
    
    def __init__(self):
        self.test_running = False
        self.test_thread = None
    
    def start_test(self):
        """开始WebSocket测试"""
        if self.test_running:
            logger.warning("WebSocket测试已在运行中")
            return
        
        self.test_running = True
        self.test_thread = threading.Thread(target=self._run_test, daemon=True)
        self.test_thread.start()
        logger.info("WebSocket测试已启动")
    
    def stop_test(self):
        """停止WebSocket测试"""
        self.test_running = False
        if self.test_thread and self.test_thread.is_alive():
            self.test_thread.join(timeout=5)
        logger.info("WebSocket测试已停止")
    
    def _run_test(self):
        """运行测试"""
        test_task_id = 99999
        test_count = 0
        
        while self.test_running:
            try:
                test_count += 1
                
                # 测试不同的状态
                if test_count % 4 == 1:
                    status = "等待中"
                    progress = 0
                elif test_count % 4 == 2:
                    status = "进行中"
                    progress = 25
                elif test_count % 4 == 3:
                    status = "进行中"
                    progress = 75
                else:
                    status = "成功"
                    progress = 100
                
                message = f"测试消息 #{test_count}"
                
                # 发送测试消息
                emit_task_update_safe(
                    task_id=test_task_id,
                    status=status,
                    progress=progress,
                    message=message
                )
                
                logger.info(f"发送测试消息 #{test_count}: {status} {progress}%")
                
                # 等待5秒
                time.sleep(5)
                
            except Exception as e:
                logger.error(f"WebSocket测试异常: {e}")
                time.sleep(1)
    
    def send_single_test_message(self, task_id=88888):
        """发送单个测试消息"""
        try:
            emit_task_update_safe(
                task_id=task_id,
                status="进行中",
                progress=50,
                message="单次测试消息"
            )
            logger.info(f"已发送单次测试消息: task_id={task_id}")
            return True
        except Exception as e:
            logger.error(f"发送单次测试消息失败: {e}")
            return False
    
    def check_socketio_status(self):
        """检查SocketIO状态"""
        try:
            if socketio is None:
                logger.error("SocketIO未初始化")
                return False
            
            # 检查是否有连接的客户端
            try:
                # 尝试获取连接数（这个方法可能不存在于所有版本）
                if hasattr(socketio.server, 'manager'):
                    manager = socketio.server.manager
                    if hasattr(manager, 'get_namespaces'):
                        namespaces = manager.get_namespaces()
                        logger.info(f"活跃命名空间: {namespaces}")
                    
                    if hasattr(manager, 'rooms'):
                        rooms = manager.rooms
                        logger.info(f"活跃房间数: {len(rooms) if rooms else 0}")
                
                logger.info("SocketIO状态检查完成")
                return True
                
            except Exception as detail_error:
                logger.warning(f"无法获取详细SocketIO状态: {detail_error}")
                return True  # SocketIO存在，只是无法获取详细信息
                
        except Exception as e:
            logger.error(f"检查SocketIO状态失败: {e}")
            return False
    
    def test_direct_emit(self, task_id=77777):
        """测试直接发送消息"""
        try:
            if socketio is None:
                logger.error("SocketIO未初始化，无法测试直接发送")
                return False
            
            test_data = {
                'task_id': str(task_id),
                'status': '进行中',
                'progress': 30,
                'message': '直接发送测试',
                'timestamp': str(time.time())
            }
            
            # 直接使用socketio.emit发送
            socketio.emit('ota_task_update', test_data, broadcast=True)
            logger.info(f"直接发送测试消息成功: {test_data}")
            return True
            
        except Exception as e:
            logger.error(f"直接发送测试消息失败: {e}")
            return False


# 创建全局测试器实例
websocket_tester = WebSocketTester()


def start_websocket_test():
    """启动WebSocket测试"""
    websocket_tester.start_test()


def stop_websocket_test():
    """停止WebSocket测试"""
    websocket_tester.stop_test()


def send_test_message(task_id=None):
    """发送测试消息"""
    if task_id is None:
        task_id = int(time.time()) % 100000  # 使用时间戳生成测试ID
    return websocket_tester.send_single_test_message(task_id)


def check_websocket_status():
    """检查WebSocket状态"""
    return websocket_tester.check_socketio_status()


def test_direct_websocket_emit(task_id=None):
    """测试直接WebSocket发送"""
    if task_id is None:
        task_id = int(time.time()) % 100000
    return websocket_tester.test_direct_emit(task_id)


if __name__ == "__main__":
    # 如果直接运行此脚本，执行基本测试
    print("WebSocket测试工具")
    print("1. 检查SocketIO状态")
    check_websocket_status()
    
    print("2. 发送测试消息")
    send_test_message()
    
    print("3. 测试直接发送")
    test_direct_websocket_emit()
